'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
    async up(queryInterface, Sequelize) {
        await queryInterface.addColumn('user', 'averageScore', {
            type: Sequelize.DECIMAL(5, 2),
            allowNull: true,
            comment: 'Average score of the user'
        });
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.removeColumn('user', 'averageScore');
    }
};
