import { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toggleCloseSidebar } from "../../features/sidebar/sidebarSlice";
import { ArrowRight, ArrowLeft, <PERSON>Up, <PERSON>Down } from "lucide-react";

const HeaderSidebar = () => {
    const [isHovered, setIsHovered] = useState(false);
    const closeSidebar = useSelector(state => state.sidebar?.closeSidebar);
    const navigate = useNavigate();

    const handleClick = () => {
        navigate(-1)
        setIsHovered(false);
    };

    return (
        <div
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            className={`flex items-center justify-between p-3 bg-white border-b border-gray-200 ${closeSidebar ? "" : "w-full"} transition-all duration-300`}
        >
            <button
                onClick={handleClick}
                className="flex items-center p-3 hover:bg-gray-200 justify-center rounded-md"
            >
                <ArrowLeft className="w-4 h-4 text-[#090a0a]" />
            </button>
            {!closeSidebar && (
                <p
                    onClick={() => navigate("/")}
                    className="cursor-pointer p-0 font-bevietnam text-[#090a0a] text-center text-base font-bold transition-all duration-300 hover:text-[#f97316]"
                >
                    Toán thầy Bee
                </p>
            )}
        </div>
    );
};

export default HeaderSidebar;
