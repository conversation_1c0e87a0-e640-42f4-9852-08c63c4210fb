import express from 'express';
import asyncHandler from '../middlewares/asyncHandler.js';
import Roles from '../constants/Roles.js';
import { requireRoles } from '../middlewares/jwtMiddleware.js';
import * as LogAdminActivityController from '../controllers/LogAdminActivityController.js';

const router = express.Router();

// Route lấy tất cả hoạt động của admin có phân trang theo thời gian
// Query parameters:
// - search: tìm kiếm theo action, description, tên admin
// - limit: số lượng record trả về (mặc định 20)
// - cursor: timestamp để phân trang (ISO string), l<PERSON>y từ nextCursor của response trước
// - sortOrder: ASC hoặc DESC (mặc định DESC)
// 
// Cách sử dụng:
// - Lần đầu: GET /api/v1/admin-activities?limit=20
// - <PERSON><PERSON><PERSON> tiếp theo: GET /api/v1/admin-activities?limit=20&cursor=2025-08-09T10:30:00.000Z
router.get('/v1/admin-activities',
    requireRoles(Roles.JustHumanResourceManagement),
    asyncHandler(LogAdminActivityController.getAllAdminActivities)
);

export default router;
