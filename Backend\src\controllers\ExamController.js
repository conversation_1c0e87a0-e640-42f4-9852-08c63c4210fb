import db from "../models/index.js"
import UserType from "../constants/UserType.js"
import { uploadImage, cleanupUploadedFiles, duplicateFirebaseFile } from "../utils/imageUpload.js"
import { Op, or, where, fn, col, literal } from "sequelize";
import { uploadPdfToFirebase, deletePdfFromFirebase } from "../utils/pdfUpload.js"
import { sendUserNotification } from "../utils/notificationUtils.js"
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js"
import * as examService from "../services/exam.service.js"
import * as notificationService from "../services/notification.service.js"
import * as studentExamService from "../services/student.exam.service.js"
import * as logAdminActivity from '../services/admin.activity.log.service.js';
import ActionAdmin from '../constants/ActionAdmin.js';
import { updateAverageScore } from "../services/user.service.js";

export const getExam = async (req, res) => {
    const search = req.query.search || ''
    const page = parseInt(req.query.page, 10) || 1
    const limit = parseInt(req.query.limit, 10) || 10
    const sortOrder = req.query.sortOrder || 'DESC'

    const result = await examService.getExamsWithFilter({
        search,
        page,
        limit,
        sortOrder
    })

    return res.status(200).json({
        message: 'Danh sách đề',
        ...result,
    })
}

export const getNewestExam = async (req, res) => {
    try {
        const exam = await db.Exam.findAll({
            where: { public: true }, // 👈 đúng chỗ này
            order: [['createdAt', 'DESC']], // Sắp xếp theo ngày tạo mới nhất
            limit: 3
        });

        res.status(200).json({
            message: 'Danh sách đề mới nhất',
            data: exam
        });
    } catch (error) {
        console.error('Lỗi khi lấy đề mới:', error);
        res.status(500).json({ message: 'Lỗi server khi lấy đề mới nhất' });
    }
};


export const getExamPublic = async (req, res) => {
    try {
        const userId = req.user.id;
        const search = req.query.search || '';
        const page = Math.max(1, parseInt(req.query.page, 10) || 1);
        const limit = 10;
        const offset = (page - 1) * limit;

        const typeOfExam = req.query.typeOfExam || '';
        const chapter = req.query.chapter || '';
        const classValue = req.query.grade || '';
        const year = req.query.year || '';
        const view = req.query.view || 'all';
        const sort = req.query.sort || 'newest';
        const isClassroomExam = req.query.isClassroomExam ?? null;

        let whereClause = { public: true };

        if (search.trim()) {
            whereClause[Op.or] = [
                { name: { [Op.like]: `%${search.trim()}%` } },
                { description: { [Op.like]: `%${search.trim()}%` } }
            ];
        }

        if (typeOfExam.trim()) {
            whereClause.typeOfExam = { [Op.like]: `%${typeOfExam}%` };
        }

        if (chapter.trim()) {
            whereClause.chapter = { [Op.like]: `%${chapter}%` };
        }

        if (classValue.trim()) {
            whereClause.class = { [Op.like]: `%${classValue}%` };
        }

        if (year.trim()) {
            whereClause.year = { [Op.like]: `%${year}%` };
        }

        if (isClassroomExam !== null) {
            whereClause.isClassroomExam = isClassroomExam === 'true' ? true : false;
        }

        // Join điều kiện theo view
        let includeClause = [];
        if (view === "done" || view === "saved") {
            includeClause.push({
                model: db.StudentExamStatus,
                as: "statuses",
                where: {
                    studentId: userId,
                    ...(view === "done" ? { isDone: true } : {}),
                    ...(view === "saved" ? { isSave: true } : {})
                },
                required: true
            });
        } else {
            includeClause.push({
                model: db.StudentExamStatus,
                as: "statuses",
                where: {
                    studentId: userId
                },
                required: false
            });
        }

        const order =
            sort === "az"
                ? [["name", "ASC"]]
                : sort === "za"
                    ? [["name", "DESC"]]
                    : sort === "oldest"
                        ? [["createdAt", "ASC"]]
                        : [["createdAt", "DESC"]]; // mặc định: newest

        // Lấy danh sách đề
        const { count: total, rows: examList } = await db.Exam.findAndCountAll({
            where: whereClause,
            include: includeClause,
            offset,
            limit,
            order,
            distinct: true,             // ✅ BẮT BUỘC khi include hasMany
            col: "id"              // ✅ Đếm theo id của bảng chính
        });


        // Map để đếm số học sinh đã làm từng đề
        const enrichedExamList = await Promise.all(
            examList.map(async (exam) => {
                const studentCount = await db.StudentExamAttempt.count({
                    where: {
                        examId: exam.id,
                        endTime: { [Op.ne]: null }
                    },
                    distinct: true,
                    col: "studentId"
                });

                const { avgStar, starCount } = await studentExamService.getExamRatingStatistics({
                    examId: exam.id,
                    avgStar: true,
                    starCount: true,
                });

                return {
                    ...exam.toJSON(),
                    studentCount,
                    avgStar,
                    starCount

                };
            })
        );

        return res.status(200).json({
            message: "Danh sách đề thi",
            ...new ResponseDataPagination(enrichedExamList, {
                total,
                page,
                pageSize: limit,
                totalPages: Math.ceil(total / limit),
                sortOrder: sort
            })
        });

    } catch (err) {
        console.error(err);
        return res.status(500).json({ message: "Lỗi server" });
    }
};

// examHandlers.js
export const submitExam = async (socket, attemptId) => {
    const t = await db.sequelize.transaction(); // 🔒 Bắt đầu transaction
    try {
        console.log("📝 Nộp bài:", attemptId);
        const attempt = await db.StudentExamAttempt.findByPk(attemptId, { transaction: t });
        if (!attempt) {
            await t.rollback();
            return socket.emit("submit_error", { message: "Nộp bài thất bại, vui lòng thử lại." });
        }

        if (attempt.endTime) {
            await t.rollback();
            return socket.emit("exam_submitted", { message: "Bài thi đã được nộp trước đó." });
        }

        attempt.endTime = new Date();

        const status = await db.StudentExamStatus.findOne({
            where: { studentId: attempt.studentId, examId: attempt.examId },
            transaction: t
        });

        if (status) {
            status.isDone = true;
            await status.save({ transaction: t });
        } else {
            await db.StudentExamStatus.create({
                studentId: attempt.studentId,
                examId: attempt.examId,
                isDone: true
            }, { transaction: t });
        }

        // 👉 Lấy tất cả answer + typeOfQuestion từ Question
        const answers = await db.Answer.findAll({
            where: { attemptId },
            include: {
                model: db.Question,
                attributes: ['id', 'typeOfQuestion']
            },
            transaction: t
        });

        // 👉 Tính điểm
        let totalScore = 0;
        const scoreQuestion = await examService.calculateScoreQuestion(attempt.examId);
        for (const answer of answers) {
            const { typeOfQuestion } = answer.Question;
            const isCorrect = answer.result === true;

            if (typeOfQuestion === 'TN' && isCorrect) {
                totalScore += scoreQuestion.TN;
            } else if (typeOfQuestion === 'TLN' && isCorrect) {
                totalScore += scoreQuestion.TLN;
            } else if (typeOfQuestion === 'DS') {
                let count = 0;
                if (!answer.answerContent || answer.answerContent == []) continue;
                const answersDS = JSON.parse(answer.answerContent); // [{statementId, answer: true/false}]

                for (const answerDS of answersDS || []) {
                    const statement = await db.Statement.findByPk(answerDS.statementId);
                    if (statement && statement.isCorrect === answerDS.answer) {
                        count++;
                    }
                }

                // Tính điểm dựa vào số lượng đúng
                if (count === 1) totalScore += 0.1;
                else if (count === 2) totalScore += 0.25;
                else if (count === 3) totalScore += 0.5;
                else if (count >= 4) {
                    totalScore += 1.0;
                    // Nếu count = 4 và isCorrect === false thì cập nhật result của answer là true
                    if (count === 4 && answer.result === false) {
                        answer.result = true;
                        await answer.save({ transaction: t });
                    }
                }
            }
        }


        attempt.score = parseFloat(totalScore.toFixed(2));
        await attempt.save({ transaction: t });

        await updateAverageScore(attempt.studentId);

        await t.commit(); // Commit nếu mọi thứ ổn

        socket.emit("exam_submitted", {
            message: "Nộp bài thành công!",
            timestamp: new Date(),
            attemptId,
            score: attempt.score,
            answers: answers.map(a => ({
                id: a.id,
                questionId: a.questionId,
                answerContent: a.answerContent,
                result: a.result,
                typeOfQuestion: a.Question?.typeOfQuestion || null,
            }))
        });

    } catch (err) {
        await t.rollback(); // Rollback nếu có lỗi
        console.error("Lỗi submit_exam:", err);
        socket.emit("submit_error", { message: "Nộp bài thất bại, vui lòng thử lại." });
    }
};

export const findExams = async (req, res) => {
    const { search } = req.query;
    const limit = 20; // Giới hạn 20 kết quả

    // Nếu không có tham số search, trả về 20 user được cập nhật gần đây nhất
    if (!search || search.trim() === '') {
        const recentExams = await db.Exam.findAll({
            order: [['updatedAt', 'DESC']],
            limit,
            attributes: ['id', 'name', 'typeOfExam', 'class', 'year']
        });

        return res.status(200).json({
            message: 'Lấy danh sách đề gần đây thành công',
            data: recentExams
        });
    }

    // Tìm kiếm theo nhiều tiêu chí
    const searchTerm = search.trim();
    // console.log(searchTerm);
    // Kiểm tra xem searchTerm có phải là số không (để tìm theo ID)
    const isNumeric = !isNaN(searchTerm) && !isNaN(parseFloat(searchTerm));
    // console.log(isNumeric);
    // Tạo điều kiện tìm kiếm
    const whereCondition = {
        [Op.or]: [
            { name: { [Op.like]: `%${searchTerm}%` } },
            { typeOfExam: { [Op.like]: `%${searchTerm}%` } },
            { class: { [Op.like]: `%${searchTerm}%` } },
            { year: { [Op.like]: `%${searchTerm}%` } },
            { description: { [Op.like]: `%${searchTerm}%` } }
        ],
    };

    // console.log(whereCondition);

    // Thực hiện tìm kiếm
    const exams = await db.Exam.findAll({
        where: whereCondition,
        limit,
        attributes: ['id', 'name', 'typeOfExam', 'class', 'year'],
        order: [['updatedAt', 'DESC']]
    });

    return res.status(200).json({
        message: 'Tìm kiếm người dùng thành công',
        data: exams,
        totalItems: exams.length
    });
}

export const findPublicExams = async (req, res) => {
    const { search } = req.query;
    const limit = 5;

    const searchTerm = search?.trim();
    let exams = [];

    if (!searchTerm) {
        // Không có từ khóa → lấy mới nhất
        exams = await db.Exam.findAll({
            where: {
                public: true,
            },
            order: [['updatedAt', 'DESC']],
            limit,
            attributes: ['id', 'name', 'typeOfExam', 'class', 'year'],
        });
    } else if (!isNaN(searchTerm)) {
        // Nếu là số → tìm theo id
        exams = await db.Exam.findAll({
            where: {
                id: Number(searchTerm),
                public: true,
            },
            limit,
            attributes: ['id', 'name', 'typeOfExam', 'class', 'year'],
        });
    } else {
        // Tìm theo tên đề thi bằng LIKE
        exams = await db.Exam.findAll({
            where: {
                name: {
                    [db.Sequelize.Op.like]: `%${searchTerm}%`,
                },
                public: true,
            },
            order: [['updatedAt', 'DESC']],
            limit,
            attributes: ['id', 'name', 'typeOfExam', 'class', 'year'],
        });
    }

    return res.status(200).json({
        message: 'Lấy danh sách đề thành công',
        data: exams,
    });
};



export const getExamPublicById = async (req, res) => {
    const { id } = req.params;

    const examDetail = await db.Exam.findByPk(id);

    if (!examDetail || !examDetail.public) {
        return res.status(404).json({ message: 'Đề không công khai hoặc không tồn tại' });
    }

    return res.status(200).json({
        message: 'Chi tiết đề',
        data: {
            ...examDetail.toJSON(),
        }
    });
}



export const getQuestionByExamId = async (req, res) => {
    const { examId } = req.params;

    if (!examId) {
        return res.status(400).json({ message: "examId không hợp lệ!" });
    }

    const sortOrder = req.query.sortOrder || "DESC";
    const search = req.query.search || "";
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const offset = (page - 1) * limit;

    try {
        const exam = await db.Exam.findByPk(examId, {
            include: [
                {
                    model: db.Question,
                    as: "questions",
                    through: {
                        attributes: ["order"],
                    },
                    include: [
                        {
                            model: db.Statement,
                            as: "statements",
                            attributes: ["id", "content", "imageUrl", "isCorrect", "order"],
                        },
                    ],
                },
            ],
        });

        if (!exam) {
            return res.status(404).json({ message: "Không tìm thấy đề thi!" });
        }

        // Lọc câu hỏi
        let filteredQuestions = exam.questions;

        if (search.trim() !== "") {
            filteredQuestions = filteredQuestions.filter((question) =>
                [
                    question.content,
                    question.typeOfQuestion,
                    question.chapter,
                    question.difficulty,
                    question.class,
                    question.id?.toString(),
                    question.description,
                ]
                    .filter(Boolean)
                    .some((field) =>
                        field.toLowerCase().includes(search.toLowerCase())
                    )
            );
        }

        // Sắp xếp theo order trong bảng trung gian ExamQuestions
        filteredQuestions.sort((a, b) => {
            const orderA = a.ExamQuestions?.order || 0;
            const orderB = b.ExamQuestions?.order || 0;
            return sortOrder === "DESC" ? orderA - orderB : orderB - orderA;
        });

        // Sắp xếp mệnh đề bên trong từng câu hỏi
        filteredQuestions.forEach((question) => {
            if (Array.isArray(question.statements)) {
                question.statements.sort((a, b) => a.order - b.order);
            }
        });

        const total = filteredQuestions.length;
        const paginatedQuestions = filteredQuestions.slice(offset, offset + limit);

        return res.status(200).json({
            message: "Lấy danh sách câu hỏi thành công!",
            ... new ResponseDataPagination(paginatedQuestions, {
                total,
                page,
                pageSize: limit,
                totalPages: Math.ceil(total / limit),
                sortOrder,
            }),
            exam,
        });
    } catch (error) {
        console.error("Lỗi khi lấy câu hỏi theo examId:", error);
        return res.status(500).json({ message: "Lỗi server", error: error.message });
    }
};



export const getPublicQuestionByExamId = async (req, res) => {
    const { examId } = req.params;

    if (!examId) {
        return res.status(400).json({ message: "examId không hợp lệ!" });
    }

    const exam = await db.Exam.findOne({
        where: { id: examId, public: true },
        attributes: ['name', 'testDuration', 'class', 'solutionUrl', 'isCheatingCheckEnabled', 'attemptLimit', 'seeCorrectAnswer', 'acceptDoExam'],
        include: [
            {
                model: db.Question,
                as: "questions",
                through: { attributes: ["order"] },
                attributes: ["id", "content", "typeOfQuestion", "imageUrl"],
                include: [
                    {
                        model: db.Statement,
                        as: "statements",
                        attributes: ["id", "content", "imageUrl", "order"],
                    },
                ],
            },
        ],
    });

    if (!exam) {
        return res.status(404).json({ message: "Không tìm thấy đề thi công khai!" });
    }

    // Sắp xếp câu hỏi theo order trong bảng ExamQuestions
    exam.questions.sort((a, b) => {
        const orderA = a.ExamQuestions?.order || 0;
        const orderB = b.ExamQuestions?.order || 0;
        return orderA - orderB;
    });

    // Sắp xếp các mệnh đề trong từng câu hỏi theo order
    exam.questions.forEach((question) => {
        if (Array.isArray(question.statements)) {
            question.statements.sort((a, b) => a.order - b.order);
        }
    });

    return res.status(200).json({
        message: "Lấy danh sách câu hỏi rút gọn thành công!",
        questions: exam.questions,
    });

};



export const getExamById = async (req, res) => {
    const { id } = req.params
    const examDetail = await db.Exam.findByPk(id)
    if (!examDetail) {
        return res.status(404).json({ message: 'Đề không tồn tại' })
    }
    // console.log("examDetail", examDetail)
    return res.status(200).json({
        message: 'Chi tiết đề',
        data: examDetail
    })
}

export const postExam = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    const uploadedFiles = []
    let uploadPdf = null

    try {
        const { examData, questions } = JSON.parse(req.body.data)
        const examImage = req.files?.examImage?.[0]

        const chapters = await db.AllCode.findAll({
            where: {
                type: 'chapter',
            }
        });

        // console.log("examData", examData)
        // console.log("questions", questions)

        if (!examData || !Array.isArray(questions)) {
            return res.status(400).json({ message: "Dữ liệu đề hoặc câu hỏi không hợp lệ!" })
        }

        const examImageUrl = await uploadImage(examImage, 'examImage')
        if (examImageUrl) uploadedFiles.push(examImageUrl)

        let examFileUrl = null;
        if (req.files?.pdf && req.files.pdf.length > 0) {
            const pdfFile = { file: req.files.pdf[0] }

            try {
                const result = await uploadPdfToFirebase(pdfFile, 'examFileUrl');
                if (result?.file) {
                    examFileUrl = result;
                    uploadPdf = result.file;
                }
            } catch (uploadError) {
                throw new Error("Lỗi khi upload file PDF: " + uploadError.message);
            }
        }

        const newExam = await db.Exam.create(
            { ...examData, imageUrl: examImageUrl, fileUrl: uploadPdf },
            { transaction }
        )
        let createdQuestions = []

        for (let i1 = 0; i1 < questions.length; i1++) {
            const { questionData, statements, order } = questions[i1]
            const chapter = chapters.find(c => c.code === questionData.chapter) ? questionData.chapter : null
            const newQuestion = await db.Question.create(
                {
                    ...questionData,
                    correctAnswer: questionData.correctAnswer ? questionData.correctAnswer.trim().replace(',', '.') : null,
                    solution: questionData.solution || null,
                    imageUrl: questionData.imageUrl || null,
                    chapter,
                },
                { transaction }
            )
            if (newQuestion.imageUrl) {
                const newImageUrl = await duplicateFirebaseFile(questionData.imageUrl, `question-${newQuestion.id}.jpg`, 'questionImage')
                uploadedFiles.push(newImageUrl)
                await newQuestion.update({ imageUrl: newImageUrl }, { transaction })
            }
            await db.ExamQuestions.create(
                {
                    examId: newExam.id,
                    questionId: newQuestion.id,
                    order,
                },
                { transaction }
            )

            let createdStatements = []

            if (Array.isArray(statements) && statements.length) {
                for (let i2 = 0; i2 < statements.length; i2++) {
                    const statement = statements[i2]

                    const newStatement = await db.Statement.create(
                        {
                            ...statement,
                            questionId: newQuestion.id,
                            imageUrl: statement.imageUrl || null,
                        },
                        { transaction }
                    )
                    if (newStatement.imageUrl) {
                        const newStatementImageUrl = await duplicateFirebaseFile(statement.imageUrl, `statement-${newStatement.id}.jpg`, 'statementImage')
                        uploadedFiles.push(newStatementImageUrl)
                        await newStatement.update({ imageUrl: newStatementImageUrl }, { transaction })
                    }
                    createdStatements.push(newStatement)
                }
            }

            createdQuestions.push({
                question: newQuestion,
                statements: createdStatements
            })
        }

        await transaction.commit()

        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.CREATE_EXAM,
            newExam.id,
            `Tạo đề thi ${newExam.name} thành công `
        );

        return res.status(201).json({
            message: "Thêm đề thi thành công!",
            exam: newExam,
            questions: createdQuestions,
        })

    } catch (error) {
        console.error('Lỗi khi thêm đề thi:', error)
        await cleanupUploadedFiles(uploadedFiles)
        if (uploadPdf) {
            await deletePdfFromFirebase(uploadPdf)
        }
        await transaction.rollback()

        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}


export const putExam = async (req, res) => {
    const { id } = req.params
    const [updated] = await db.Exam.update(req.body, {
        where: { id }
    })

    if (!updated) {
        return res.status(404).json({ message: "Đề thi không tồn tại" })
    }

    const updatedExam = await db.Exam.findByPk(id)

    const adminId = req.user.id;
    await logAdminActivity.logAdminActivity(
        adminId,
        ActionAdmin.UPDATE_EXAM,
        id,
        `Cập nhật đề thi ${updatedExam.name} thành công `
    );

    return res.status(200).json({ message: "Cập nhật đề thi thành công", data: updatedExam })
}

export const putImageExam = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    const { id } = req.params
    try {
        const exam = await db.Exam.findByPk(id)
        if (!exam) {
            await transaction.rollback()
            return res.status(404).json({ message: "Đề thi không tồn tại" })
        }

        const oldImageUrl = exam.imageUrl
        const newImageFile = req.file
        let newImageUrl = null
        if (newImageFile) {
            newImageUrl = await uploadImage(newImageFile, 'examImage')
        }

        const [updated] = await db.Exam.update(
            { imageUrl: newImageUrl },
            { where: { id } }
        )

        if (!updated) {
            await cleanupUploadedFiles([newImageUrl])
            await transaction.rollback()
            return res.status(500).json({ message: "Lỗi khi cập nhật ảnh đề thi" })
        }

        if (oldImageUrl) {
            try {
                await cleanupUploadedFiles([oldImageUrl])
            } catch (error) {
                await cleanupUploadedFiles([newImageUrl])
                await transaction.rollback()
                return res.status(500).json({ message: "Lỗi khi xóa ảnh cũ" })
            }
        }

        await transaction.commit()

        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.UPDATE_EXAM,
            id,
            `Cập nhật ảnh đề thi ${exam.name} thành công `
        );

        return res.status(200).json({
            message: "Cập nhật ảnh đề thi thành công",
            oldImageUrl,
            newImageUrl
        })
    } catch (error) {
        console.error("Lỗi khi cập nhật ảnh đề thi:", error)
        await transaction.rollback()
        return res.status(500).json({ message: "Lỗi server", error: error.message })
    }
}

export const getSavedExams = async (req, res) => {
    const { id } = req.user

    const savedExams = await db.StudentExamStatus.findAll({
        where: { studentId: id, isSave: true },
        include: [
            {
                model: db.Exam,
                as: "exam",
                attributes: ["id", "name", "typeOfExam", "class", "imageUrl", "chapter", "testDuration", "public", "createdAt", "updatedAt"]
            }
        ]
    })

    return res.status(200).json({
        message: "Lấy danh sách đề thi đã lưu thành công",
        data: savedExams
    })
}

export const uploadSolutionPdf = async (req, res) => {
    const { id } = req.params
    const transaction = await db.sequelize.transaction()
    let uploadedFile
    const exam = await db.Exam.findByPk(id)
    if (!exam) {
        return res.status(404).json({ message: "Đề thi không tồn tại" })
    }

    try {
        if (exam.solutionPdfUrl) {
            await deletePdfFromFirebase(exam.solutionPdfUrl)
        }
        // console.log("exam.solutionPdfUrl", exam.solutionPdfUrl)
        if (req.file) {
            uploadedFile = await uploadPdfToFirebase(req, 'solutionPdf')
            await exam.update({ solutionPdfUrl: uploadedFile.file }, { transaction })
        } else {
            await exam.update({ solutionPdfUrl: null }, { transaction })
        }
        await transaction.commit()

        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.UPDATE_EXAM,
            id,
            `Cập nhật file PDF cho đề thi ${exam.name} thành công `
        );

        return res.status(200).json({
            message: "Cập nhật file PDF thành công!",
            data: exam.solutionPdfUrl
        })
    } catch (error) {
        console.error("Lỗi khi cập nhật file PDF:", error)
        await transaction.rollback()
        if (uploadedFile) {
            await deletePdfFromFirebase(uploadedFile.file)
        }
        return res.status(500).json({ message: "Lỗi khi cập nhật file PDF", error: error.message })
    }
}

export const uploadExamFile = async (req, res) => {
    const { id } = req.params
    const transaction = await db.sequelize.transaction()
    let uploadedFile
    const exam = await db.Exam.findByPk(id)
    if (!exam) {
        return res.status(404).json({
            message: "Đề thi không tồn tại"
        })
    }
    try {
        if (exam.fileUrl) {
            await deletePdfFromFirebase(exam.fileUrl)
        }
        if (req.file) {
            uploadedFile = await uploadPdfToFirebase(req, 'examFile')
            await exam.update({ fileUrl: uploadedFile.file }, { transaction })
        } else {
            await exam.update({ fileUrl: null }, { transaction })
        }
        await transaction.commit()

        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.UPDATE_EXAM,
            id,
            `Cập nhật file đề thi ${exam.name} thành công `
        );

        return res.status(200).json({
            message: "Cập nhật file đề thi thành công!",
            data: exam.fileUrl
        })
    }
    catch (error) {
        console.error("Lỗi khi cập nhật file đề thi:", error)
        await transaction.rollback()
        if (uploadedFile) {
            await deletePdfFromFirebase(uploadedFile.file)
        }
        return res.status(500).json({ message: "Lỗi khi cập nhật file đề thi", error: error.message })
    }
}


export const deleteExam = async (req, res) => {
    const { id } = req.params
    const deleted = await db.Exam.destroy({
        where: { id }
    })
    if (!deleted) {
        return res.status(404).json({ message: "Đề thi không tồn tại" })
    }

    const adminId = req.user.id;
    await logAdminActivity.logAdminActivity(
        adminId,
        ActionAdmin.DELETE_EXAM,
        id,
        `Xóa đề thi ${deleted.name} thành công `
    );

    return res.status(200).json({ message: "Xóa đề thi thành công" })
}

export const getRelatedExams = async (req, res) => {
    const { examId } = req.params;
    const limit = 5;

    // Find the source exam
    const sourceExam = await db.Exam.findByPk(examId);

    if (!sourceExam) {
        return res.status(404).json({
            message: "Không tìm thấy đề thi gốc"
        });
    }

    // Create query to find related exams
    const whereClause = {
        id: { [Op.ne]: examId },      // Không phải đề hiện tại
        public: true,                 // Chỉ lấy đề công khai
        [Op.or]: [
            { class: sourceExam.class },
            { typeOfExam: sourceExam.typeOfExam },
            ...(sourceExam.chapter ? [{ chapter: sourceExam.chapter }] : [])
        ]
    };

    // Add chapter condition if it exists
    if (sourceExam.chapter) {
        whereClause[Op.or].push({ chapter: sourceExam.chapter });
    }

    // Find related exams
    // Find related exams
    let relatedExams = await db.Exam.findAll({
        where: whereClause,
        limit: limit,
        attributes: ['id', 'name', 'year', 'testDuration'],
        order: [['createdAt', 'DESC']]
    });

    const relatedIds = relatedExams.map(e => e.id);

    // Nếu chưa đủ -> lấy thêm đề mới nhất để bù
    if (relatedExams.length < limit) {
        const remaining = limit - relatedExams.length;

        const additionalExams = await db.Exam.findAll({
            where: {
                id: { [Op.notIn]: [examId, ...relatedIds] },
                public: true
            },
            attributes: ['id', 'name', 'year', 'testDuration'],
            order: [['createdAt', 'DESC']],
            limit: remaining
        });

        relatedExams = [...relatedExams, ...additionalExams];
    }

    // If user is logged in, get their status for these exams
    const userId = req.user?.id;
    let examStatusMap = {};

    if (userId && relatedExams.length > 0) {
        const examIds = relatedExams.map(exam => exam.id);

        const statuses = await db.StudentExamStatus.findAll({
            where: {
                studentId: userId,
                examId: { [Op.in]: examIds }
            }
        });

        statuses.forEach(status => {
            examStatusMap[status.examId] = {
                isDone: status.isDone,
                isSave: status.isSave
            };
        });
    }

    // Add status to each exam if user is logged in
    const formattedExams = relatedExams.map(exam => {
        const examData = exam.toJSON();
        if (userId) {
            const status = examStatusMap[exam.id] || { isDone: false, isSave: false };
            return {
                ...examData,
                isDone: status.isDone,
                isSave: status.isSave
            };
        }
        return examData;
    });

    return res.status(200).json({
        message: "Danh sách đề thi liên quan",
        data: formattedExams,
        total: formattedExams.length
    });
}

// API endpoint for exam submission
export const submitExamAPI = async (req, res) => {
    const transaction = await db.sequelize.transaction();
    try {
        const { attemptId } = req.body;
        const userId = req.user.id; // Get user ID from JWT token

        console.log("📝 Nộp bài qua API:", attemptId, "bởi user:", userId);

        // Validate input
        if (!attemptId) {
            return res.status(400).json({ message: "Thiếu thông tin attemptId" });
        }

        // Find the attempt
        const attempt = await db.StudentExamAttempt.findByPk(attemptId, { transaction });

        if (!attempt) {
            await transaction.rollback();
            return res.status(404).json({ message: "Không tìm thấy lượt làm bài" });
        }

        // Verify that the attempt belongs to the current user
        if (attempt.studentId !== userId) {
            await transaction.rollback();
            return res.status(403).json({ message: "Bạn không có quyền nộp bài thi này" });
        }

        // Check if the exam has already been submitted
        if (attempt.endTime !== null && attempt.endTime !== undefined && attempt.endTime !== 'null' && attempt.endTime !== 'undefined') {
            if (attempt.endTime) {
                await transaction.rollback();
                console.log("Bài thi đã được nộp trước đó", attempt.endtime);
                return res.status(400).json({ message: "Bài thi đã được nộp trước đó" });
            }
        }

        // Set the end time
        attempt.endTime = new Date();

        // Update or create the exam status
        const status = await db.StudentExamStatus.findOne({
            where: { studentId: attempt.studentId, examId: attempt.examId },
            transaction
        });

        if (status) {
            status.isDone = true;
            await status.save({ transaction });
        } else {
            await db.StudentExamStatus.create({
                studentId: attempt.studentId,
                examId: attempt.examId,
                isDone: true
            }, { transaction });
        }

        // Get all answers with their question types
        const answers = await db.Answer.findAll({
            where: { attemptId },
            include: {
                model: db.Question,
                attributes: ['id', 'typeOfQuestion']
            },
            transaction
        });

        // Calculate the score
        let totalScore = 0;
        const scoreQuestion = await examService.calculateScoreQuestion(attempt.examId);

        for (const answer of answers) {
            const { typeOfQuestion } = answer.Question;
            const isCorrect = answer.result === true;

            if (typeOfQuestion === 'TN' && isCorrect) {
                totalScore += scoreQuestion.TN;
            } else if (typeOfQuestion === 'TLN' && isCorrect) {
                totalScore += scoreQuestion.TLN;
            } else if (typeOfQuestion === 'DS') {
                let count = 0;
                if (!answer.answerContent || answer.answerContent == []) continue;
                const answersDS = JSON.parse(answer.answerContent); // [{statementId, answer: true/false}]

                for (const answerDS of answersDS || []) {
                    const statement = await db.Statement.findByPk(answerDS.statementId);
                    if (statement && statement.isCorrect === answerDS.answer) {
                        count++;
                    }
                }

                // Tính điểm dựa vào số lượng đúng
                if (count === 1) {
                    totalScore += 0.1;
                    if (answer.result === true) {
                        answer.result = false;
                        await answer.save({ transaction });
                    }
                }
                else if (count === 2) {
                    totalScore += 0.25;
                    if (answer.result === true) {
                        answer.result = false;
                        await answer.save({ transaction });
                    }
                }
                else if (count === 3) {
                    totalScore += 0.5;
                    if (answer.result === true) {
                        answer.result = false;
                        await answer.save({ transaction });
                    }
                }
                else if (count >= 4) {
                    totalScore += 1.0;
                    // Nếu count = 4 và isCorrect === false thì cập nhật result của answer là true
                    if (count === 4 && answer.result === false) {
                        answer.result = true;
                        await answer.save({ transaction });
                    }
                }
            }
        }

        // Update the score
        attempt.score = parseFloat(totalScore.toFixed(2));
        await attempt.save({ transaction });

        // Commit the transaction
        await transaction.commit();
        await updateAverageScore(userId);
        // Lấy thông tin về đề thi
        const exam = await db.Exam.findByPk(attempt.examId);
        const examName = exam ? exam.name : "Bài thi";

        try {
            const statuses = await db.StudentStudyStatus.findAll({
                where: { studentId: userId, isDone: false },
                include: [
                    {
                        model: db.LearningItem,
                        as: 'learningItem', // đảm bảo đúng alias
                        where: { url: exam.id, typeOfLearningItem: 'BTVN' },
                        required: true
                    }
                ],
            });

            for (const status of statuses) {
                console.log("Cập nhật trạng thái học tập cho user:", userId, "với learningItemId:", status.learningItemId);
                status.isDone = true;
                await status.save();
            }
        } catch (error) {
            console.error("Lỗi khi cập nhật trạng thái học tập:", error);
            // Không ảnh hưởng đến kết quả trả về, chỉ log lỗi
        }

        await notificationService.createNotification({
            userId,
            title: "Nộp bài thành công",
            content: `Bạn đã nộp bài "${examName}" thành công`,
            type: "EXAM",
            relatedId: attempt.examId,
            relatedType: "EXAM",
            actionUrl: `/practice/exam/attempt/${attempt.id}/score`,
            isRead: false
        });

        // // Gửi thông báo đến người dùng
        // try {
        //     const io = req.app.get('io');
        //     if (io) {
        //         await sendUserNotification(
        //             io,
        //             userId,
        //             "Nộp bài thành công",
        //             `Bạn đã nộp bài "${examName}" thành công`,
        //             "EXAM",
        //             attempt.examId,
        //             "EXAM",
        //             `/practice/exam/attempt/${attempt.id}/score`
        //         );
        //     }
        // } catch (notificationError) {
        //     console.error("Lỗi khi gửi thông báo nộp bài:", notificationError);
        //     // Không ảnh hưởng đến kết quả trả về
        // }

        // Return the result
        return res.status(200).json({
            message: "Nộp bài thành công!",
            timestamp: new Date(),
            attemptId,
            score: attempt.score,
            answers: answers.map(a => ({
                id: a.id,
                questionId: a.questionId,
                answerContent: a.answerContent,
                result: a.result,
                typeOfQuestion: a.Question?.typeOfQuestion || null,
            }))
        });

    } catch (error) {
        // Rollback the transaction in case of error
        await transaction.rollback();
        console.error("Lỗi khi nộp bài thi:", error);
        return res.status(500).json({
            message: "Nộp bài thất bại, vui lòng thử lại",
            error: error.message
        });
    }
}

export const getTotalScoreExam = async (req, res) => {
    const { examId } = req.params;

    const totalScore = await examService.getTotalScoreExam(examId);
    return res.status(200).json({
        message: "Lấy tổng số điểm của đề thi thành công",
        data: totalScore
    });

}

export const createExam = async (req, res) => {
    const transaction = await db.sequelize.transaction();
    try {
        const { examData, questionIds } = req.body;

        const newExam = await db.Exam.create(examData, { transaction });
        if (questionIds && questionIds.length > 0) {
            for (let i = 0; i < questionIds.length; i++) {
                await db.ExamQuestions.create({
                    examId: newExam.id,
                    questionId: questionIds[i],
                    order: questionIds[i].order || i
                }, { transaction });
            }
        }

        await transaction.commit();

        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.CREATE_EXAM,
            newExam.id,
            `Tạo đề thi ${newExam.name} thành công `
        );


        return res.status(200).json({
            message: "Tạo đề thi thành công",
            data: newExam
        });

    } catch (error) {
        await transaction.rollback();
        console.error("Lỗi khi tạo đề thi:", error);
        return res.status(500).json({
            message: "Tạo đề thi thất bại",
            error: error.message
        });
    }
}
