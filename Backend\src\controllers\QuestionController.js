import { Op, literal } from "sequelize"
import db from "../models/index.js"
import { uploadImage, cleanupUploadedFiles } from "../utils/imageUpload.js"
import UserType from "../constants/UserType.js"
import * as questionService from "../services/question.service.js"
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, PageBreak, ImageRun } from 'docx'
import { sanitizeText, downloadImage, parseTextWithMath } from '../utils/latexToDocx.js'
import * as logAdminActivity from '../services/admin.activity.log.service.js';
import ActionAdmin from '../constants/ActionAdmin.js';

export const getQuestion = async (req, res, next) => {
    const sortOrder = req.query.sortOrder || 'DESC'
    const search = req.query.search || ''
    const page = parseInt(req.query.page, 10) || 1
    const limit = parseInt(req.query.limit, 10) || 10

    const result = await questionService.getQuestionsWithFilter({
        sortOrder,
        search,
        page,
        limit
    })

    return res.status(200).json({
        message: 'Lấy danh sách câu hỏi thành công',
        ...result,
    })
}

export const getQuestionById = async (req, res) => {
    const { id } = req.params

    const questionDetail = await db.Question.findByPk(id, {
        include: [
            {
                model: db.Statement,
                as: 'statements',
                attributes: ['id', 'content', 'isCorrect', 'imageUrl', 'difficulty'], // 📝 Chọn các trường cần thiết
            },
        ],
    })

    if (!questionDetail) {
        return res.status(404).json({ message: 'Câu hỏi không tồn tại' })
    }

    return res.status(200).json({
        message: 'Chi tiết câu hỏi kèm đáp án',
        data: questionDetail,
    })
}

export const findQuestions = async (req, res) => {
    const { search } = req.query;
    const limit = 5;

    const searchTerm = search?.trim();
    let questions = [];

    if (!searchTerm) {
        // Không có từ khóa => lấy mới nhất
        questions = await db.Question.findAll({
            limit,
            order: [['createdAt', 'DESC']],
        });
    } else if (!isNaN(searchTerm)) {
        // Nếu search là số => tìm theo id
        questions = await db.Question.findAll({
            where: {
                id: Number(searchTerm),
            },
            limit,
        });
    } else {
        // Tìm kiếm gần đúng bằng LIKE
        questions = await db.Question.findAll({
            where: {
                content: {
                    [db.Sequelize.Op.like]: `%${searchTerm}%`,
                },
            },
            order: [['createdAt', 'DESC']],
            limit,
        });
    }

    return res.status(200).json({
        message: 'Danh sách câu hỏi',
        data: questions,
    });
};


export const getQuestionByExamId = async (req, res) => {
    const { examId } = req.params

    if (!examId) {
        return res.status(400).json({ message: "examId không hợp lệ!" })
    }

    const exam = await db.Exam.findByPk(examId, {
        include: [
            {
                model: db.Question,
                as: 'questions',
                through: { attributes: [] },
                include: [
                    {
                        model: db.Statement,
                        as: 'statements',
                        attributes: ['id', 'content', 'imageUrl'],
                    },
                ],
                attributes: ['id', 'content', 'typeOfQuestion', 'imageUrl'],
            },
        ],
    })

    if (!exam) {
        return res.status(404).json({ message: "Không tìm thấy đề thi!" })
    }

    return res.status(200).json({
        message: "Lấy danh sách câu hỏi thành công!",
        data: exam.questions,
    })
}

export const postQuestion = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    const uploadedFiles = []

    try {
        const { questionData, statementOptions, examId } = JSON.parse(req.body.data)
        const questionImage = req.files?.questionImage?.[0]
        const solutionImage = req.files?.solutionImage?.[0]
        const statementImages = req.files?.statementImages || []

        if (!questionData) {
            return res.status(400).json({ message: "Dữ liệu câu hỏi không hợp lệ!" })
        }

        const questionImageUrl = await uploadImage(questionImage, 'questionImage')
        if (questionImageUrl) uploadedFiles.push(questionImageUrl)

        const solutionImageUrl = await uploadImage(solutionImage, 'solutionImage')
        if (solutionImageUrl) uploadedFiles.push(solutionImageUrl)

        const newQuestion = await db.Question.create(
            {
                ...questionData,
                imageUrl: questionImageUrl,
                solutionImageUrl: solutionImageUrl
            },
            { transaction }
        )

        let statements = []
        let imageIndex = 0

        if (Array.isArray(statementOptions) && statementOptions.length) {
            statements = await Promise.all(
                statementOptions.map(async (statement, index) => {
                    let statementImageUrl = null

                    if (statement.needImage && statementImages[imageIndex]) {
                        statementImageUrl = await uploadImage(statementImages[imageIndex], 'statementImage')
                        if (statementImageUrl) uploadedFiles.push(statementImageUrl)
                        imageIndex++
                    }

                    return db.Statement.create(
                        {
                            ...statement,
                            questionId: newQuestion.id,
                            imageUrl: statementImageUrl,
                            order: index + 1
                        },
                        { transaction }
                    )
                })
            )
        }

        if (examId) {
            const exam = await db.Exam.findByPk(examId, { transaction })
            if (!exam) {
                await transaction.rollback()
                await cleanupUploadedFiles(uploadedFiles)
                return res.status(404).json({ message: "Đề thi không tồn tại!" })
            }

            const added = await db.ExamQuestions.create(
                { examId, questionId: newQuestion.id },
                { transaction }
            )

            if (!added) {
                await transaction.rollback()
                await cleanupUploadedFiles(uploadedFiles)
                return res.status(500).json({ message: "Lỗi khi thêm câu hỏi vào đề thi!" })
            }
        }

        await transaction.commit()
        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.CREATE_QUESTION,
            newQuestion.id,
            `Tạo câu hỏi ${newQuestion.content} vào đề thi ${examId} thành công `
        );
        return res.status(201).json({
            message: "Thêm câu hỏi thành công!",
            question: newQuestion,
            statements,
        })

    } catch (error) {
        await transaction.rollback()
        await cleanupUploadedFiles(uploadedFiles)

        console.error("Lỗi khi thêm câu hỏi:", error)
        return res.status(500).json({ message: "Lỗi server", error: error.message })
    }
}

export const putQuestion = async (req, res) => {
    const transaction = await db.sequelize.transaction();
    try {
        const { id } = req.params;
        const { questionData, statements } = req.body;

        // Kiểm tra xem câu hỏi có tồn tại không
        const existingQuestion = await db.Question.findByPk(id, { transaction });

        if (!existingQuestion) {
            await transaction.rollback();
            return res.status(404).json({ message: "Câu hỏi không tồn tại!" });
        }

        // Lọc bỏ các trường không được cập nhật
        const allowedFields = [
            "content",
            "difficulty",
            "chapter",
            "class",
            "description",
            "correctAnswer",
            "solution",
            "solutionUrl",
        ];

        const updateData = {};
        allowedFields.forEach((field) => {
            if (questionData[field] !== undefined) {
                updateData[field] = questionData[field];
            }
        });

        // Cập nhật câu hỏi (Không cập nhật imageUrl, solutionUrl)
        const [updated] = await db.Question.update(updateData, {
            where: { id },
            transaction,
        });

        if (!updated) {
            await transaction.rollback();
            return res.status(500).json({ message: "Lỗi khi cập nhật câu hỏi!" });
        }

        // Cập nhật danh sách mệnh đề (Không cập nhật imageUrl của statement)
        if (Array.isArray(statements) && statements.length > 0) {
            await Promise.all(
                statements.map(async (statement) => {
                    const { id: statementId, content, isCorrect, difficulty } = statement;

                    if (!statementId) return;

                    // Chỉ cập nhật các trường được phép
                    const statementUpdateData = {};
                    if (content !== undefined) statementUpdateData.content = content;
                    if (isCorrect !== undefined) statementUpdateData.isCorrect = isCorrect;
                    if (difficulty !== undefined) statementUpdateData.difficulty = difficulty;

                    await db.Statement.update(statementUpdateData, {
                        where: { id: statementId, questionId: id },
                        transaction,
                    });
                })
            );
        }

        // Commit transaction nếu mọi thứ thành công
        await transaction.commit();

        // Trả về dữ liệu cập nhật mới
        const updatedQuestion = await db.Question.findByPk(id, {
            include: [{ model: db.Statement, as: "statements" }],
        });

        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.UPDATE_QUESTION,
            id,
            `Cập nhật câu hỏi ${updatedQuestion.content} thành công `
        );

        return res.status(200).json({
            message: "Cập nhật câu hỏi và mệnh đề thành công!",
            data: updatedQuestion,
        });
    } catch (error) {
        await transaction.rollback();
        console.error("Lỗi khi cập nhật câu hỏi:", error);
        return res.status(500).json({ message: "Lỗi server!", error: error.message });
    }
};


export const putQuestionImage = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    const { id } = req.params

    try {
        const question = await db.Question.findByPk(id, { transaction })

        if (!question) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Câu hỏi không tồn tại.' })
        }

        const oldImageUrl = question.imageUrl
        const newImageFile = req.file
        let newImageUrl = null
        if (newImageFile) {
            newImageUrl = await uploadImage(newImageFile, 'questionImage')
        }

        const [updated] = await db.Question.update(
            { imageUrl: newImageUrl },
            { where: { id }, transaction }
        )

        if (!updated) {
            await cleanupUploadedFiles([newImageUrl])
            await transaction.rollback()
            return res.status(500).json({ message: 'Lỗi khi cập nhật ảnh câu hỏi.' })
        }

        if (oldImageUrl) {
            try {
                await cleanupUploadedFiles([oldImageUrl])
                console.log(`Đã xóa ảnh cũ: ${oldImageUrl}`)
            } catch (err) {
                console.error(`Lỗi khi xóa ảnh cũ: ${oldImageUrl}`, err)
                await cleanupUploadedFiles([newImageUrl])
                await transaction.rollback()
                return res.status(500).json({ message: 'Lỗi khi xóa ảnh cũ.', error: err.message })
            }
        }

        await transaction.commit()
        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.UPDATE_QUESTION,
            id,
            `Cập nhật ảnh câu hỏi ${question.content} thành công `
        );
        return res.status(200).json({
            message: 'Cập nhật ảnh câu hỏi thành công.',
            oldImageUrl,
            newImageUrl,
        })

    } catch (error) {
        console.error('Lỗi khi cập nhật ảnh câu hỏi:', error)
        await transaction.rollback()
        return res.status(500).json({ message: 'Lỗi server.', error: error.message })
    }
}

export const putQuestionSolutionImage = async (req, res) => {
    const transaction = await db.sequelize.transaction()
    const { id } = req.params

    try {
        const question = await db.Question.findByPk(id, { transaction })

        if (!question) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Câu hỏi không tồn tại.' })
        }

        const oldImageUrl = question.solutionImageUrl
        const newImageFile = req.file
        let newImageUrl = null
        if (newImageFile) {
            newImageUrl = await uploadImage(newImageFile, 'solutionImage')
        }

        const [updated] = await db.Question.update(
            { solutionImageUrl: newImageUrl },
            { where: { id }, transaction }
        )

        if (!updated) {
            await cleanupUploadedFiles([newImageUrl])
            await transaction.rollback()
            return res.status(500).json({ message: 'Lỗi khi cập nhật ảnh câu hỏi.' })
        }

        if (oldImageUrl) {
            try {
                await cleanupUploadedFiles([oldImageUrl])
                console.log(`Đã xóa ảnh cũ: ${oldImageUrl}`)
            } catch (err) {
                console.error(`Lỗi khi xóa ảnh cũ: ${oldImageUrl}`, err)
                await cleanupUploadedFiles([newImageUrl])
                await transaction.rollback()
                return res.status(500).json({ message: 'Lỗi khi xóa ảnh cũ.', error: err.message })
            }
        }

        await transaction.commit()
        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.UPDATE_QUESTION,
            id,
            `Cập nhật ảnh câu hỏi lời giải ${question.content} thành công `
        );
        return res.status(200).json({
            message: 'Cập nhật ảnh câu hỏi lời giải thành công.',
            oldImageUrl,
            newImageUrl,
        })

    } catch (error) {
        console.error('Lỗi khi cập nhật ảnh câu hỏi:', error)
        await transaction.rollback()
        return res.status(500).json({ message: 'Lỗi server.', error: error.message })
    }
}

export const putQuestionsExam = async (req, res) => {
    const { questions } = req.body;
    const { examId } = req.params;
    const transaction = await db.sequelize.transaction();
    const adminId = req.user.id;
    const code = await db.AllCode.findAll({
        where: {
            type: 'chapter',
        }
    });

    try {
        const exam = await db.Exam.findByPk(examId, { transaction });
        if (!exam) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Đề thi không tồn tại.' });
        }

        // 1. Xoá câu hỏi không còn trong danh sách
        const questionsIdsExam = await db.ExamQuestions.findAll({
            where: { examId },
            attributes: ['questionId'],
            transaction
        });
        const currentIds = questionsIdsExam.map(q => q.questionId);
        const incomingIds = questions.filter(q => !q.isNewQuestion).map(q => q.id);
        const toDeleteIds = currentIds.filter(id => !incomingIds.includes(id));

        if (toDeleteIds.length > 0) {
            await db.ExamQuestions.destroy({ where: { examId, questionId: toDeleteIds }, transaction });
            // Nếu cần: await db.Question.destroy({ where: { id: toDeleteIds }, transaction });
            await logAdminActivity.logAdminActivity(
                adminId,
                ActionAdmin.DELETE_QUESTION,
                null,
                `Xóa câu hỏi ${toDeleteIds.join(', ')} khỏi đề thi ${exam.name} thành công `
            );
        }

        // 2. Tạo và cập nhật các câu hỏi
        for (let i = 0; i < questions.length; i++) {
            const q = questions[i];
            const chapter = code.find(c => c.code === q.chapter) ? q.chapter : null
            const baseData = {
                ...q,
                correctAnswer: q.correctAnswer ? q.correctAnswer.trim().replace(',', '.') : null,
                solution: q.solution || null,
                chapter: chapter
            };
            let newQuestion = null

            if (q.isNewQuestion) {
                const { id, ...qData } = baseData;
                newQuestion = await db.Question.create(qData, { transaction });
                await db.ExamQuestions.create({
                    examId,
                    questionId: newQuestion.id,
                    order: q.ExamQuestions?.order || i
                }, { transaction });
                await logAdminActivity.logAdminActivity(
                    adminId,
                    ActionAdmin.CREATE_QUESTION,
                    newQuestion.id,
                    `Tạo câu hỏi ${newQuestion.id} vào đề thi ${exam.name} thành công `
                );
            } else {
                await db.Question.update(baseData, { where: { id: q.id }, transaction });
                await db.ExamQuestions.update(
                    { order: q.ExamQuestions?.order || i },
                    { where: { questionId: q.id, examId }, transaction }
                );
            }

            // 3. Xử lý statements nếu có
            if (q.typeOfQuestion === "TN" || q.typeOfQuestion === "DS") {
                const existingStatements = await db.Statement.findAll({
                    where: { questionId: q.id },
                    attributes: ['id'],
                    transaction
                });
                const existingIds = existingStatements.map(s => s.id);
                const incomingStatements = Array.isArray(q.statements) ? q.statements : [];
                const incomingIds = incomingStatements.filter(s => !s.isNewStatement).map(s => s.id);
                const toDeleteIds = existingIds.filter(id => !incomingIds.includes(id));

                // Xoá các statements không còn
                if (toDeleteIds.length > 0) {
                    await db.Statement.destroy({
                        where: { id: toDeleteIds, questionId: q.id },
                        transaction
                    });
                }

                // Tạo/cập nhật lại các statements hiện có
                for (let j = 0; j < incomingStatements.length; j++) {
                    const s = incomingStatements[j];
                    const data = {
                        ...s,
                        questionId: newQuestion ? newQuestion.id : q.id,
                        order: s.order || j
                    };

                    if (s.isNewStatement) {
                        await db.Statement.create(data, { transaction });
                    } else {
                        await db.Statement.update(data, {
                            where: { id: s.id, questionId: q.id },
                            transaction
                        });
                    }
                }
            }
        }

        await transaction.commit();
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.UPDATE_QUESTION,
            null,
            `Cập nhật câu hỏi cho đề thi ${exam.name} thành công `
        );
        return res.status(200).json({ message: 'Cập nhật câu hỏi thành công.' });

    } catch (error) {
        await transaction.rollback();
        console.error('Lỗi khi cập nhật câu hỏi:', error);
        return res.status(500).json({ message: 'Lỗi server.', error: error.message });
    }
};





export const deleteQuestion = async (req, res) => {
    const { id } = req.params
    const transaction = await db.sequelize.transaction()

    try {
        // Tìm câu hỏi và các statement liên quan
        const question = await db.Question.findByPk(id, {
            include: [
                {
                    model: db.Statement,
                    as: 'statements',
                    attributes: ['id', 'imageUrl']
                }
            ],
            transaction
        })

        if (!question) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Câu hỏi không tồn tại' })
        }

        // Thu thập tất cả các URL ảnh cần xóa
        const imagesToDelete = []

        // Thêm ảnh câu hỏi nếu có
        if (question.imageUrl) {
            imagesToDelete.push(question.imageUrl)
        }

        // Thêm ảnh lời giải nếu có
        if (question.solutionImageUrl) {
            imagesToDelete.push(question.solutionImageUrl)
        }

        // Thêm ảnh của các statement nếu có
        if (question.statements && question.statements.length > 0) {
            question.statements.forEach(statement => {
                if (statement.imageUrl) {
                    imagesToDelete.push(statement.imageUrl)
                }
            })
        }

        // Xóa câu hỏi (cascade sẽ tự động xóa statements)
        const deleted = await db.Question.destroy({
            where: { id },
            transaction
        })

        if (!deleted) {
            await transaction.rollback()
            return res.status(500).json({ message: 'Lỗi khi xóa câu hỏi' })
        }

        // Commit transaction trước khi xóa ảnh
        await transaction.commit()

        // Xóa tất cả ảnh liên quan
        if (imagesToDelete.length > 0) {
            try {
                await cleanupUploadedFiles(imagesToDelete)
                // console.log(`Đã xóa ${imagesToDelete.length} ảnh liên quan đến câu hỏi ${id}:`, imagesToDelete)
            } catch (err) {
                // console.error(`Lỗi khi xóa ảnh liên quan đến câu hỏi ${id}:`, err)
                // Không return error vì câu hỏi đã được xóa thành công
            }
        }
        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.DELETE_QUESTION,
            id,
            `Xóa câu hỏi ${question.content} thành công `
        );
        return res.status(200).json({
            message: 'Xóa câu hỏi thành công',
            deletedImages: imagesToDelete.length
        })

    } catch (error) {
        await transaction.rollback()
        console.error('Lỗi khi xóa câu hỏi:', error)
        return res.status(500).json({ message: 'Lỗi server', error: error.message })
    }
}

export const deleteQuestionImage = async (req, res) => {
    const { id } = req.params
    const transaction = await db.sequelize.transaction()
    try {
        const question = await db.Question.findByPk(id, { transaction })
        if (!question) {
            await transaction.rollback()
            return res.status(404).json({ message: 'Câu hỏi không tồn tại.' })
        }
        const oldImageUrl = question.imageUrl
        const [updated] = await db.Question.update(
            { imageUrl: null },
            { where: { id }, transaction }
        )
        if (!updated) {
            await transaction.rollback()
            return res.status(500).json({ message: 'Lỗi khi xóa ảnh câu hỏi.' })
        }

        if (oldImageUrl) {
            try {
                await cleanupUploadedFiles([oldImageUrl])
                console.log(`Đã xóa ảnh cũ: ${oldImageUrl}`)
            } catch (err) {
                console.error(`Lỗi khi xóa ảnh cũ: ${oldImageUrl}`, err)
                await transaction.rollback()
                return res.status(500).json({ message: 'Lỗi khi xóa ảnh cũ.', error: err.message })
            }
        }

        await transaction.commit()
        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.DELETE_QUESTION,
            id,
            `Xóa ảnh câu hỏi ${question.content} thành công `
        );
        return res.status(200).json({
            message: 'Xóa ảnh câu hỏi thành công.',
            oldImageUrl
        })
    } catch (error) {
        console.error('Lỗi khi xóa ảnh câu hỏi:', error)
        await transaction.rollback()
        return res.status(500).json({ message: 'Lỗi server.', error: error.message })
    }
}

export const exportQuestionsToDocx = async (req, res) => {
    try {
        const {
            examId,
        } = req.query;
        const {
            questionIds = []
        } = req.body;

        // Lấy dữ liệu questions
        let questions = [];

        if (!examId && questionIds.length === 0) {
            return res.status(400).json({ message: 'examId hoặc questionIds không hợp lệ!' });
        }
        if (examId) {
            // Lấy questions theo examId
            const exam = await db.Exam.findByPk(examId, {
                include: [
                    {
                        model: db.Question,
                        as: 'questions',
                        through: { attributes: ['order'] },
                        include: [
                            {
                                model: db.Statement,
                                as: 'statements',
                                attributes: ['id', 'content', 'isCorrect', 'imageUrl', 'order'],
                                order: [['order', 'ASC']]
                            }
                        ],
                    }
                ]
            });

            if (!exam) {
                return res.status(404).json({ message: 'Đề thi không tồn tại!' });
            }
            questions = exam.questions || [];
        } else if (questionIds.length > 0) {
            // console.log("questionIds", questionIds)
            const questionsData = await db.Question.findAll({
                where: { id: questionIds },
                include: [
                    {
                        model: db.Statement,
                        as: 'statements',
                        attributes: ['id', 'content', 'isCorrect', 'imageUrl', 'order'],
                        order: [['order', 'ASC']]
                    }
                ]
            });
            questions = [...questionsData];
        }

        if (!questions || questions.length === 0) {
            return res.status(404).json({ message: 'Không tìm thấy câu hỏi nào!' });
        }

        // Tạo DOCX document
        const doc = new Document({
            sections: [
                {
                    properties: {},
                    children: await createDocxContent(questions, examId)
                }
            ]
        });

        // Generate buffer
        const buffer = await Packer.toBuffer(doc);

        // Tạo tên file
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileName = examId
            ? `Exam-${examId}-Questions-${timestamp}.docx`
            : `Questions-Export-${timestamp}.docx`;

        // Set headers để download file
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
        res.setHeader('Content-Length', buffer.length);

        return res.send(buffer);

    } catch (error) {
        console.error('Lỗi khi xuất DOCX:', error);
        return res.status(500).json({
            message: 'Lỗi server khi xuất file DOCX',
            error: error.message
        });
    }
};

/**
 * Tạo content runs từ text có chứa LaTeX - Highlight công thức toán học
 * @param {string} text - Text chứa LaTeX
 * @param {number} fontSize - Font size
 * @param {boolean} bold - Bold text
 * @returns {Promise<Array>} - Array các TextRun objects
 */
const createContentRuns = async (text, fontSize = 22, bold = false) => {
    const parts = parseTextWithMath(text);
    const runs = [];

    for (const part of parts) {
        if (part.type === 'text') {
            if (part.content.trim()) {
                runs.push(new TextRun({
                    text: sanitizeText(part.content),
                    size: fontSize,
                    bold: bold
                }));
            }
        } else if (part.type === 'math') {
            // Highlight công thức toán học bằng background vàng
            runs.push(new TextRun({
                text: ` ${part.content} `,
                size: fontSize,
                bold: bold,
                highlight: "yellow", // Bôi vàng để người dùng dễ nhận biết
                font: "Consolas", // Font monospace để công thức dễ đọc hơn
                color: "000000" // Màu đen
            }));
        }
    }

    return runs.length > 0 ? runs : [new TextRun({ text: sanitizeText(text), size: fontSize, bold: bold })];
};

/**
 * Tạo image run từ URL với kích thước được tính toán tự động
 * @param {string} imageUrl - URL của hình ảnh
 * @param {number} maxWidth - Chiều rộng tối đa
 * @param {number} maxHeight - Chiều cao tối đa
 * @returns {Promise<ImageRun|null>} - ImageRun object hoặc null
 */
const createImageRun = async (imageUrl, maxWidth = 400, maxHeight = 300) => {
    try {
        const imageData = await downloadImage(imageUrl, maxWidth, maxHeight);
        if (!imageData) return null;

        return new ImageRun({
            data: imageData.buffer,
            transformation: {
                width: imageData.width,
                height: imageData.height,
            },
        });
    } catch (error) {
        console.warn('Error creating image run:', error.message);
        return null;
    }
};

/**
 * Tạo nội dung cho DOCX document
 * @param {Array} questions - Danh sách câu hỏi
 * @param {string} examId - ID của đề thi (nếu có)
 * @returns {Array} - Danh sách paragraphs cho DOCX
 */
const createDocxContent = async (questions, examId) => {
    const children = [];

    // Title
    const title = examId ? `ĐỀ THI SỐ ${examId}` : 'DANH SÁCH CÂU HỎI';
    children.push(
        new Paragraph({
            text: title,
            heading: HeadingLevel.TITLE,
            alignment: AlignmentType.CENTER,
            spacing: { after: 400 }
        })
    );

    // Metadata
    children.push(
        new Paragraph({
            children: [
                new TextRun({
                    text: `Tổng số câu hỏi: ${questions.length}`,
                    bold: true
                })
            ],
            spacing: { after: 200 }
        })
    );

    children.push(
        new Paragraph({
            children: [
                new TextRun({
                    text: `Ngày xuất: ${new Date().toLocaleDateString('vi-VN')}`,
                    bold: true
                })
            ],
            spacing: { after: 400 }
        })
    );

    children.push(
        new Paragraph({
            children: [
                new TextRun({
                    text: `Hướng dẫn: Để chuyển công thức toán học sang định dạng MathType (yêu cầu Word đã cài MathType), hãy làm như sau:\n` +
                        `- Bôi đen công thức toán học.\n` +
                        `- Nhấn tổ hợp phím Alt + '\\'.\n` +
                        `Lưu ý: Công thức toán học thường được đặt trong cụm ký hiệu như $...$ hoặc $$...$$ và đã được bôi vàng.`,
                    italics: true,
                    color: "666666"
                })
            ],
            spacing: { after: 400 }
        })
    );


    // Thêm line separator
    children.push(
        new Paragraph({
            text: "─".repeat(80),
            alignment: AlignmentType.CENTER,
            spacing: { after: 400 }
        })
    );

    // Duyệt qua từng câu hỏi
    for (let index = 0; index < questions.length; index++) {
        const question = questions[index];

        // Question number và metadata
        children.push(
            new Paragraph({
                children: [
                    new TextRun({
                        text: `Câu ${index + 1} (ID: ${question.id}):`,
                        bold: true,
                        size: 24
                    }),
                ],
                spacing: { before: 200, after: 100 }
            })
        );

        // Question content với LaTeX được convert thành equations
        const questionContentRuns = await createContentRuns(question.content, 22, false);
        children.push(
            new Paragraph({
                children: questionContentRuns,
            })
        );

        // Hình ảnh câu hỏi
        if (question.imageUrl) {
            const imageRun = await createImageRun(question.imageUrl, 500, 350);
            if (imageRun) {
                children.push(
                    new Paragraph({
                        children: [imageRun],
                        spacing: { after: 100 },
                        alignment: AlignmentType.CENTER
                    })
                );
            } else {
                // Fallback: hiển thị URL nếu không download được
                children.push(
                    new Paragraph({
                        children: [
                            new TextRun({
                                text: `[Hình ảnh: ${question.imageUrl}]`,
                                italics: true,
                                color: "666666"
                            })
                        ],
                        spacing: { after: 100 }
                    })
                );
            }
        }

        // Statements (đáp án) nếu có
        if (question.statements && question.statements.length > 0) {
            children.push(
                new Paragraph({
                    children: [
                        new TextRun({
                            text: "Các lựa chọn:",
                            bold: true
                        })
                    ],
                    spacing: { after: 50 }
                })
            );

            // Sắp xếp statements theo order
            const sortedStatements = question.statements.sort((a, b) => (a.order || 0) - (b.order || 0));

            for (let stmtIndex = 0; stmtIndex < sortedStatements.length; stmtIndex++) {
                const statement = sortedStatements[stmtIndex];
                const label = String.fromCharCode(65 + stmtIndex); // A, B, C, D...
                const isCorrect = statement.isCorrect ? " ✓" : "";

                // Statement content với equations
                const statementContentRuns = await createContentRuns(statement.content, 20, statement.isCorrect);
                const statementRuns = [
                    new TextRun({
                        text: `${label}. `,
                        size: 20,
                        bold: statement.isCorrect
                    }),
                    ...statementContentRuns,
                    new TextRun({
                        text: isCorrect,
                        size: 20,
                        bold: true,
                        color: "008000"
                    })
                ];

                children.push(
                    new Paragraph({
                        children: statementRuns,
                        spacing: { after: 50 }
                    })
                );

                // Statement image
                if (statement.imageUrl) {
                    const statementImageRun = await createImageRun(statement.imageUrl, 350, 250);
                    if (statementImageRun) {
                        children.push(
                            new Paragraph({
                                children: [statementImageRun],
                                spacing: { after: 50 },
                                alignment: AlignmentType.CENTER
                            })
                        );
                    } else {
                        // Fallback: hiển thị URL
                        children.push(
                            new Paragraph({
                                children: [
                                    new TextRun({
                                        text: `   [Hình ảnh: ${statement.imageUrl}]`,
                                        italics: true,
                                        color: "666666",
                                        size: 18
                                    })
                                ],
                                spacing: { after: 50 }
                            })
                        );
                    }
                }
            }
        }

        // Correct Answer
        if (question.correctAnswer) {
            const correctAnswerContentRuns = await createContentRuns(question.correctAnswer, 22, true);
            const correctAnswerRuns = [
                new TextRun({
                    text: "Đáp án đúng: ",
                    bold: true,
                    color: "008000"
                }),
                ...correctAnswerContentRuns
            ];

            children.push(
                new Paragraph({
                    children: correctAnswerRuns,
                    spacing: { after: 100 }
                })
            );
        }

        // Solution
        if (question.solution) {
            children.push(
                new Paragraph({
                    children: [
                        new TextRun({
                            text: "Lời giải:",
                            bold: true
                        })
                    ],
                    spacing: { after: 50 }
                })
            );

            const solutionRuns = await createContentRuns(question.solution, 20, false);
            children.push(
                new Paragraph({
                    children: solutionRuns,
                    spacing: { after: 100 }
                })
            );
        }

        // Solution image
        if (question.solutionImageUrl) {
            const solutionImageRun = await createImageRun(question.solutionImageUrl, 600, 450);
            if (solutionImageRun) {
                children.push(
                    new Paragraph({
                        children: [solutionImageRun],
                        spacing: { after: 100 },
                        alignment: AlignmentType.CENTER
                    })
                );
            } else {
                // Fallback: hiển thị URL
                children.push(
                    new Paragraph({
                        children: [
                            new TextRun({
                                text: `[Hình ảnh lời giải: ${question.solutionImageUrl}]`,
                                italics: true,
                                color: "666666"
                            })
                        ],
                        spacing: { after: 100 }
                    })
                );
            }
        }

        // Description nếu có
        if (question.description) {
            const descriptionContentRuns = await createContentRuns(question.description, 20, false);
            const descriptionRuns = [
                new TextRun({
                    text: "Ghi chú: ",
                    italics: true,
                    color: "666666"
                }),
                ...descriptionContentRuns
            ];

            children.push(
                new Paragraph({
                    children: descriptionRuns,
                    spacing: { after: 200 }
                })
            );
        }

        // Separator giữa các câu hỏi
        if (index < questions.length - 1) {
            children.push(
                new Paragraph({
                    text: "─".repeat(50),
                    alignment: AlignmentType.CENTER,
                    spacing: { before: 200, after: 200 }
                })
            );
        }
    }

    return children;
};

export const generateExam = async (req, res) => {
    try {
        const {
            classLevel = '12',
            chapters = [],
            tnCount = 12,
            dsCount = 4,
            tlnCount = 6
        } = req.body;

        const { allChapters, difficulties } = await questionService.getAllChaptersAndDifficulties();

        if (!allChapters.length || !difficulties.length) {
            return res.status(400).json({
                message: 'Không tìm thấy dữ liệu chapter hoặc difficulty trong hệ thống!'
            });
        }

        // Giai đoạn đầu: addSameChapter = false
        let addSameChapter = false;
        let filteredChapters = questionService.filterRelatedChapters(classLevel, chapters, allChapters, addSameChapter);
        console.log("filteredChapters", filteredChapters)
        if (!filteredChapters.length) {
            return res.status(400).json({
                message: chapters?.length
                    ? `Không tìm thấy chapter nào liên quan đến: ${chapters.join(', ')}!`
                    : `Không tìm thấy chapter nào cho lớp ${classLevel}!`
            });
        }

        const difficultyList = difficulties.map(d => d.code);
        const usedQuestionIds = [];

        // 1st try
        let tnQuestions = await questionService.getQuestionsForType('TN', tnCount, filteredChapters, difficultyList, usedQuestionIds);
        usedQuestionIds.push(...tnQuestions.map(q => q.id));

        let dsQuestions = await questionService.getQuestionsForType('DS', dsCount, filteredChapters, difficultyList, usedQuestionIds);
        usedQuestionIds.push(...dsQuestions.map(q => q.id));

        let tlnQuestions = await questionService.getQuestionsForType('TLN', tlnCount, filteredChapters, difficultyList, usedQuestionIds);
        usedQuestionIds.push(...tlnQuestions.map(q => q.id));
        // Nếu chưa đủ câu, thử lại với addSameChapter = true
        if (tnQuestions.length < tnCount || dsQuestions.length < dsCount || tlnQuestions.length < tlnCount) {
            if (!addSameChapter) {
                addSameChapter = true;
                filteredChapters = questionService.filterRelatedChapters(classLevel, chapters, allChapters, addSameChapter);

                if (tnQuestions.length < tnCount) {
                    const moreTN = await questionService.getQuestionsForType('TN', tnCount - tnQuestions.length, filteredChapters, difficultyList, usedQuestionIds);
                    usedQuestionIds.push(...moreTN.map(q => q.id));
                    tnQuestions = [...tnQuestions, ...moreTN];
                }

                if (dsQuestions.length < dsCount) {
                    const moreDS = await questionService.getQuestionsForType('DS', dsCount - dsQuestions.length, filteredChapters, difficultyList, usedQuestionIds);
                    usedQuestionIds.push(...moreDS.map(q => q.id));
                    dsQuestions = [...dsQuestions, ...moreDS];
                }

                if (tlnQuestions.length < tlnCount) {
                    const moreTLN = await questionService.getQuestionsForType('TLN', tlnCount - tlnQuestions.length, filteredChapters, difficultyList, usedQuestionIds);
                    usedQuestionIds.push(...moreTLN.map(q => q.id));
                    tlnQuestions = [...tlnQuestions, ...moreTLN];
                }
            }

            if (tnQuestions.length < tnCount || dsQuestions.length < dsCount || tlnQuestions.length < tlnCount) {
                return res.status(400).json({
                    message: 'Không đủ câu hỏi trong cơ sở dữ liệu để tạo đề!',
                    available: {
                        TN: tnQuestions.length,
                        DS: dsQuestions.length,
                        TLN: tlnQuestions.length
                    },
                    required: {
                        TN: tnCount,
                        DS: dsCount,
                        TLN: tlnCount
                    }
                });
            }
        }

        // Gộp câu hỏi và đánh số thứ tự
        const allQuestions = [
            ...tnQuestions.map((q, i) => ({ ...q.toJSON(), order: i + 1, questionType: 'TN' })),
            ...dsQuestions.map((q, i) => ({ ...q.toJSON(), order: tnCount + i + 1, questionType: 'DS' })),
            ...tlnQuestions.map((q, i) => ({ ...q.toJSON(), order: tnCount + dsCount + i + 1, questionType: 'TLN' }))
        ];

        // Phân tích
        const analysis = {
            chapterDistribution: {},
            difficultyDistribution: {},
            typeDistribution: {
                TN: tnQuestions.length,
                DS: dsQuestions.length,
                TLN: tlnQuestions.length
            },
            totalQuestions: allQuestions.length,
            classLevel
        };

        allQuestions.forEach(q => {
            analysis.chapterDistribution[q.chapter] = (analysis.chapterDistribution[q.chapter] || 0) + 1;
            analysis.difficultyDistribution[q.difficulty] = (analysis.difficultyDistribution[q.difficulty] || 0) + 1;
        });

        const chapterDetails = Object.fromEntries(allChapters.map(ch => [ch.code, ch.description]));
        const difficultyDetails = Object.fromEntries(difficulties.map(d => [d.code, d.description]));

        const adminId = req.user.id;
        await logAdminActivity.logAdminActivity(
            adminId,
            ActionAdmin.GENERATE_QUESTION,
            null,
            `Tạo đề thi tự động thành công với ${allQuestions.length} câu hỏi`
        );
        return res.status(200).json({
            message: 'Tạo danh sách câu hỏi đa dạng thành công!',
            analysis,
            chapterDetails,
            difficultyDetails,
            questions: allQuestions,
            metadata: {
                generatedAt: new Date().toISOString(),
                classLevel,
                selectedChapters: chapters,
                relatedChapters: filteredChapters.map(ch => ch.code),
                requestedCounts: { TN: tnCount, DS: dsCount, TLN: tlnCount },
                actualCounts: {
                    TN: tnQuestions.length,
                    DS: dsQuestions.length,
                    TLN: tlnQuestions.length
                }
            }
        });

    } catch (error) {
        console.error('Lỗi khi tạo danh sách câu hỏi:', error);
        return res.status(500).json({
            message: 'Lỗi server khi tạo danh sách câu hỏi tự động',
            error: error.message
        });
    }
};


/**
 * Lấy câu hỏi liên quan ngẫu nhiên dựa trên một câu hỏi cụ thể
 * 
 * Route: GET /v1/question/:questionId/related?limit=5
 * Route Admin: GET /v1/admin/question/:questionId/related?limit=5
 * 
 * Logic tìm câu hỏi liên quan theo mức độ ưu tiên:
 * 1. Cùng chương + cùng lớp + cùng loại + cùng độ khó (điểm cao nhất)
 * 2. Cùng chương + cùng lớp + cùng loại
 * 3. Cùng chương + cùng lớp
 * 4. Cùng nhóm chương (3 ký tự đầu)
 * 
 * Scoring system:
 * - Cùng chương chính xác: +10 điểm
 * - Cùng nhóm chương (3 ký tự đầu): +5 điểm  
 * - Cùng lớp: +5 điểm
 * - Cùng loại câu hỏi: +3 điểm
 * - Cùng độ khó: +2 điểm
 * 
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
export const getRelatedQuestions = async (req, res) => {
    try {
        const { questionId } = req.params;
        const { limit = 5 } = req.query;

        // Tìm câu hỏi gốc để lấy thông tin
        const originalQuestion = await db.Question.findByPk(questionId, {
            attributes: ['id', 'chapter', 'class', 'difficulty', 'typeOfQuestion']
        });

        if (!originalQuestion) {
            return res.status(404).json({
                message: 'Không tìm thấy câu hỏi!'
            });
        }

        // Xây dựng điều kiện tìm kiếm câu hỏi liên quan
        const whereConditions = {
            id: { [db.Sequelize.Op.ne]: questionId } // Loại trừ câu hỏi gốc
        };

        // Tìm câu hỏi cùng chương (ưu tiên cao nhất)
        if (originalQuestion.chapter) {
            // Lấy 3 ký tự đầu của chapter để tìm các chương liên quan
            const chapterPrefix = originalQuestion.chapter.substring(0, 3);

            whereConditions[db.Sequelize.Op.or] = [
                // Cùng chương chính xác
                { chapter: originalQuestion.chapter },
                // Cùng nhóm chương (3 ký tự đầu)
                { chapter: { [db.Sequelize.Op.like]: `${chapterPrefix}%` } }
            ];
        }

        // Ưu tiên câu hỏi cùng lớp và loại
        const priorityConditions = [];

        // Điều kiện 1: Cùng lớp, cùng loại câu hỏi, cùng độ khó
        if (originalQuestion.class && originalQuestion.typeOfQuestion && originalQuestion.difficulty) {
            priorityConditions.push({
                ...whereConditions,
                class: originalQuestion.class,
                typeOfQuestion: originalQuestion.typeOfQuestion,
                difficulty: originalQuestion.difficulty
            });
        }

        // Điều kiện 2: Cùng lớp, cùng loại câu hỏi
        if (originalQuestion.class && originalQuestion.typeOfQuestion) {
            priorityConditions.push({
                ...whereConditions,
                class: originalQuestion.class,
                typeOfQuestion: originalQuestion.typeOfQuestion
            });
        }

        // Điều kiện 3: Cùng lớp
        if (originalQuestion.class) {
            priorityConditions.push({
                ...whereConditions,
                class: originalQuestion.class
            });
        }

        // Điều kiện 4: Chỉ cùng chương/nhóm chương
        priorityConditions.push(whereConditions);

        let relatedQuestions = [];
        const requestedLimit = parseInt(limit);

        // Thử từng điều kiện theo mức độ ưu tiên
        for (const condition of priorityConditions) {
            if (relatedQuestions.length >= requestedLimit) break;

            const questions = await db.Question.findAll({
                where: condition,
                include: [
                    {
                        model: db.Statement,
                        as: 'statements',
                        attributes: ['id', 'content', 'isCorrect', 'imageUrl', 'order'],
                        order: [['order', 'ASC']]
                    }
                ],
                attributes: [
                    'id', 'content', 'typeOfQuestion', 'chapter', 'difficulty',
                    'class', 'description', 'correctAnswer', 'solution',
                    'imageUrl', 'solutionImageUrl', 'createdAt'
                ],
                order: db.sequelize.random(), // Random order
                limit: requestedLimit - relatedQuestions.length
            });

            // Thêm câu hỏi chưa có trong danh sách
            for (const q of questions) {
                if (!relatedQuestions.find(existing => existing.id === q.id)) {
                    relatedQuestions.push(q);
                    if (relatedQuestions.length >= requestedLimit) break;
                }
            }
        }

        // Tính toán độ liên quan cho mỗi câu hỏi
        const questionsWithRelevance = relatedQuestions.map(q => {
            let relevanceScore = 0;
            let relevanceFactors = [];

            // Cùng chương chính xác: +10 điểm
            if (q.chapter === originalQuestion.chapter) {
                relevanceScore += 10;
                relevanceFactors.push('Cùng chương');
            }
            // Cùng nhóm chương (3 ký tự đầu): +5 điểm
            else if (originalQuestion.chapter && q.chapter &&
                q.chapter.startsWith(originalQuestion.chapter.substring(0, 3))) {
                relevanceScore += 5;
                relevanceFactors.push('Cùng nhóm chương');
            }

            // Cùng lớp: +5 điểm
            if (q.class === originalQuestion.class) {
                relevanceScore += 5;
                relevanceFactors.push('Cùng lớp');
            }

            // Cùng loại câu hỏi: +3 điểm
            if (q.typeOfQuestion === originalQuestion.typeOfQuestion) {
                relevanceScore += 3;
                relevanceFactors.push('Cùng loại câu hỏi');
            }

            // Cùng độ khó: +2 điểm
            if (q.difficulty === originalQuestion.difficulty) {
                relevanceScore += 2;
                relevanceFactors.push('Cùng độ khó');
            }

            return {
                ...q.toJSON(),
                relevanceScore,
                relevanceFactors
            };
        });

        // Sắp xếp theo điểm số liên quan (cao nhất trước)
        questionsWithRelevance.sort((a, b) => b.relevanceScore - a.relevanceScore);

        return res.status(200).json({
            message: 'Lấy danh sách câu hỏi liên quan thành công!',
            data: {
                originalQuestion: {
                    id: originalQuestion.id,
                    chapter: originalQuestion.chapter,
                    class: originalQuestion.class,
                    difficulty: originalQuestion.difficulty,
                    typeOfQuestion: originalQuestion.typeOfQuestion
                },
                relatedQuestions: questionsWithRelevance,
                metadata: {
                    totalFound: questionsWithRelevance.length,
                    requestedLimit: requestedLimit,
                    generatedAt: new Date().toISOString()
                }
            }
        });

    } catch (error) {
        console.error('Lỗi khi lấy câu hỏi liên quan:', error);
        return res.status(500).json({
            message: 'Lỗi server khi lấy câu hỏi liên quan',
            error: error.message
        });
    }
};

/**
 * Gợi ý câu hỏi thay thế
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
export const suggestQuestionReplacements = async (req, res) => {
    try {
        const {
            questionIds = [],
            chapter,
            classLevel,
            difficulty,
            typeOfQuestion,
            search = '',
            limit = 10,
            page = 1
        } = req.body;
        // Xây dựng điều kiện tìm kiếm
        const whereConditions = {};

        // Loại trừ các câu hỏi đã có trong danh sách
        if (Array.isArray(questionIds) && questionIds.length > 0) {
            whereConditions.id = { [db.Sequelize.Op.notIn]: questionIds };
        }

        // Sử dụng thông tin từ request body
        const searchChapter = chapter;
        const searchClass = classLevel;
        const searchDifficulty = difficulty;
        const searchType = typeOfQuestion;

        if (searchChapter) {
            whereConditions.chapter = searchChapter;
        }

        if (searchClass) {
            whereConditions.class = searchClass;
        }

        if (searchDifficulty) {
            whereConditions.difficulty = searchDifficulty;
        }

        if (searchType) {
            whereConditions.typeOfQuestion = searchType;
        }

        // Thêm điều kiện search text nếu có
        if (search.trim()) {
            whereConditions[db.Sequelize.Op.or] = [
                { content: { [db.Sequelize.Op.like]: `%${search.trim()}%` } },
                { description: { [db.Sequelize.Op.like]: `%${search.trim()}%` } }
            ];
        }
        // Tính toán offset cho pagination
        const offset = (parseInt(page) - 1) * parseInt(limit);

        // Lấy danh sách câu hỏi gợi ý
        const { count, rows: suggestions } = await db.Question.findAndCountAll({
            where: whereConditions,
            include: [
                {
                    model: db.Statement,
                    as: 'statements',
                    attributes: ['id', 'content', 'isCorrect', 'imageUrl']
                }
            ],
            order: [['createdAt', 'DESC']], // Ưu tiên câu hỏi mới nhất
            limit: parseInt(limit),
            offset: offset
        });

        // Tính toán thông tin pagination
        const totalPages = Math.ceil(count / parseInt(limit));

        return res.status(200).json({
            message: 'Lấy gợi ý câu hỏi thay thế thành công!',
            data: {
                suggestions: suggestions,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages: totalPages,
                    totalItems: count,
                    itemsPerPage: parseInt(limit),
                    hasNextPage: parseInt(page) < totalPages,
                    hasPrevPage: parseInt(page) > 1
                },
                currentFilters: {
                    chapter: searchChapter,
                    classLevel: searchClass,
                    difficulty: searchDifficulty,
                    typeOfQuestion: searchType,
                    search: search
                }
            }
        });

    } catch (error) {
        console.error('Lỗi khi lấy gợi ý câu hỏi thay thế:', error);
        return res.status(500).json({
            message: 'Lỗi server khi lấy gợi ý câu hỏi thay thế',
            error: error.message
        });
    }
};

