import db from "../models/index.js"
import UserType from "../constants/UserType.js"
import { uploadImage, cleanupUploadedFiles } from "../utils/imageUpload.js"
import { Op, or, where } from "sequelize";
import { uploadPdfToFirebase, deletePdfFromFirebase } from "../utils/pdfUpload.js"
import { sendUserNotification } from "../utils/notificationUtils.js"
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js"

const Exam = db.Exam

export const getExamsWithFilter = async ({ sortOrder = 'DESC', search = '', page = 1, limit = 10 }) => {
    const offset = (page - 1) * limit

    let whereClause = {}
    if (search.trim() !== '') {
        whereClause = {
            [Op.or]: [
                { name: { [Op.like]: `%${search}%` } },
                { description: { [Op.like]: `%${search}%` } },
                { chapter: { [Op.like]: `%${search}%` } },
                { year: { [Op.like]: `%${search}%` } },
                { class: { [Op.like]: `%${search}%` } },
                { typeOfExam: { [Op.like]: `%${search}%` } }
            ]
        }
    }

    const [examList, total] = await Promise.all([
        db.Exam.findAll({
            where: whereClause,
            offset,
            limit,
            order: [['createdAt', sortOrder]]
        }),
        db.Exam.count({
            where: whereClause
        })
    ])

    return new ResponseDataPagination(examList, {
        total,
        page,
        pageSize: limit,
        totalPages: Math.ceil(total / limit),
        sortOrder,
    });
}

export const getQuestionInExam = async (examId) => {
    const exam = await db.Exam.findByPk(examId, {
        include: [
            {
                model: db.Question,
                as: "questions",
                through: { attributes: [] },
                attributes: ["id", "typeOfQuestion"],
            },
        ],
    });

    if (!exam) {
        throw new Error('Không tìm thấy đề thi.');
    }

    return exam;
}

export const calculateScoreQuestion = async (examID) => {
    const exam = await getQuestionInExam(examID);

    const score = {
        TN: 0.25,
        TLN: 0.5,
        DS: 1.0,
    }

    const questionCount = {
        TN: 0,
        TLN: 0,
        DS: 0,
    }

    for (const question of exam.questions) {
        questionCount[question.typeOfQuestion]++;
    }

    if (questionCount.TN === 12 && questionCount.TLN === 6 && questionCount.DS === 4) {
        return score;
    }

    if (questionCount.DS === 0 && questionCount.TLN === 0 && questionCount.TN > 0) {
        score.TN = 10 / questionCount.TN;
        score.TLN = 0;
        score.DS = 0;
    }

    return score;
}


export const getTotalScoreExam = async (examId) => {
    const exam = await getQuestionInExam(examId);
    let totalScore = 0;

    for (const question of exam.questions) {
        if (question.typeOfQuestion === 'TN') {
            totalScore += 0.25;
        } else if (question.typeOfQuestion === 'TLN') {
            totalScore += 0.5;
        } else if (question.typeOfQuestion === 'DS') {
            totalScore += 1.0;
        }
    }

    return 10;
}

export const reExaminationManyService = async (attemptIds) => {
    if (!Array.isArray(attemptIds) || attemptIds.length === 0) {
        throw { status: 400, message: 'Danh sách attemptId không hợp lệ!' };
    }

    const transaction = await db.sequelize.transaction();

    try {
        const attempts = await db.StudentExamAttempt.findAll({
            where: { id: attemptIds },
            transaction,
        });

        if (attempts.length === 0) {
            throw { status: 404, message: 'Không tìm thấy lượt làm bài nào!' };
        }

        const answers = await db.Answer.findAll({
            where: { attemptId: attemptIds },
            include: [{ model: db.Question, attributes: ['id', 'typeOfQuestion', 'correctAnswer'] }],
            transaction,
        });

        // Lấy các statementId cần thiết
        const tnStatementIds = answers
            .filter(a => a.Question?.typeOfQuestion === 'TN')
            .map(a => a.answerContent);

        const dsStatementIds = answers
            .filter(a => a.Question?.typeOfQuestion === 'DS')
            .flatMap(a => {
                try {
                    const parsed = JSON.parse(a.answerContent);
                    return Array.isArray(parsed) ? parsed.map(ans => ans.statementId) : [];
                } catch {
                    console.warn('❗ JSON lỗi:', a.answerContent);
                    return [];
                }
            });

        const allStatementIds = [...new Set([...tnStatementIds, ...dsStatementIds])];

        const allStatements = await db.Statement.findAll({
            where: { id: allStatementIds },
            transaction,
        });

        const statementMap = Object.fromEntries(
            allStatements.map(s => [s.id.toString(), s.isCorrect])
        );

        // Group answer theo attemptId
        const answersByAttempt = {};
        for (const answer of answers) {
            const id = answer.attemptId;
            if (!answersByAttempt[id]) answersByAttempt[id] = [];
            answersByAttempt[id].push(answer);
        }

        const results = [];

        for (const attempt of attempts) {
            const attemptAnswers = answersByAttempt[attempt.id] || [];
            let score = 0;
            let countUpdated = 0;

            for (const answer of attemptAnswers) {
                const question = answer.Question;

                if (question?.typeOfQuestion === 'TN') {
                    if (!answer.answerContent) continue;
                    const isCorrect = statementMap[answer.answerContent] === true;

                    if (answer.result !== isCorrect) {
                        await answer.update({ result: isCorrect }, { transaction });
                        countUpdated++;
                    }

                    if (isCorrect) score += 0.25;

                } else if (question?.typeOfQuestion === 'DS') {
                    if (!answer.answerContent) continue;
                    const ans = JSON.parse(answer.answerContent);
                    const correctCount = ans.filter(a => statementMap[a.statementId] === a.answer).length;

                    if (correctCount === 1) score += 0.1;
                    else if (correctCount === 2) score += 0.25;
                    else if (correctCount === 3) score += 0.5;
                    else if (correctCount >= 4) {
                        score += 1.0;
                        if (correctCount === 4 && answer.result === false) {
                            await answer.update({ result: true }, { transaction });
                            countUpdated++;
                        }
                    }

                } else if (question?.typeOfQuestion === 'TLN') {
                    if (!answer.answerContent || !question.correctAnswer) continue;

                    const studentAns = answer.answerContent.trim().replace(',', '.');
                    const correctAns = question.correctAnswer.trim().replace(',', '.');
                    const check = studentAns === correctAns;

                    if (answer.result !== check) {
                        await answer.update({ result: check }, { transaction });
                        countUpdated++;
                    }

                    if (check) score += 0.5;
                }
            }

            await attempt.update({ score }, { transaction });
            results.push({
                attemptId: attempt.id,
                score,
                updated: countUpdated,
                total: attemptAnswers.length,
            });
        }

        await transaction.commit();
        return {
            message: 'Chấm lại hàng loạt thành công!',
            results,
        };

    } catch (error) {
        await transaction.rollback();
        throw error;
    }
};
