import api from './api';

// <PERSON><PERSON><PERSON> tất cả hoạt động admin với phân trang theo thời gian
export const getAdminActivitiesAPI = async ({ 
    search = '', 
    limit = 20, 
    cursor = null, 
    sortOrder = 'DESC',
    startDate = null,
    endDate = null
}) => {
    let url = `/v1/admin-activities?limit=${limit}&sortOrder=${sortOrder}`;
    
    if (search) {
        url += `&search=${encodeURIComponent(search)}`;
    }
    
    if (cursor) {
        url += `&cursor=${encodeURIComponent(cursor)}`;
    }
    
    if (startDate) {
        url += `&startDate=${encodeURIComponent(startDate)}`;
    }
    
    if (endDate) {
        url += `&endDate=${encodeURIComponent(endDate)}`;
    }
    
    const response = await api.get(url);
    return response;
};
