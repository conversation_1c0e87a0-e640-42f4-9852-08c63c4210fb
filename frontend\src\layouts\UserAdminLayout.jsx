import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import AdminSidebar from '../components/sidebar/AdminSidebar';
import { Home } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchUserById } from '../features/user/userSlice';

const UserAdminLayout = ({ children }) => {
    const { userId } = useParams();
    const location = useLocation();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { student } = useSelector(state => state.users);
    const [tabs, setTabs] = useState([
        { name: '<PERSON> tiết', path: `/admin/student-management/${userId}`, active: true },
        { name: 'Lớp người dùng', path: `/admin/student-management/${userId}/classes`, active: false },
        { name: '<PERSON><PERSON><PERSON> sử làm bài', path: `/admin/student-management/${userId}/history`, active: false },
        { name: '<PERSON><PERSON><PERSON><PERSON> danh', path: `/admin/student-management/${userId}/attendance`, active: false },
        { name: 'Học phi', path: `/admin/student-management/${userId}/tuition`, active: false },
    ]);
    const { closeSidebar } = useSelector((state) => state.sidebar);

    const handleTabClick = (tab) => {
        setTabs(prevTabs => prevTabs.map(t => ({
            ...t,
            active: t.name === tab.name
        })));
        navigate(tab.path);
    };

    useEffect(() => {
        setTabs(prevTabs =>
            prevTabs.map(tab => ({
                ...tab,
                active: location.pathname === tab.path
            }))
        );
    }, [location.pathname]);

    useEffect(() => {
        if (userId && String(student?.id) !== String(userId)) {
            dispatch(fetchUserById(userId));
        }
    }, [userId, dispatch, student?.id]);

    return (
        <div className="flex min-h-screen bg-gray-50">
            <AdminSidebar />
            <div className={`flex-1 transition-all duration-300 ${closeSidebar ? "ml-[80px]" : "ml-[224px]"}`}>
                <div className="flex flex-col min-h-screen">
                    {/* Header */}
                    <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                        <div className="flex items-center gap-4">
                            {/* Breadcrumb */}
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Home size={16} />
                                <span>Trang chủ</span>
                                <span>/</span>
                                <span>Quản lý học sinh</span>
                                <span>/</span>
                                <span className="font-semibold">{tabs.find(tab => tab.active)?.name}</span>
                            </div>
                        </div>
                        <h1 className="text-2xl font-bold text-gray-900 mt-2">
                            Chi tiết học sinh - {student ? `${student.lastName} ${student.firstName}` : userId}
                        </h1>
                    </div>

                    {/* Navigation Tabs */}
                    <div className="bg-white border-b border-gray-200 px-6">
                        <div className="flex space-x-8">
                            {tabs.map((tab) => (
                                <button
                                    key={tab.name}
                                    onClick={() => handleTabClick(tab)}
                                    className={`py-3 px-4 text-sm font-medium ${tab.active ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
                                >
                                    {tab.name}
                                </button>
                            ))}
                        </div>
                    </div>
                    {/* Main Content */}
                    {children}
                </div>
            </div>
        </div>
    );
};

export default UserAdminLayout;
