'use strict';
import { Model } from 'sequelize';

export default (sequelize, DataTypes) => {
    class AdminActivityLog extends Model {
        /**
         * Helper method for defining associations.
         * This method is not a part of Sequelize lifecycle.
         * The `models/index` file will call this method automatically.
         */
        static associate(models) {
            AdminActivityLog.belongsTo(models.User, { foreignKey: 'adminId', as: 'admin' });
        }
    }
    AdminActivityLog.init({
        adminId: DataTypes.INTEGER,
        action: DataTypes.STRING,
        targetId: DataTypes.INTEGER,
        description: DataTypes.TEXT,
        createdAt: DataTypes.DATE,
        updatedAt: DataTypes.DATE,
    }, {
        sequelize,
        modelName: 'AdminActivityLog',
        tableName: 'AdminActivityLog',
    });
    return AdminActivityLog;
}