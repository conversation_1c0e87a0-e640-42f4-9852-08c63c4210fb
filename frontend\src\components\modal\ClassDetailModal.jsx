import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Clock, GraduationCap, Calendar, User, BookOpen, ExternalLink, X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const ClassDetailModal = ({ isOpen, onClose, classData }) => {
    const navigate = useNavigate();

    if (!classData) return null;

    const handleNavigateToClass = () => {
        navigate(`/class/${classData.class_code}`);
        onClose();
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 z-[60] bg-black bg-opacity-40 flex items-start lg:items-center justify-center overflow-y-auto pt-8 pb-8 px-2 sm:px-4"
                    onClick={onClose}
                >
                    <motion.div
                        initial={{ scale: 0.95 }}
                        animate={{ scale: 1 }}
                        exit={{ scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                        onClick={(e) => e.stopPropagation()}
                        className="bg-white w-full max-w-lg p-6 rounded-md shadow-lg border border-gray-200"
                    >
                        {/* Header */}
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-cyan-100 rounded-md flex items-center justify-center">
                                    <GraduationCap size={20} className="text-cyan-600" />
                                </div>
                                <div>
                                    <h2 className="text-lg font-semibold text-gray-900">{classData.name}</h2>
                                    {classData.sessionNumber && (
                                        <p className="text-sm text-gray-500">Buổi {classData.sessionNumber}</p>
                                    )}
                                </div>
                            </div>
                            <button
                                onClick={onClose}
                                className="p-1.5 rounded-md hover:bg-gray-100 transition-colors"
                            >
                                <X size={18} className="text-gray-500" />
                            </button>
                        </div>

                        <hr className="mb-4 border-gray-200" />

                        {/* Content */}
                        <div className="space-y-4">
                            {/* Thời gian */}
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-md">
                                <Clock size={16} className="text-gray-600" />
                                <div>
                                    <p className="text-sm font-medium text-gray-900">Thời gian học</p>
                                    <p className="text-xs text-gray-500">
                                        {classData.startTime} - {classData.endTime}
                                    </p>
                                </div>
                            </div>

                            {/* Thông tin lớp */}
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-xs text-gray-500">Mã lớp</p>
                                    <p className="text-sm font-medium text-gray-900">{classData.class_code}</p>
                                </div>
                                <div>
                                    <p className="text-xs text-gray-500">Khối</p>
                                    <p className="text-sm font-medium text-gray-900">{classData.grade}</p>
                                </div>
                                <div>
                                    <p className="text-xs text-gray-500">Năm học</p>
                                    <p className="text-sm font-medium text-gray-900">{classData.academicYear}</p>
                                </div>
                                <div>
                                    <p className="text-xs text-gray-500">Trạng thái</p>
                                    <div className={`h-fit flex-shrink-0 p-1 border border-gray-200 rounded-full text-[0.6rem] w-fit ${
                                        classData.status === 'LHD' 
                                            ? 'border-green-300 text-green-600' 
                                            : 'border-red-300 text-red-600'
                                    }`}>
                                        {classData.status === 'LHD' ? 'Hoạt động' : 'Chưa hoạt động'}
                                    </div>
                                </div>
                            </div>

                            {/* Mô tả */}
                            {classData.description && (
                                <div>
                                    <p className="text-xs text-gray-500 mb-1">Mô tả</p>
                                    <p className="text-sm text-gray-700">{classData.description}</p>
                                </div>
                            )}
                        </div>

                        <hr className="my-4 border-gray-200" />

                        {/* Actions */}
                        <div className="flex justify-end gap-3">
                            <button
                                onClick={onClose}
                                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-md transition-colors duration-200"
                            >
                                Đóng
                            </button>
                            <button
                                onClick={handleNavigateToClass}
                                className="px-4 py-2 bg-cyan-600 hover:bg-cyan-700 text-white text-sm font-medium rounded-md transition-colors duration-200 flex items-center gap-2"
                            >
                                <ExternalLink size={16} />
                                Vào lớp học
                            </button>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default ClassDetailModal;
