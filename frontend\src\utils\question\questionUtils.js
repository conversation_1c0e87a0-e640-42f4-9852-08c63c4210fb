// questionUtils.js
import { setErrorMessage } from "../../features/state/stateApiSlice";

// Hàm validateCorrectAnswer nhận vào:
// - question: đối tượng câu hỏi (chứa typeOfQuestion)
// - correctAnswer: giá trị của correctAnswer
// - dispatch: hàm dispatch từ Redux
// - setErrorMessage: action creator để thêm lỗi vào errorSlice
export const validateCorrectAnswer = (question, correctAnswer, dispatch, content) => {
    if (content.trim() === "") {
        dispatch(setErrorMessage("Nội dung câu hỏi không được để trống!"));
        return false;
    }
    if (question.typeOfQuestion === null) {
        dispatch(setErrorMessage("Loại câu hỏi không được để trống!"));
        return false;
    }
    if (correctAnswer.trim() === "") {
        dispatch(setErrorMessage("Đáp án không được để trống!"));
        return false;

    } else {
        if (question.typeOfQuestion === 'TLN' && !/^[-+]?\d+(\.\d+)?$/.test(correctAnswer.replace(",", "."))) {
            dispatch(setErrorMessage("Đáp án phải là một số!"));
            return false;
        }
    }

    if (question.class === null) {
        dispatch(setErrorMessage("Lớp không được để trống!"));
        return false;
    }

    if (question.class !== null && question.chapter !== null) {
        if (!question.chapter.startsWith(question.class)) {
            dispatch(setErrorMessage("Chương này không phải là chương của lớp!"));
            return false;
        }
    }

    if (question.typeOfQuestion === "TN") {
        if (!/^[A-D]$/.test(correctAnswer.trim())) {
            dispatch(setErrorMessage("Đáp án cho câu hỏi TN phải có dạng một ký tự A, B, C hoặc D!"));
            return false;
        }
    } else if (question.typeOfQuestion === "DS") {
        const tokens = correctAnswer.trim().split(/\s+/);
        if (tokens.length === 0 || !tokens.every((token) => token === "Đ" || token === "S")) {
            dispatch(setErrorMessage("Đáp án cho câu hỏi DS phải có dạng các ký tự 'Đ' hoặc 'S', ví dụ: 'Đ Đ S S'!"));
            return false;
        }
    }


    return true;
};

// Hàm processInput nhận vào:
// - question: đối tượng câu hỏi (chứa typeOfQuestion)
// - correctAnswer: đáp án đầu vào
// - content: chuỗi content chứa nội dung câu hỏi và đáp án
// Hàm trả về một object chứa:
// - questionContent: nội dung câu hỏi đã được xử lý (loại bỏ tiền tố "Câu X.")
// - newStatements: mảng đối tượng statement đã xử lý, trong đó có thuộc tính isCorrect dựa vào correctAnswer
export const processInput = (question, correctAnswer, content, dispatch) => {
    // Tách content thành các dòng, loại bỏ khoảng trắng thừa và dòng rỗng
    const lines = content
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line !== "");

    let questionContent = "";
    const statementLines = [];
    let foundOption = false;

    if (question.typeOfQuestion === "TN") {
        // Với TN, đáp án có định dạng "A. ...", "B. ..." (chữ in hoa và dấu chấm)
        for (let line of lines) {
            if (/^[A-Z]\./.test(line)) {
                foundOption = true;
                statementLines.push(line.slice(2).trim());
            } else if (!foundOption) {
                questionContent += line + " ";
            } else {
                statementLines[statementLines.length - 1] += " " + line;
            }
        }
    } else if (question.typeOfQuestion === "DS") {
        // Với DS, đáp án có định dạng "a) ..." (chữ thường và dấu ngoặc đơn)
        for (let line of lines) {
            if (/^[a-z]\)/.test(line)) {
                foundOption = true;
                statementLines.push(line.slice(2).trim());
            } else if (!foundOption) {
                questionContent += line + " ";
            } else {
                statementLines[statementLines.length - 1] += " " + line;
            }
        }
    } else if (question.typeOfQuestion === "TLN") {
        questionContent = content;
    }

    // Loại bỏ tiền tố "Câu X." ở đầu nội dung câu hỏi nếu có
    questionContent = questionContent.trim().replace(/^Câu\s*\d+\.\s*/, "");

    if (question.typeOfQuestion === "DS" || question.typeOfQuestion === "TN") {
        if (statementLines.length < 2 || statementLines === null || statementLines === undefined) {
            dispatch(setErrorMessage("Mệnh đề không hợp lệ"));
            return false;
        }
    }

    // Tạo mảng các đối tượng statement từ statementLines
    const newStatements = statementLines.map((line) => ({
        content: line,
        isCorrect: false,
        needImage: false,
        difficulty: null,
    }));

    // Xử lý correctAnswer theo từng loại câu hỏi
    if (question.typeOfQuestion === "DS") {
        const correctTokens = correctAnswer.trim().split(/\s+/);
        correctTokens.forEach((answer, index) => {
            newStatements[index].isCorrect = answer === "Đ";
        });
    } else if (question.typeOfQuestion === "TN") {
        const letter = correctAnswer.trim();
        const correctIndex = letter.charCodeAt(0) - "A".charCodeAt(0);
        newStatements.forEach((statement, index) => {
            statement.isCorrect = index === correctIndex;
        });
    }

    return { questionContent, newStatements };
};


export const validateInput = (question, dispatch) => {
    if (question.class === null) {
        dispatch(setErrorMessage("Lớp không được để trống!"));
        return false;
    }
    if (question.correctAnswer) {
        if (!/^[-+]?\d+(\.\d+)?$/.test(question.correctAnswer)) {
            dispatch(setErrorMessage("Đáp án phải là một số!"));
            return false;
        }
    }

    if (question.class !== null && question.chapter !== null) {
        if (!question.chapter.startsWith(question.class)) {
            dispatch(setErrorMessage("Chương này không phải là chương của lớp!"));
            return false;
        }
    }

    if (question.typeOfQuestion === 'TN') {
        if (question.statements.filter(statement => statement.isCorrect === true).length !== 1) {
            dispatch(setErrorMessage("Câu hỏi TN có một đáp án đúng!"));
            return false;
        }
    }

    return true;
}

export const processInputForUpdate = (question) => {
    const newQuestion = {
        ...question,
        statements: question.statements?.map(statement => ({ ...statement }))
    };

    newQuestion.content = newQuestion.content?.trim().replace(/^Câu\s*\d+\.\s*/, "");
    newQuestion.correctAnswer = newQuestion.correctAnswer?.trim().replace(",", ".");
    newQuestion.solution = newQuestion.solution?.trim();
    newQuestion.solutionUrl = newQuestion.solutionUrl?.trim();
    newQuestion.description = newQuestion.description?.trim();

    newQuestion.statements?.forEach((statement) => {
        statement.content = statement.content?.trim();
    });

    return newQuestion;
};

export const normalizeText = (text) => {
    return text
        // Chuẩn hóa Unicode dạng NFC (gộp ký tự tổ hợp thành một ký tự chuẩn)
        .normalize("NFC")

        // Chuyển ký tự Cyrillic (Nga) giống Latin → Latin thật
        // Ví dụ: chữ 'а' (Nga, U+0430) → 'a' (Latin, U+0061)
        .replace(/[а-яА-Я]/g, (c) => String.fromCharCode(c.charCodeAt(0) - 848))

        // Xóa các ký tự zero-width không nhìn thấy (ẩn)
        // \u200B (zero-width space)
        // \u200C (zero-width non-joiner)
        // \u200D (zero-width joiner)
        // \uFEFF (zero-width no-break space / BOM)
        .replace(/[\u200B-\u200D\uFEFF]/g, "")

        // Thay ngoặc kép cong (“ ”) → ngoặc kép thẳng (")
        .replace(/[“”]/g, '"')

        // Thay ngoặc đơn cong (‘ ’) → ngoặc đơn thẳng (')
        .replace(/[‘’]/g, "'")

        // Thay gạch ngang dài (–) → gạch ngang thường (-)
        .replace(/–/g, "-")

        // Bỏ lệnh LaTeX \section*{Tiêu đề} → giữ lại nội dung bên trong
        .replace(/\\section\*\{([^}]+)\}/gi, "$1")

        // Xóa ảnh Markdown dạng ![alt](url)
        .replace(/!\[.*?\]\(.*?\)/g, "")

        // Xóa lệnh LaTeX \label{...}, \ref{...}, \cite{...}
        .replace(/\\(label|ref|cite)\{.*?\}/gi, "")

        // Xóa toàn bộ bảng LaTeX (\begin{tabular} ... \end{tabular})
        .replace(/\\begin\{tabular\}[\s\S]*?\\end\{tabular\}/gi, "")

        // Xóa bảng Markdown (các dòng bắt đầu bằng | và có nhiều dòng)
        .replace(/^\|.*\n(\|.*\n)+/gim, "")

        // Xóa dấu # ở đầu dòng (heading trong Markdown)
        .replace(/^#+\s*/gim, "")

        // Xóa tất cả dấu # còn lại
        .replace(/#/g, "")

        // Xóa dòng chỉ chứa --- (ngăn cách trong Markdown)
        .replace(/^\s*---+\s*$/gim, "")

        // Xóa dòng chỉ chứa \hline (đường kẻ bảng LaTeX)
        .replace(/^\s*\\hline\s*$/gim, "")

        // Xóa dòng bảng LaTeX dạng: { } & ... \\ (có thể có ngoặc trước &)
        .replace(/^\s*[\}\{]?\s*&.*?\\\\\s*$/gim, "")

        // Xóa dòng bảng LaTeX dạng: & ... \\
        .replace(/^\s*&.*?\\\\\s*$/gim, "")

        // Xóa dòng bảng LaTeX dạng: \?{ }? & ...
        .replace(/^\s*\\?[\}\{]?\s*&.*$/gim, "")

        // Xóa nội dung in đậm Markdown (**...**)
        .replace(/\*\*(.*?)\*\*/gs, "")

        // Chuyển công thức toán block $$...$$ → inline $...$
        .replace(/\$\$(.*?)\$\$/gs, (_, content) => `$${content}$`)

        // Xóa khoảng trắng thừa đầu và cuối
        .trim();
};




export const splitCorrectAnswerTN = (correctAnswersText) => {
    if (correctAnswersText.trim() === "") return [];
    // Chuyển đổi chuỗi đáp án thành mảng các đáp án
    correctAnswersText = normalizeText(correctAnswersText);

    const correctAnswers = correctAnswersText
        .trim()
        .split(/\s+/)        // tách theo 1 hoặc nhiều khoảng trắng
        .filter(Boolean)     // loại bỏ phần tử rỗng (nếu có)
        .map(ans => ans.toUpperCase());  // viết hoa toàn bộ

    return correctAnswers;
};

// Regex để nhận diện dòng bắt đầu lời giải/đáp án
// Hỗ trợ cả trường hợp standalone header và header với nội dung theo sau
const ANSWER_OR_SOLUTION_REGEX = /^([đĐd][áaàạảãâấầậẩẫăắằặẳẵ]?p\s*[aáãẵẫảẩẳ]n[sS]?|[Ll][ơoờòõỏ][ìi]?\s*[Gg][iy][aăâẩẳảãẵẫ][iy]*|[Gg][iy][aăâẩẳảẵã][iy]*|[Hh][ướưu][ớơo][nnm]g\s*[dđ][ẫaăâ]n\s*[Gg][iy][aăâẩẳả][iy]*)\s*[:：]?\s*(.*)$/i;
export const splitContentTN = (content, correctAnswersText, dispatch) => {
    if (content.trim() === "" || correctAnswersText.trim() === "")
        return []

    content = normalizeText(content);
    const lines = content
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line !== "");

    let questionsTN = [];
    let questionContent = "";
    let statementLines = [];
    let foundQuestion = false;
    let foundStatement = false;
    let foundAnswerLine = false;
    let currentSolution = "";
    let index = 0;
    let countTN = 0;
    let id = 0

    const correctAnswers = splitCorrectAnswerTN(correctAnswersText);

    for (let line of lines) {
        // Nếu là dòng bắt đầu câu hỏi mới
        if (/^([Cc]âu)\s*\d+[\.:]/.test(line)) {
            if (foundQuestion) {
                // Push câu trước đó trước khi reset
                questionsTN.push({
                    id: id++,
                    questionData: {
                        typeOfQuestion: "TN",
                        content: questionContent.trim(),
                        solution: currentSolution.trim(), // thêm lời giải
                        class: null,
                        chapter: null,
                        difficulty: null,
                        imageUrl: null,
                    },
                    statements: statementLines,
                });
                index++;
            }

            foundQuestion = true;
            questionContent = line.replace(/^([Cc]âu)\s*\d+[\.:]\s*/, "");
            statementLines = [];
            currentSolution = "";
            foundStatement = false;
            foundAnswerLine = false;
        }

        // Nếu là dòng "Đáp án: ..."
        else if (ANSWER_OR_SOLUTION_REGEX.test(line)) {
            foundAnswerLine = true;
            foundStatement = false;
            currentSolution += line + "\n"; // ✅ giữ lại dòng này
        }

        // Nếu là dòng lời giải (sau dòng Đáp án)
        else if (foundAnswerLine) {
            currentSolution += line + "\n";
        }

        // Nếu là đáp án A. B. C.
        else if (/^[a-dA-D][\.\)]/.test(line) && !foundAnswerLine) {
            const match = line.match(/^([a-dA-D])[\.\)]\s*(.*)$/);
            if (match) {
                const [, option, text] = match;
                foundStatement = true;
                statementLines.push({
                    order: countTN,
                    imageUrl: null,
                    content: text.trim(),
                    isCorrect:
                        correctAnswers[index] &&
                        correctAnswers[index].toUpperCase() === option.toUpperCase(),
                });
                countTN++;
            }
        }



        // Nội dung phụ của đáp án
        else if (foundStatement) {
            statementLines[statementLines.length - 1].content += " " + line;
        }

        // Nội dung phụ của câu hỏi
        else {
            questionContent += " " + line;
        }
    }

    // Thêm câu cuối cùng
    if (foundQuestion) {
        if (foundQuestion) {
            questionsTN.push({
                id: id++,
                questionData: {
                    typeOfQuestion: "TN",
                    content: questionContent.trim(),
                    solution: currentSolution.trim(), // ✅ đừng quên
                    class: null,
                    chapter: null,
                    difficulty: null,
                    imageUrl: null,
                },
                statements: statementLines,
            });
        }
    }

    if (questionsTN.length !== correctAnswers.length) {
        dispatch(
            setErrorMessage(
                `Số lượng đáp án không khớp với số lượng câu hỏi! ${questionsTN.length} - ${correctAnswers.length}`
            )
        );
        return [];
    }

    return questionsTN
};


export const splitCorrectAnswerDS = (correctAnswersText) => {
    if (correctAnswersText.trim() === "") return [];
    // Chuyển đổi chuỗi đáp án thành mảng các đáp án
    correctAnswersText = normalizeText(correctAnswersText);

    const correctAnswers = correctAnswersText
        .trim()
        .replace(/-/g, "")
        .toUpperCase()
        .split(/\s+/)
        .map(group => group.split(""));

    return correctAnswers
}


export const splitContentDS = (content, correctAnswersText, dispatch) => {
    if (content.trim() === "" || correctAnswersText.trim() === "") return []

    content = normalizeText(content);

    const lines = content
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line !== "");


    let questionsDS = [];
    let questionContent = "";
    let statementLines = [];
    let foundQuestion = false;
    let foundStatement = false;
    let foundAnswerLine = false;
    let currentSolution = "";
    let index = 0;
    let id = 100

    const correctAnswers = splitCorrectAnswerDS(correctAnswersText);

    for (let line of lines) {
        // Nhận diện câu hỏi mới
        if (/^([Cc]âu)\s*\d+[\.:]/.test(line)) {
            if (foundQuestion) {
                if (!correctAnswers[index]) {
                    dispatch(setErrorMessage("Số lượng đáp án không khớp với số lượng câu hỏi!"));
                    return false;
                }
                if (statementLines.length !== correctAnswers[index].length) {
                    dispatch(setErrorMessage(`Số lượng đáp án không khớp với số lượng mệnh đề ở Câu ${index + 1}!`));
                    return false;
                }

                questionsDS.push({
                    id: id++,
                    questionData: {
                        typeOfQuestion: "DS",
                        content: questionContent.trim(),
                        solution: currentSolution.trim(),
                        class: null,
                        chapter: null,
                        difficulty: null,
                        imageUrl: null,
                    },
                    statements: statementLines,
                });
                index++;
            }

            foundQuestion = true;
            questionContent = line.replace(/^([Cc]âu)\s*\d+[\.:]\s*/, "");
            statementLines = [];
            currentSolution = "";
            foundStatement = false;
            foundAnswerLine = false;
        }


        // Dòng đáp án hoặc lời giải
        else if (ANSWER_OR_SOLUTION_REGEX.test(line)) {
            foundAnswerLine = true;
            foundStatement = false;
            currentSolution += line + "\n"; // ✅ giữ lại dòng này
        }
        // Lời giải sau dòng đáp án
        else if (foundAnswerLine) {
            currentSolution += line + "\n";
        }

        // Dòng mệnh đề
        else if (/^[a-dA-D][\.\)]/.test(line) && !foundAnswerLine) {
            const match = line.match(/^([a-dA-D])[\.\)]\s*(.*)$/);
            if (match) {
                const [, , text] = match;
                foundStatement = true;
                const currentAnswer = correctAnswers[index]?.[statementLines.length];

                statementLines.push({
                    imageUrl: null,
                    order: statementLines.length,
                    content: text.trim(),
                    isCorrect: currentAnswer === "Đ" || currentAnswer === "D",
                });
            }
        }

        // Nội dung bổ sung cho mệnh đề
        else if (foundStatement) {
            statementLines[statementLines.length - 1].content += " " + line;
        }

        // Nội dung phụ của câu hỏi
        else {
            questionContent += " " + line;
        }
    }

    // Xử lý câu cuối cùng
    if (foundQuestion) {
        if (!correctAnswers[index]) {
            dispatch(setErrorMessage("Số lượng đáp án không khớp với số lượng câu hỏi!"));
            return [];
        }
        if (statementLines.length !== correctAnswers[index].length) {
            dispatch(setErrorMessage(`Số lượng đáp án không khớp với số lượng mệnh đề ở Câu ${index + 1}!`));
            return [];
        }

        questionsDS.push({
            id: id++,
            questionData: {
                typeOfQuestion: "DS",
                content: questionContent.trim(),
                solution: currentSolution.trim(),
                class: null,
                chapter: null,
                difficulty: null,
                imageUrl: null,
            },
            statements: statementLines,
        });
    }

    return questionsDS
};


export const splitCorrectAnswerTLN = (correctAnswersText) => {
    if (correctAnswersText.trim() === "") return [];
    // Chuyển đổi chuỗi đáp án thành mảng các đáp án
    correctAnswersText = normalizeText(correctAnswersText);
    const correctAnswers = correctAnswersText
        .trim()
        .replace(/,/g, ".")
        .split(/\s+/)      // tách theo 1 hoặc nhiều khoảng trắng
        .filter(Boolean);  // loại bỏ chuỗi rỗng (nếu có)
    // console.log(correctAnswers)
    return correctAnswers;
}

export const splitContentTLN = (content, correctAnswersText, dispatch) => {
    if (content.trim() === "" || correctAnswersText.trim() === "") return [];

    content = normalizeText(content);
    const lines = content
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line !== "");

    let questionsTLN = [];
    let questionContent = "";
    let currentSolution = "";
    let foundQuestion = false;
    let foundAnswerLine = false;
    let index = 0;
    let id = 200;
    const correctAnswers = splitCorrectAnswerTLN(correctAnswersText);

    for (let line of lines) {
        // Dòng bắt đầu câu hỏi mới
        if (/^([Cc]âu)\s*\d+[\.:]/.test(line)) {
            if (foundQuestion) {
                if (index >= correctAnswers.length) {
                    dispatch(setErrorMessage("Số lượng đáp án không khớp với số lượng câu hỏi!"));
                    return false;
                }

                questionsTLN.push({
                    id: id++,
                    questionData: {
                        typeOfQuestion: "TLN",
                        content: questionContent.trim(),
                        correctAnswer: correctAnswers[index],
                        solution: currentSolution.trim(),
                        class: null,
                        chapter: null,
                        difficulty: null,
                        imageUrl: null,
                    },
                });

                index++;
            }

            foundQuestion = true;
            questionContent = line.replace(/^([Cc]âu)\s*\d+[\.:]\s*/, "");
            currentSolution = "";
            foundAnswerLine = false;
        }

        // Dòng "Đáp án"/"Lời giải"
        else if (ANSWER_OR_SOLUTION_REGEX.test(line)) {
            foundAnswerLine = true;
            currentSolution += line + "\n"; // ✅ giữ lại dòng này
        }

        // Nội dung lời giải (sau dòng Đáp án)
        else if (foundAnswerLine) {
            currentSolution += line + "\n";
        }

        // Nội dung bổ sung cho câu hỏi
        else {
            questionContent += " " + line;
        }
    }

    // Câu cuối cùng
    if (foundQuestion) {
        if (index >= correctAnswers.length) {
            dispatch(setErrorMessage("Số lượng đáp án không khớp với số lượng câu hỏi!"));
            return [];
        }

        questionsTLN.push({
            id: id++,
            questionData: {
                typeOfQuestion: "TLN",
                content: questionContent.trim(),
                correctAnswer: correctAnswers[index],
                solution: currentSolution.trim(),
                class: null,
                chapter: null,
                difficulty: null,
                imageUrl: null,
            },
        });
    }

    return questionsTLN;
};




export const validateExamData = (examData, dispatch) => {
    if (examData.name.trim() === "") {
        dispatch(setErrorMessage("Tên đề thi không được để trống!"));
        return false;
    }
    if (examData.class === null) {
        dispatch(setErrorMessage("Lớp không được để trống!"));
        return false;
    }
    if (examData.typeOfExam === null) {
        dispatch(setErrorMessage("Kiểu đề thi không được để trống!"));
        return false;
    }

    if (examData.year === null) {
        dispatch(setErrorMessage("Năm không được để trống!"));
        return false;
    }

    if (examData.passRate < 0 || examData.passRate > 100) {
        dispatch(setErrorMessage("Tỷ lệ đạt không hợp lệ!"));
        return false;
    }

    return true;
}

export const splitMarkdownToParts = (markdown, dispatch) => {
    if (typeof markdown !== 'string') {
        dispatch(setErrorMessage("Đề thi không hợp lệ!"));
        return false;
    }

    // Regex hỗ trợ: PHẦN I / phần 1 / Phần ii / phần III / PHẦN 3
    const pattern = /((ph[ầâàa]n)\s*(i{1,3}|1|2|3))/gi;

    const matches = [];
    let match;
    while ((match = pattern.exec(markdown)) !== null) {
        matches.push({ index: match.index, text: match[0].toUpperCase() });
    }

    if (matches.length < 3) {
        dispatch(setErrorMessage("Không tìm thấy đủ 3 phần trong đề thi!"));
        return false;
    }

    const [match1, match2, match3] = matches;

    const partTN = markdown.slice(match1.index, match2.index).trim();
    const partDS = markdown.slice(match2.index, match3.index).trim();
    const partTLN = markdown.slice(match3.index).trim();

    return {
        TN: partTN,
        DS: partDS,
        TLN: partTLN,
    };
};