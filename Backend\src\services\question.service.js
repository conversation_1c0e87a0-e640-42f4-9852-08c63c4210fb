import { Op } from "sequelize"
import db from "../models/index.js"
import { uploadImage, cleanupUploadedFiles } from "../utils/imageUpload.js"
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js"

const Question = db.Question
const Statement = db.Statement
const Exam = db.Exam


export const getQuestionsWithFilter = async ({ sortOrder = 'DESC', search = '', page = 1, limit = 10 }) => {
    const offset = (page - 1) * limit

    let whereClause = {}
    if (search.trim() !== '') {
        whereClause = {
            [Op.or]: [
                { content: { [Op.like]: `%${search}%` } },
                { typeOfQuestion: { [Op.like]: `%${search}%` } },
                { chapter: { [Op.like]: `%${search}%` } },
                { difficulty: { [Op.like]: `%${search}%` } },
                { class: { [Op.like]: `%${search}%` } },
                { id: { [Op.like]: `%${search}%` } },
                { description: { [Op.like]: `%${search}%` } },
            ],
        }
    }

    const [questionList, total] = await Promise.all([
        Question.findAll({
            where: whereClause,
            offset,
            limit,
            include: [
                {
                    model: Statement,
                    as: 'statements',
                    attributes: ['content', 'order', 'isCorrect', 'imageUrl'],
                },
                {
                    model: Exam,
                    as: 'exams',
                    attributes: ['id', 'name', 'description'],
                    through: {
                        attributes: ['order'], // Lấy thêm thứ tự của câu hỏi trong đề thi
                        as: 'examQuestion'
                    }
                }
            ],
            order: [['createdAt', sortOrder]],
        }),
        Question.count({ where: whereClause }),
    ])

    return new ResponseDataPagination(questionList, {
        total,
        page,
        pageSize: limit,
        totalPages: Math.ceil(total / limit),
        sortOrder,
    })
}

export async function getAllChaptersAndDifficulties() {
    const allChapters = await db.AllCode.findAll({
        where: { type: 'chapter' },
        attributes: ['code', 'description']
    });

    const difficulties = await db.AllCode.findAll({
        where: { type: 'difficulty' },
        attributes: ['code', 'description']
    });

    return { allChapters, difficulties };
}

export function filterRelatedChapters(classLevel, chapters, allChapters, addSameChapter = false) {
    if (!chapters || chapters.length === 0) {
        return allChapters.filter(ch => ch.code.startsWith(`${classLevel}C`));
    }

    const relatedChapters = new Set(chapters);

    if (addSameChapter) {
        chapters.forEach(chapterCode => {
            const prefix = chapterCode.substring(0, 4);
            const matches = allChapters.filter(ch => ch.code.startsWith(prefix));
            matches.forEach(ch => relatedChapters.add(ch.code));
        });
    }

    return allChapters.filter(ch => relatedChapters.has(ch.code));
}

export async function getQuestionsForType(
    typeOfQuestion,
    count,
    filteredChapters,
    difficultyList,
    questionIds = []
) {
    const questions = [];
    const usedCombinations = new Set();
    const questionIdSet = new Set(questionIds);

    // Tạo tất cả combination chapter - difficulty
    const allCombinations = [];
    for (const chapter of filteredChapters) {
        for (const difficulty of difficultyList) {
            allCombinations.push({ chapter: chapter.code, difficulty });
        }
    }

    const maxRounds = 3; // Số lần lặp qua toàn bộ combinations
    for (let round = 0; round < maxRounds; round++) {
        const shuffled = [...allCombinations].sort(() => Math.random() - 0.5);

        for (const combo of shuffled) {
            if (questions.length >= count) break;

            const found = await db.Question.findAll({
                where: {
                    typeOfQuestion,
                    chapter: combo.chapter,
                    difficulty: combo.difficulty,
                    id: { [Op.notIn]: [...questionIdSet] }
                },
                include: [{
                    model: db.Statement,
                    as: 'statements',
                    attributes: ['id', 'content', 'isCorrect', 'imageUrl', 'order'],
                    order: [['order', 'ASC']]
                }],
                attributes: [
                    'id', 'content', 'typeOfQuestion', 'chapter', 'difficulty',
                    'class', 'description', 'correctAnswer', 'solution',
                    'imageUrl', 'solutionImageUrl'
                ],
                order: db.sequelize.random(),
                limit: count - questions.length
            });

            for (const q of found) {
                if (!questionIdSet.has(q.id)) {
                    questions.push(q);
                    questionIdSet.add(q.id);
                }
                if (questions.length >= count) break;
            }

            usedCombinations.add(`${combo.chapter}-${combo.difficulty}`);
        }

        if (questions.length >= count) break;
    }

    // Nếu vẫn chưa đủ, fallback lấy thêm không phân biệt difficulty
    if (questions.length < count) {
        const additional = await db.Question.findAll({
            where: {
                typeOfQuestion,
                chapter: { [Op.in]: filteredChapters.map(ch => ch.code) },
                id: { [Op.notIn]: [...questionIdSet] }
            },
            include: [{
                model: db.Statement,
                as: 'statements',
                attributes: ['id', 'content', 'isCorrect', 'imageUrl', 'order'],
                order: [['order', 'ASC']]
            }],
            attributes: [
                'id', 'content', 'typeOfQuestion', 'chapter', 'difficulty',
                'class', 'description', 'correctAnswer', 'solution',
                'imageUrl', 'solutionImageUrl'
            ],
            order: db.sequelize.random(),
            limit: count - questions.length
        });

        for (const q of additional) {
            if (!questionIdSet.has(q.id)) {
                questions.push(q);
                questionIdSet.add(q.id);
                if (questions.length >= count) break;
            }
        }
    }

    return questions.slice(0, count);
}
