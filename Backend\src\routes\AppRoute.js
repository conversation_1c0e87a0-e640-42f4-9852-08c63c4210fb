import express from 'express'
import UserRoutes from './UserRoutes.js'
import ImageRoutes from './ImageRoutes.js'
import ExamRoutes from './ExamRoutes.js'
import QuestionRoutes from './QuestionRoutes.js'
import ClassRoutes from './ClassRoutes.js'
import AssistantReportRoutes from './AssistantReportRoutes.js'
import QuestionReportRoutes from './QuestionReportRoutes.js'
import LessonRoutes from './LessonRoutes.js'
import AttemptRoutes from './AttemptRoutes.js'
import AnswerRoutes from './AnswerRoutes.js'
import CheatRoutes from './CheatRoutes.js'
import LearningItemRoutes from './LearningItemRoutes.js'
import CodeRouters from './CodeRoutes.js'
import StatementRoutes from './StatementRoutes.js'
import SlideRoutes from './SlideRoutes.js'
import ArticleRoutes from './ArticleRoutes.js'
import AchievementRoutes from './AchievementRoutes.js'
import NotificationRoutes from './NotificationRoutes.js'
import NotificationExampleRoutes from './NotificationExampleRoutes.js'
import TuitionPaymentRoutes from './TuitionPaymentRoutes.js'
import LogAdminActivityRoutes from './LogAdminActivityRoutes.js'
import corsMiddleware from '../middlewares/corsMiddleware.js'
import AttendanceRoutes from './AttendanceRoutes.js'
import CronRoutes from './CronRoute.js'
import SheetLinksRoutes from './SheetLinksRoutes.js'
import DoExamRoutes from './DoExamRoutes.js'
import Exam1Routes from './Exam1Routes.js'
import ExamCommentsRoutes from './ExamCommentsRoutes.js'
import StudentExamRoutes from './StudentExamStatus.js'
import GPTRoutes from './GPTRoutes.js'
import MistralAIRoutes from './MistralAIRoutes.js';

export const AppRoute = (app) => {
    // Áp dụng CORS middleware cho tất cả các route
    app.use(corsMiddleware);

    // User routes
    app.use('/api/', UserRoutes)

    // Image upload route
    app.use('/api/', ImageRoutes)

    // Exam routes
    app.use('/api/', ExamRoutes)

    // Question routes
    app.use('/api/', QuestionRoutes)

    // Class routes
    app.use('/api/', ClassRoutes)

    // AssistantReport routes
    app.use('/api/', AssistantReportRoutes)

    // QuestionReport routes
    app.use('/api/', QuestionReportRoutes)

    // Lesson routes
    app.use('/api/', LessonRoutes)

    // Attempt routes
    app.use('/api/', AttemptRoutes)

    // Answer routes
    app.use('/api/', AnswerRoutes)

    // Cheat routes
    app.use('/api/', CheatRoutes)

    // LearningItem routes
    app.use('/api/', LearningItemRoutes)

    // Code routes
    app.use('/api/', CodeRouters)

    // Statement routes
    app.use('/api/', StatementRoutes)

    // Slide routes
    app.use('/api/', SlideRoutes)

    // Article routes
    app.use('/api/', ArticleRoutes)

    // Achievement routes
    app.use('/api/', AchievementRoutes)

    // Notification routes
    app.use('/api/', NotificationRoutes)

    // Notification example routes
    app.use('/api/', NotificationExampleRoutes)

    // TuitionPayment routes
    app.use('/api/', TuitionPaymentRoutes)

    // LogAdminActivity routes
    app.use('/api/', LogAdminActivityRoutes)

    // Attendance routes
    app.use('/api/', AttendanceRoutes)

    // Cron job routes
    app.use('/api/', CronRoutes)

    // Sheet Links routes
    app.use('/api/', SheetLinksRoutes)

    // DoExam routes
    app.use('/api/', DoExamRoutes)

    // Exam1 routes
    app.use('/api/', Exam1Routes)

    // ExamComments routes
    app.use('/api/', ExamCommentsRoutes)

    // StudentExam routes
    app.use('/api/', StudentExamRoutes)

    // GPT routes
    app.use('/api/', GPTRoutes)

    // MistralAI routes
    app.use('/api/', MistralAIRoutes)
}
