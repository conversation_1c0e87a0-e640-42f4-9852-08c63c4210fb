import { Bee<PERSON>ath<PERSON>ogo } from '../logo/BeeMathLogo';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { toggleDropdown, toggleExamDropdown, toggleCloseSidebar } from '../../features/sidebar/sidebarSlice';
import { logout } from '../../features/auth/authSlice';
import HeaderSidebar from './HeaderSidebar';
import UserSidebar from './UserSidebar';
import UserType from 'src/constants/UserType';
import { ChevronLeft, ChevronRight, Users, FileText, CreditCard, Newspaper, Globe, LogOut, School, Code, Trophy, ClipboardList, FileSearch, Bot, UserCog, BarChart3, User, Home, Activity } from "lucide-react";

const MenuSidebar = ({ onClick, route, Icon, text, icon2, role = [] }) => {
    const location = useLocation();
    const closeSidebar = useSelector(state => state.sidebar?.closeSidebar); // Fix lỗi undefined

    const user = useSelector((state) => state.auth.user);
    if (role.length > 0 && !role.includes(user?.userType)) {
        return null;
    }

    return (
        <div className="flex items-center justify-center px-3 w-full"
        >
            <button
                onClick={onClick}
                className={`flex items-center rounded-lg
        ${closeSidebar ? 'justify-center w-9 h-9' : 'justify-start gap-4 w-full p-2'}
        ${location.pathname.includes(route)
                        ? 'bg-[#253f61] text-white'
                        : 'bg-white text-[#253f61] hover:bg-[#f0f4fa] hover:text-[#253f61]'}`}
            >
                <Icon className="w-5 h-5" />
                {!closeSidebar && (
                    <div className="flex items-center justify-between flex-row">
                        <div className="text-sm font-medium font-bevietnam leading-none whitespace-nowrap flex-shrink-0">
                            {text}
                        </div>
                        {icon2}
                    </div>

                )}
            </button>
        </div>
    );
}

const AdminSidebar = () => {
    const closeSidebar = useSelector(state => state.sidebar.closeSidebar);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const handleLogout = async () => {
        await dispatch(logout());
        navigate('/login');
    };

    return (
        <div className={`fixed left-0 flex flex-col min-h-screen justify-between bg-white ${closeSidebar ? '' : 'w-[14rem]'} p-2 shadow-[0px_1px_8px_2px_rgba(20,20,20,0.08)] z-[40]`}>
            <button
                onClick={() => dispatch(toggleCloseSidebar())}
                className="absolute top-1/2 -right-4 transform -translate-y-1/2 bg-white border rounded-full p-1 shadow-md hover:bg-gray-100 transition"
            >
                {closeSidebar ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
            </button>

            <div className="flex-col w-full justify-start items-start gap-5 inline-flex">
                <HeaderSidebar />
                <div className="flex flex-col gap-2 w-full">
                    <MenuSidebar
                        onClick={() => navigate('/admin')}
                        route={'/admin'}
                        Icon={BarChart3}
                        text={'Dashboard'}
                        role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT, UserType.ASSISTANT, UserType.MARKETING]}
                    />

                    <div className="flex flex-col gap-2 overflow-y-auto max-h-[calc(100vh-400px)]">
                        <MenuSidebar
                            onClick={() => navigate('/admin/student-management')}
                            route={'/admin/student-management'}
                            Icon={Users}
                            text={'Học sinh'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT, UserType.ASSISTANT]}

                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/staff-management')}
                            route={'/admin/staff-management'}
                            Icon={UserCog}
                            text={'Nhân viên'}
                            role={[UserType.ADMIN, UserType.HUMANRESOURCEMANAGEMENT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/log-activity')}
                            route={'/admin/log-activity'}
                            Icon={Activity}
                            text={'Nhật ký hoạt động'}
                            role={[UserType.ADMIN, UserType.HUMANRESOURCEMANAGEMENT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/class-management')}
                            route={'/admin/class-management'}
                            Icon={School}
                            text={'Lớp học'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.HUMANRESOURCEMANAGEMENT, UserType.ASSISTANT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/question-management')}
                            route={'/admin/question-management'}
                            Icon={ClipboardList}
                            text={'Câu hỏi'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.ASSISTANT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/question-report-management')}
                            route={'/admin/question-report-management'}
                            Icon={FileSearch}
                            text={'Báo cáo câu hỏi'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.ASSISTANT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/exam-management')}
                            route={'/admin/exam-management'}
                            Icon={FileText}
                            text={'Đề thi'}
                            role={[UserType.ADMIN, UserType.TEACHER, UserType.CLASSMANAGEMENT, UserType.ASSISTANT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/code-management')}
                            route={'/admin/code-management'}
                            Icon={Code}
                            text={'Code'}
                            role={[UserType.ADMIN]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/achievement-management')}
                            route={'/admin/achievement-management'}
                            Icon={Trophy}
                            text={'Thành tích'}
                            role={[UserType.ADMIN, UserType.MARKETING]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/tuition-payment')}
                            route={'/admin/tuition-payment'}
                            Icon={CreditCard}
                            text={'Học phí'}
                            role={[UserType.ADMIN, UserType.HUMANRESOURCEMANAGEMENT]}
                        />
                        <MenuSidebar
                            onClick={() => navigate('/admin/homepage-management')}
                            route={'/admin/homepage-management'}
                            Icon={Globe}
                            text={'Trang chủ'}
                            role={[UserType.ADMIN, UserType.MARKETING]}
                        />
                    </div>
                </div>
            </div>
            <hr className="border-t border-gray-200" />
            <div className="flex-col w-full justify-start items-start gap-3 inline-flex">
                {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon3} text={'Thông báo'} icon2={notification} /> */}
                {/* <MenuSidebar onClick={() => navigate('/')} route={'/'} icon={icon4} text={'Trợ giúp'} /> */}
                <MenuSidebar onClick={handleLogout} route={'/login'} Icon={LogOut} text={'Đăng xuất'} />
                <MenuSidebar onClick={() => navigate('/overview')} route={'/overview'} Icon={Home} text={'Trang học sinh'} />
                <UserSidebar />
            </div>
        </div>
    );
}

export default AdminSidebar;
