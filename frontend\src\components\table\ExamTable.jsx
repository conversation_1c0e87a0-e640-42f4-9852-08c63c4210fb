import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import LoadingSpinner from "../loading/LoadingSpinner";
import ConfirmDeleteModal from "../modal/ConfirmDeleteModal";
import { setSortOrder, downloadQuestionsDocx } from "../../features/exam/examSlice";
import TooltipTd from "./TooltipTd";
import { fetchCodesByType } from "../../features/code/codeSlice";
import { useNavigate } from "react-router-dom";
import { deleteExam } from "../../features/exam/examSlice";
import { TotalComponent } from "./TotalComponent";
import LoadingData from "../loading/LoadingData";
import DownloadIcon from "../icons/DownloadIcon";

const ExamTable = ({ exams, fetchExams }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { loading } = useSelector(state => state.states);
    const [isOpenConfirmDeleteModal, setIsOpenConfirmDeleteModal] = useState(false);
    const { pagination, search, downloadingExamIds } = useSelector(state => state.exams);

    const { page: currentPage, pageSize: limit, sortOrder, total } = pagination;


    const [deleteMode, setDeleteMode] = useState(false);
    const { codes } = useSelector((state) => state.codes);
    const [id, setId] = useState(null);

    const confirmDeleteModal = () => {
        if (id === null) return;
        dispatch(deleteExam(id))
            .unwrap()
            .then(() => {
                dispatch(fetchExams(({ search, currentPage, limit, sortOrder }))).unwrap()
                setIsOpenConfirmDeleteModal(false);
            });
    }

    const handleClickedRow = (id) => {
        if (deleteMode) {
            setIsOpenConfirmDeleteModal(true);
            setId(id);
        } else {
            navigate(`/admin/exam-management/${id}`);
        }
    };

    const handleDownloadDocx = async (examId, examName, event) => {
        event.stopPropagation(); // Ngăn việc click row
        
        try {
            await dispatch(downloadQuestionsDocx(examId)).unwrap();
            // Thông báo thành công (có thể thêm toast notification)
            console.log(`Downloaded DOCX for exam: ${examName}`);
        } catch (error) {
            console.error('Error downloading DOCX:', error);
            // Thông báo lỗi (có thể thêm toast notification)
            alert('Có lỗi xảy ra khi tải file. Vui lòng thử lại!');
        }
    };

    useEffect(() => {
        dispatch(fetchCodesByType(["chapter", "exam type", "year", "grade", "difficulty", "question type"]))
    }, [dispatch]);

    return (
        <LoadingData
            loading={loading}
            isNoData={exams.length > 0 ? false : true}
            loadText="Đang tải danh sách đề thi"
            noDataText="Không có đề thi nào."
        >
            <div className="flex flex-col text-sm gap-4 h-full min-h-0">
                <ConfirmDeleteModal
                    isOpen={isOpenConfirmDeleteModal}
                    onClose={() => setIsOpenConfirmDeleteModal(false)}
                    onConfirm={confirmDeleteModal}
                />
                <TotalComponent
                    total={total}
                    page={currentPage}
                    pageSize={limit}
                    setSortOrder={() => dispatch(setSortOrder())}
                    deleteMode={deleteMode}
                    isDelete={true}
                    setDeleteMode={setDeleteMode}
                />
                <div className="flex-grow h-[70vh] overflow-y-auto hide-scrollbar">
                    <table className="w-full border-collapse border border-[#E7E7ED]">
                        <thead className="bg-[#F6FAFD] sticky top-0 z-10">
                            <tr className="border border-[#E7E7ED]">
                                <th className="py-3 w-16 uppercase">ID</th>
                                <th className="py-3 w-64 uppercase">Tên</th>
                                <th className="py-3 w-16 uppercase">Kiểu</th>
                                <th className="py-3 w-16 uppercase">Lớp</th>
                                <th className="py-3 w-30 uppercase">Chương</th>
                                <th className="py-3 w-20 uppercase">Năm</th>
                                <th className="py-3 w-16 uppercase">Lời giải</th>
                                <th className="py-3 w-20 uppercase">CK</th>
                                <th className="py-3 w-30 uppercase">Ngày đăng</th>
                                <th className="py-3 w-30 uppercase">Cập nhật lúc</th>
                                <th className="py-3 w-24 uppercase">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            {exams.map((exam, index) => (
                                <tr
                                    onClick={() => handleClickedRow(exam.id)}
                                    key={exam.id} className={`border border-[#E7E7ED] cursor-pointer ${deleteMode ? 'hover:bg-red-50' : 'hover:bg-gray-50'}`}>
                                    <td className="py-3 text-center">{exam.id}</td>
                                    <TooltipTd
                                        value={exam.name}
                                        tooltipText={exam.name}
                                        imageUrl={exam.imageUrl}
                                    />
                                    <TooltipTd
                                        value={exam.typeOfExam}
                                        tooltipText={
                                            codes['exam type']?.find((code) => code.code === exam.typeOfExam)?.description || ""
                                        }
                                    />
                                    <td className="py-3 text-center">{exam.class}</td>
                                    <TooltipTd
                                        value={exam.chapter}
                                        tooltipText={
                                            codes['chapter']?.find((code) => code.code === exam.chapter)?.description || ""
                                        }
                                    />
                                    <td className="py-3 text-center">{exam.year}</td>
                                    <TooltipTd
                                        value={exam.solutionUrl ? "Rồi" : 'Chưa'}
                                        className={`${exam.solutionUrl ? 'text-green-500 font-semibold' : 'text-yellow-500 font-semibold'}`}
                                        tooltipText={
                                            exam.solutionUrl
                                        }
                                    />
                                    <td className={`py-3 text-center ${exam.public ? 'text-green-500 font-semibold' : 'text-red-500 font-semibold'}`}>{exam.public ? 'Có' : 'Không'}</td>
                                    <td className="py-3 text-center">{new Date(exam.createdAt).toLocaleDateString()}</td>
                                    <td className="py-3 text-center">{new Date(exam.updatedAt).toLocaleDateString()}</td>
                                    <td className="py-3 text-center">
                                        <div className="flex justify-center items-center space-x-2">
                                            <button
                                                onClick={(e) => handleDownloadDocx(exam.id, exam.name, e)}
                                                disabled={downloadingExamIds.includes(exam.id)}
                                                className={`
                                                    p-2 rounded-md transition-all duration-200 
                                                    ${downloadingExamIds.includes(exam.id) 
                                                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                                                        : 'bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700'
                                                    }
                                                `}
                                                title={downloadingExamIds.includes(exam.id) ? "Đang tải..." : "Tải file Word câu hỏi"}
                                            >
                                                {downloadingExamIds.includes(exam.id) ? (
                                                    <div className="w-5 h-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                                                ) : (
                                                    <DownloadIcon className="w-5 h-5" />
                                                )}
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </LoadingData>

    )
}

export default ExamTable;