import * as XLSX from 'xlsx';

/**
 * Export attempt data to Excel
 * @param {Array} attempts - Array of attempt objects
 * @param {String} examName - Name of the exam
 */
export const exportAttemptsToExcel = (attempts, examName) => {
    if (!attempts || !Array.isArray(attempts) || attempts.length === 0) {
        console.error('No data to export');
        return;
    }

    try {
        // Create worksheet data
        const worksheetData = attempts.map((attempt, index) => {
            return {
                'STT': index + 1,
                'Họ và tên': attempt?.student ? `${attempt.student.lastName} ${attempt.student.firstName}` : 'N/A',
                'Trường': attempt?.student?.highSchool || 'N/A',
                'Lớp': attempt?.student?.class || 'N/A',
                'Email': attempt?.student?.email || 'N/A',
                'Số điện thoại': attempt?.student?.phone || 'N/A',
                'Thời gian bắt đầu': attempt?.startTime ? new Date(attempt.startTime).toLocaleString('vi-VN') : 'N/A',
                'Thời gian kết thúc': attempt?.endTime ? new Date(attempt.endTime).toLocaleString('vi-VN') : 'Chưa nộp',
                'Thời gian làm bài': attempt?.duration || 'Chưa nộp',
                'Điểm': attempt?.score !== undefined ? attempt.score : 'Chưa có',
                'Số câu đúng': attempt?.correctCount !== undefined ? attempt.correctCount : 'N/A',
                'Tổng số câu': attempt?.totalCount !== undefined ? attempt.totalCount : 'N/A',
            };
        });

        // Create worksheet
        const worksheet = XLSX.utils.json_to_sheet(worksheetData);

        // Set column widths
        const columnWidths = [
            { wch: 5 },  // STT
            { wch: 25 }, // Họ và tên
            { wch: 25 }, // Trường
            { wch: 10 }, // Lớp
            { wch: 25 }, // Email
            { wch: 15 }, // Số điện thoại
            { wch: 20 }, // Thời gian bắt đầu
            { wch: 20 }, // Thời gian kết thúc
            { wch: 15 }, // Thời gian làm bài
            { wch: 10 }, // Điểm
            { wch: 10 }, // Số câu đúng
            { wch: 10 }, // Tổng số câu
        ];
        worksheet['!cols'] = columnWidths;

        // Create workbook
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Danh sách bài làm');

        // Generate filename
        const sanitizedExamName = examName ? examName.replace(/[\\/:*?"<>|]/g, '_') : 'exam';
        const fileName = `Danh_sach_bai_lam_${sanitizedExamName}_${new Date().toISOString().slice(0, 10)}.xlsx`;

        // Export to file
        XLSX.writeFile(workbook, fileName);

        return fileName;
    } catch (error) {
        console.error('Error exporting to Excel:', error);
        throw error;
    }
};

/**
 * Export attendance data to Excel
 * @param {Array} attendances - Array of attendance objects
 * @param {String} lessonName - Name of the lesson
 * @param {String} className - Name of the class
 */
export const exportAttendancesToExcel = (attendances, lessonName, className) => {
    if (!attendances || !Array.isArray(attendances) || attendances.length === 0) {
        console.error('No attendance data to export');
        return;
    }

    try {
        // Create worksheet data
        const worksheetData = attendances.map((attendance, index) => {
            // Helper function to get status text
            const getStatusText = (status) => {
                switch (status) {
                    case 'present': return 'Có mặt';
                    case 'absent': return 'Vắng mặt';
                    case 'late': return 'Đi muộn';
                    default: return 'N/A';
                }
            };

            // Helper function to get BTVN status text
            // Escape để tránh chèn HTML nếu bạn render ra DOM (innerHTML)
            const escapeHtml = (s) =>
                String(s)
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");

            const getBTVNStatusText = (btvnStatuses) => {
                // Hỗ trợ cả Array lẫn Object; reject các kiểu khác
                const arr = Array.isArray(btvnStatuses)
                    ? btvnStatuses
                    : (btvnStatuses && typeof btvnStatuses === "object")
                        ? Object.values(btvnStatuses)
                        : [];

                if (arr.length === 0) return "Không có BTVN";

                const parts = arr
                    .filter((item) => item && typeof item === "object") // bỏ null/primitive
                    .map((status) => {
                        // Tên bài: ưu tiên btvnInfo.name, kèm lesson.name nếu có
                        const nameRaw =
                            (status.btvnInfo && typeof status.btvnInfo === "object" && status.btvnInfo.name) ||
                            "Unknown";
                        const lessonName =
                            status?.btvnInfo?.lesson && typeof status.btvnInfo.lesson === "object"
                                ? status.btvnInfo.lesson.name
                                : undefined;

                        const name =
                            escapeHtml(
                                lessonName ? `${nameRaw} - ${lessonName}` : nameRaw
                            );

                        // Trạng thái: nếu không rõ, hiển thị "Không rõ"
                        const isDone = status?.isDone;
                        const statusText =
                            typeof isDone === "boolean"
                                ? isDone ? "Hoàn thành" : "Chưa hoàn thành"
                                : "Không rõ";

                        // Điểm: chấp nhận 0; chỉ hiển thị nếu là số hữu hạn
                        const score = status?.score;
                        const hasValidScore = Number.isFinite(Number(score));
                        const scoreText = hasValidScore ? ` - Điểm: ${Number(score)}` : "";

                        return `${name}: ${statusText}${scoreText}`;
                    })
                    .filter(Boolean);

                return parts.length ? parts.join(", ") : "Không có BTVN";
            };

            // Helper function to format weekly attendance
            const getWeeklyAttendanceText = (weeklyAttendances, currentAttendance, lessonName, className) => {
                const attendanceTexts = [];

                // Thêm buổi học hiện tại nếu status là "present"
                if (currentAttendance?.status === 'present') {
                    const currentLessonName = lessonName || 'Buổi học hiện tại';
                    const currentClassName = className || '';
                    const currentStatusText = getStatusText(currentAttendance.status);
                    const currentLessonDay = currentAttendance?.attendanceTime ? 
                        new Date(currentAttendance.attendanceTime).toLocaleDateString('vi-VN') : '';
                    
                    attendanceTexts.push(
                        `${currentLessonName}${currentClassName ? ` (${currentClassName})` : ''} - ${currentStatusText}${currentLessonDay ? ` (${currentLessonDay})` : ''}`
                    );
                }

                // Thêm các buổi học khác trong tuần
                if (weeklyAttendances && Array.isArray(weeklyAttendances) && weeklyAttendances.length > 0) {
                    const otherAttendances = weeklyAttendances.map(att => {
                        const lessonName = att?.lesson?.name || 'N/A';
                        const className = att?.lesson?.class?.name || '';
                        const statusText = getStatusText(att?.status);
                        const lessonDay = att?.lesson?.day ? new Date(att.lesson.day).toLocaleDateString('vi-VN') : '';
                        
                        return `${lessonName}${className ? ` (${className})` : ''} - ${statusText}${lessonDay ? ` (${lessonDay})` : ''}`;
                    });
                    
                    attendanceTexts.push(...otherAttendances);
                }

                if (attendanceTexts.length === 0) {
                    return 'Không có buổi học trong tuần';
                }

                // Sử dụng xuống dòng thay vì dấu ; để các buổi học hiển thị theo chiều dọc
                return attendanceTexts.join('\n');
            };


            const calculateAttendanceInWeek = (weeklyAttendances, status) => {
                let count = 0;
                if (!weeklyAttendances || !Array.isArray(weeklyAttendances) || weeklyAttendances.length === 0) {
                    return 0;
                }
                count = weeklyAttendances.length;
                if (status === 'present') {
                    count++
                }
                return count;
            };

            return {
                'STT': index + 1,
                'Họ và tên': attendance?.user?.fullName ||
                    (attendance?.user ? `${attendance.user.lastName || ''} ${attendance.user.firstName || ''}`.trim() : 'N/A'),
                'Trường': attendance?.user?.highSchool || 'N/A',
                'Lớp': attendance?.user?.class || 'N/A',
                'Số điện thoại': attendance?.user?.phone || 'N/A',
                'Tài khoản': attendance?.user?.username || 'N/A',
                'Mật khẩu': attendance?.user?.password || 'N/A',
                'Trạng thái điểm danh': getStatusText(attendance?.status),
                'Trạng thái BTVN': getBTVNStatusText(attendance?.btvnStatuses),
                'Thời gian điểm danh': attendance?.attendanceTime ? new Date(attendance.attendanceTime).toLocaleString('vi-VN') : 'N/A',
                'Ghi chú': attendance?.note || '',
                'Số buổi học trong tuần' : calculateAttendanceInWeek(attendance?.weeklyAttendances, attendance?.status),
                'Điểm danh tuần': getWeeklyAttendanceText(attendance?.weeklyAttendances, attendance, lessonName, className),
                'Ngày tạo': attendance?.createdAt ? new Date(attendance.createdAt).toLocaleString('vi-VN') : 'N/A',
                'Ngày cập nhật': attendance?.updatedAt ? new Date(attendance.updatedAt).toLocaleString('vi-VN') : 'N/A',
            };
        });

        // Create worksheet
        const worksheet = XLSX.utils.json_to_sheet(worksheetData);

        // Set column widths
        const columnWidths = [
            { wch: 5 },  // STT
            { wch: 25 }, // Họ và tên
            { wch: 25 }, // Trường
            { wch: 10 }, // Lớp
            { wch: 15 }, // Số điện thoại
            { wch: 15 }, // Tài khoản
            { wch: 15 }, // Mật khẩu
            { wch: 15 }, // Trạng thái điểm danh
            { wch: 30 }, // Trạng thái BTVN
            { wch: 20 }, // Thời gian điểm danh
            { wch: 30 }, // Ghi chú
            { wch: 10 }, // Số buổi học trong tuần (tăng width để chứa nhiều thông tin hơn)
            { wch: 50 }, // Điểm danh tuần (tăng width để chứa nhiều thông tin hơn)
            { wch: 20 }, // Ngày tạo
            { wch: 20 }, // Ngày cập nhật
        ];
        worksheet['!cols'] = columnWidths;

        // Thiết lập wrap text cho tất cả các cell để hiển thị xuống dòng
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        for (let R = range.s.r; R <= range.e.r; ++R) {
            for (let C = range.s.c; C <= range.e.c; ++C) {
                const cell_address = XLSX.utils.encode_cell({ r: R, c: C });
                if (!worksheet[cell_address]) continue;
                
                // Thiết lập wrap text cho tất cả các cell
                if (!worksheet[cell_address].s) worksheet[cell_address].s = {};
                worksheet[cell_address].s.alignment = {
                    wrapText: true,
                    vertical: 'top'
                };
                
                // Đặc biệt thiết lập cho cột "Điểm danh tuần" (cột thứ 13, index 12)
                if (C === 12) {
                    worksheet[cell_address].s.alignment = {
                        wrapText: true,
                        vertical: 'top',
                        horizontal: 'left'
                    };
                }
            }
        }

        // Create workbook
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Danh sách điểm danh');

        // Generate filename
        const sanitizedLessonName = lessonName ? lessonName.replace(/[\\/:*?"<>|]/g, '_') : 'lesson';
        const sanitizedClassName = className ? className.replace(/[\\/:*?"<>|]/g, '_') : 'class';
        const fileName = `Diem_danh_${sanitizedClassName}_${sanitizedLessonName}_${new Date().toISOString().slice(0, 10)}.xlsx`;

        // Export to file
        XLSX.writeFile(workbook, fileName);

        return fileName;
    } catch (error) {
        console.error('Error exporting attendance to Excel:', error);
        throw error;
    }
};

/**
 * Export students data to Excel
 * @param {Array} students - Array of student objects
 * @param {Object} filters - Filter options applied
 */
export const exportStudentsToExcel = (students, filters = {}) => {
    if (!students || !Array.isArray(students) || students.length === 0) {
        console.error('No student data to export');
        return;
    }

    try {
        const worksheetData = students.map((student, index) => {
            // Gộp tên các lớp học sinh đã tham gia
            const classNames = Array.isArray(student.classStatuses)
                ? student.classStatuses
                    .map(cs => cs.class?.name)
                    .filter(Boolean)
                    .join(', ')
                : 'Chưa tham gia lớp nào';

            return {
                'ID': student?.id || 'N/A',
                'Họ và tên': student?.lastName && student?.firstName
                    ? `${student.lastName} ${student.firstName}`
                    : 'N/A',
                'Trường học': student?.highSchool || 'N/A',
                'Khối lớp': student?.class || 'N/A',
                'Năm tốt nghiệp': student?.graduationYear || 'Chưa cập nhật',
                'Số điện thoại': student?.phone || 'N/A',
                'Tài khoản': student?.username || 'N/A',
                'Mât khẩu': student?.password || 'N/A',
                'Trạng thái': student?.isActive ? 'Hoạt động' : 'Không hoạt động',
                'Lớp đã tham gia': classNames,
                'Ngày tham gia': student?.createdAt ? new Date(student.createdAt).toLocaleString('vi-VN') : 'N/A',
                'Cập nhật lần cuối': student?.updatedAt ? new Date(student.updatedAt).toLocaleString('vi-VN') : 'N/A',
            };
        });

        const worksheet = XLSX.utils.json_to_sheet(worksheetData);

        const columnWidths = [
            { wch: 5 },   // ID
            { wch: 25 },  // Họ và tên
            { wch: 25 },  // Trường học
            { wch: 10 },  // Khối lớp
            { wch: 15 },  // Năm tốt nghiệp
            { wch: 15 },  // Số điện thoại
            { wch: 20 },  // Tài khoản
            { wch: 15 },  // Mật khẩu
            { wch: 15 },  // Trạng thái
            { wch: 30 },  // Lớp đã tham gia
            { wch: 20 },  // Ngày tham gia
            { wch: 20 },  // Cập nhật cuối
        ];
        worksheet['!cols'] = columnWidths;

        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Danh sách học sinh');

        let filenameParts = ['Danh_sach_hoc_sinh'];
        if (filters.graduationYear) filenameParts.push(`nam_${filters.graduationYear}`);
        if (filters.classFilter) filenameParts.push(`khoi_${filters.classFilter}`);
        filenameParts.push(new Date().toISOString().slice(0, 10));

        const fileName = `${filenameParts.join('_')}.xlsx`;
        XLSX.writeFile(workbook, fileName);

        return fileName;
    } catch (error) {
        console.error('Error exporting students to Excel:', error);
        throw error;
    }
};
