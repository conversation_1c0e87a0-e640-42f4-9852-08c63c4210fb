import { useEffect } from "react";
import { useDispatch } from "react-redux";
import AdminLayout from "../../../layouts/AdminLayout";
import AdminActivityTable from "../../../components/table/AdminActivityTable";
import AdminActivitySearch from "../../../components/search/AdminActivitySearch";
import DateRangeFilter from "../../../components/filter/DateRangeFilter";
import { clearError } from "../../../features/adminActivityLog/adminActivityLogSlice";
import { Activity } from "lucide-react";

const LogAdminActivityPage = () => {
    const dispatch = useDispatch();

    useEffect(() => {
        // Clear any previous errors when component mounts
        dispatch(clearError());
    }, [dispatch]);

    return (
        <AdminLayout
            title="Nhật ký hoạt động Admin"
            subtitle="<PERSON> tất cả hoạt động của các quản trị viên trong hệ thống"
        >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                {/* Header */}
                <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center gap-3 mb-4">
                        <div className="p-2 bg-blue-100 rounded-lg">
                            <Activity className="text-blue-600" size={24} />
                        </div>
                        <div>
                            <h1 className="text-xl font-semibold text-gray-900">
                                Nhật ký hoạt động Admin
                            </h1>
                            <p className="text-gray-600 text-sm">
                                Xem và theo dõi tất cả các hoạt động của quản trị viên
                            </p>
                        </div>
                    </div>

                    {/* Filters */}
                    <div className="flex flex-col lg:flex-row gap-4">
                        {/* Search */}
                        <div className="flex flex-col w-full gap-3">
                            <AdminActivitySearch />
                            {/* Date Range Filter */}
                            <DateRangeFilter />
                        </div>


                    </div>
                </div>

                {/* Content */}
                <div className="p-6">
                    <AdminActivityTable />
                </div>
            </div>
        </AdminLayout>
    );
};

export default LogAdminActivityPage;