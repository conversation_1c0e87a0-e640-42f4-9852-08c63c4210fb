import React, { useState, useEffect } from 'react';
import { Calendar, Clock, GraduationCap, ChevronLeft, ChevronRight, CheckCircle, AlertCircle } from 'lucide-react';
import ClassDetailModal from './modal/ClassDetailModal';
import LearningItemDetailModal from './modal/LearningItemDetailModal';

const ModernSchedule = ({ classes = [], learningItems = [] }) => {
    const [currentWeekOffset, setCurrentWeekOffset] = useState(0);
    const [viewMode, setViewMode] = useState('week'); // 'week' or 'day'
    const [selectedDay, setSelectedDay] = useState(0); // For day view
    const [isUserNavigating, setIsUserNavigating] = useState(false); // Track user navigation
    
    // Modal states
    const [classModalOpen, setClassModalOpen] = useState(false);
    const [learningItemModalOpen, setLearningItemModalOpen] = useState(false);
    const [selectedClass, setSelectedClass] = useState(null);
    const [selectedLearningItem, setSelectedLearningItem] = useState(null);

    // Handle class click
    const handleClassClick = (classData) => {
        setSelectedClass(classData);
        setClassModalOpen(true);
    };

    // Handle learning item click
    const handleLearningItemClick = (learningItem, classData = null) => {
        setSelectedLearningItem(learningItem);
        setSelectedClass(classData);
        setLearningItemModalOpen(true);
    };

    // Get current week based on offset
    const getWeekDays = (weekOffset = 0) => {
        const today = new Date();
        const monday = new Date(today);
        const currentDay = today.getDay();
        const diffToMonday = (currentDay + 6) % 7;
        monday.setDate(today.getDate() - diffToMonday + (weekOffset * 7));

        const labels = [
            { short: 'T2', long: 'Thứ Hai' },
            { short: 'T3', long: 'Thứ Ba' },
            { short: 'T4', long: 'Thứ Tư' },
            { short: 'T5', long: 'Thứ Năm' },
            { short: 'T6', long: 'Thứ Sáu' },
            { short: 'T7', long: 'Thứ Bảy' },
            { short: 'CN', long: 'Chủ Nhật' },
        ];

        return labels.map((item, index) => {
            const date = new Date(monday);
            date.setDate(monday.getDate() + index);

            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const isToday = date.toDateString() === new Date().toDateString();

            return {
                ...item,
                date: `${day}/${month}`,
                fullDate: date,
                isToday
            };
        });
    };

    const weekDays = getWeekDays(currentWeekOffset);

    // Auto select today when component mounts or week changes
    useEffect(() => {
        // Only auto-select if user is not actively navigating
        if (!isUserNavigating) {
            const todayIndex = weekDays.findIndex(day => day.isToday);
            if (todayIndex !== -1) {
                // If today is in current week, select it
                setSelectedDay(todayIndex);
            } else if (currentWeekOffset === 0) {
                // If this is the first render and today is not in current week, 
                // still set today's day of week index
                const today = new Date();
                const currentDay = today.getDay();
                const todayIndex = currentDay === 0 ? 6 : currentDay - 1;
                setSelectedDay(todayIndex);
            }
        }
        // Reset navigation flag after a delay to allow future auto-selection
        if (isUserNavigating) {
            const timer = setTimeout(() => {
                setIsUserNavigating(false);
            }, 1000);
            return () => clearTimeout(timer);
        }
    }, [currentWeekOffset, weekDays, isUserNavigating]);

    // Format date for comparison
    const formatDate = (dateStr) => {
        const d = new Date(dateStr);
        return `${String(d.getDate()).padStart(2, '0')}/${String(d.getMonth() + 1).padStart(2, '0')}`;
    };

    // Get classes for a specific day
    const getClassesForDay = (dayShort) => {
        return classes.filter(cls =>
            cls.status === 'LHD' &&
            cls.public == true &&
            (
                (cls.dayOfWeek1 === dayShort && cls.startTime1 && cls.endTime1) ||
                (cls.dayOfWeek2 === dayShort && cls.startTime2 && cls.endTime2) ||
                (cls.dayOfWeek === dayShort && cls.startTime && cls.endTime)
            )
        );
    };

    // Get learning items for a specific day
    const getLearningItemsForDay = (date) => {
        return learningItems.filter(item =>
            formatDate(item.learningItem.deadline) === date
        );
    };

    // Get class color based on name
    const getClassColor = (className, index = 0) => {
        const lowerName = className.toLowerCase();
        let grade = null;

        if (lowerName.includes('10')) grade = '10';
        else if (lowerName.includes('11')) grade = '11';
        else if (lowerName.includes('12')) grade = '12';

        const colorMap = {
            '10': {
                đề: 'bg-blue-50 border-blue-200 text-blue-800',
                đại: 'bg-indigo-50 border-indigo-200 text-indigo-800',
                hình: 'bg-emerald-50 border-emerald-200 text-emerald-800',
            },
            '11': {
                đề: 'bg-orange-50 border-orange-200 text-orange-800',
                đại: 'bg-cyan-50 border-cyan-200 text-cyan-800',
                hình: 'bg-teal-50 border-teal-200 text-teal-800',
            },
            '12': {
                đề: 'bg-purple-50 border-purple-200 text-purple-800',
                đại: 'bg-lime-50 border-lime-200 text-lime-800',
                hình: 'bg-amber-50 border-amber-200 text-amber-800',
            }
        };

        if (grade) {
            if (lowerName.includes('đề')) return colorMap[grade]['đề'];
            if (lowerName.includes('đại')) return colorMap[grade]['đại'];
            if (lowerName.includes('hình')) return colorMap[grade]['hình'];
        }

        const defaultColors = [
            'bg-gray-50 border-gray-200 text-gray-800',
            'bg-slate-50 border-slate-200 text-slate-800',
            'bg-zinc-50 border-zinc-200 text-zinc-800',
        ];
        return defaultColors[index % defaultColors.length];
    };

    // Process classes for display
    const processClassesForDay = (dayShort) => {
        const dayClasses = getClassesForDay(dayShort);
        const processedClasses = [];

        dayClasses.forEach((cls) => {
            // Handle session 1
            if (cls.dayOfWeek1 === dayShort && cls.startTime1 && cls.endTime1) {
                processedClasses.push({
                    ...cls,
                    startTime: cls.startTime1.substring(0, 5),
                    endTime: cls.endTime1.substring(0, 5),
                    sessionNumber: 1,
                    id: `${cls.id}-session1`
                });
            }
            // Handle session 2
            if (cls.dayOfWeek2 === dayShort && cls.startTime2 && cls.endTime2) {
                processedClasses.push({
                    ...cls,
                    startTime: cls.startTime2.substring(0, 5),
                    endTime: cls.endTime2.substring(0, 5),
                    sessionNumber: 2,
                    id: `${cls.id}-session2`
                });
            }
            // Handle legacy format
            if (cls.dayOfWeek === dayShort && cls.startTime && cls.endTime) {
                processedClasses.push({
                    ...cls,
                    startTime: cls.startTime.substring(0, 5),
                    endTime: cls.endTime.substring(0, 5),
                    sessionNumber: null,
                    id: `${cls.id}-legacy`
                });
            }
        });

        // Sort by start time
        return processedClasses.sort((a, b) => {
            const [aHour, aMin] = a.startTime.split(':').map(Number);
            const [bHour, bMin] = b.startTime.split(':').map(Number);
            return (aHour * 60 + aMin) - (bHour * 60 + bMin);
        });
    };

    // Week navigation
    const navigateWeek = (direction) => {
        setIsUserNavigating(true);
        setCurrentWeekOffset(prev => prev + direction);
    };

    // Day navigation with automatic week change
    const navigateDay = (direction) => {
        setIsUserNavigating(true);
        const newSelectedDay = selectedDay + direction;
        
        if (newSelectedDay < 0) {
            // Go to previous week, select last day (Sunday)
            setCurrentWeekOffset(prev => prev - 1);
            setSelectedDay(6);
        } else if (newSelectedDay > 6) {
            // Go to next week, select first day (Monday)
            setCurrentWeekOffset(prev => prev + 1);
            setSelectedDay(0);
        } else {
            // Stay in current week
            setSelectedDay(newSelectedDay);
        }
    };

    // Get current week range for display
    const getWeekRange = () => {
        const firstDay = weekDays[0];
        const lastDay = weekDays[6];
        return `${firstDay.date} - ${lastDay.date}`;
    };

    // Responsive view toggle
    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth < 768) {
                setViewMode('day');
            } else {
                setViewMode('week');
            }
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            {/* Header */}
            <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Calendar size={20} className="text-gray-600" />
                        <h3 className="font-semibold text-gray-900">
                            {viewMode === 'week' ? `Tuần ${getWeekRange()}` : weekDays[selectedDay]?.long + ' ' + weekDays[selectedDay]?.date}
                        </h3>
                    </div>

                    <div className="flex items-center gap-2">
                        {/* View toggle for desktop */}
                        <div className="hidden md:flex bg-white rounded-md border border-gray-200 p-1">
                            <button
                                onClick={() => setViewMode('week')}
                                className={`px-3 py-1 text-xs rounded transition-colors ${viewMode === 'week'
                                        ? 'bg-gray-900 text-white'
                                        : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                            >
                                Tuần
                            </button>
                            <button
                                onClick={() => setViewMode('day')}
                                className={`px-3 py-1 text-xs rounded transition-colors ${viewMode === 'day'
                                        ? 'bg-gray-900 text-white'
                                        : 'text-gray-600 hover:bg-gray-100'
                                    }`}
                            >
                                Ngày
                            </button>
                        </div>

                        {/* Navigation */}
                        <div className="flex items-center gap-1">
                            <button
                                onClick={() => viewMode === 'week' ? navigateWeek(-1) : navigateDay(-1)}
                                className="p-1.5 rounded-md hover:bg-gray-200 transition-colors"
                            >
                                <ChevronLeft size={16} className="text-gray-600" />
                            </button>
                            <button
                                onClick={() => viewMode === 'week' ? navigateWeek(1) : navigateDay(1)}
                                className="p-1.5 rounded-md hover:bg-gray-200 transition-colors"
                            >
                                <ChevronRight size={16} className="text-gray-600" />
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="p-4">
                {viewMode === 'week' ? (
                    // Week View
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                        {weekDays.map((day, index) => {
                            const dayClasses = processClassesForDay(day.short);
                            const dayLearningItems = getLearningItemsForDay(day.date);

                            return (
                                <div
                                    key={index}
                                    className={`bg-gray-50 rounded-lg p-3 border transition-all duration-200 hover:shadow-sm ${day.isToday ? 'border-cyan-300 bg-cyan-50' : 'border-gray-200'
                                        }`}
                                >
                                    {/* Day header */}
                                    <div className="text-center mb-3">
                                        <p className={`font-medium ${day.isToday ? 'text-cyan-700' : 'text-gray-900'}`}>
                                            {day.short}
                                        </p>
                                        <p className={`text-sm ${day.isToday ? 'text-cyan-600 font-medium' : 'text-gray-500'}`}>
                                            {day.date}
                                        </p>
                                    </div>

                                    {/* Learning Items */}
                                    <div className="space-y-1 mb-3">
                                        {dayLearningItems.map((item, itemIndex) => {
                                            const Icon = item.isDone ? CheckCircle : AlertCircle;
                                            const now = new Date();
                                            const deadline = new Date(item.learningItem.deadline);
                                            const isOverdue = !item.isDone && deadline < now;

                                            return (
                                                <div
                                                    key={itemIndex}
                                                    className={`p-1.5 rounded text-xs border-l-2 cursor-pointer hover:shadow-sm transition-all duration-200 ${item.isDone
                                                            ? 'bg-green-50 border-green-400 text-green-700 hover:bg-green-100'
                                                            : isOverdue
                                                                ? 'bg-red-50 border-red-400 text-red-700 hover:bg-red-100'
                                                                : 'bg-yellow-50 border-yellow-400 text-yellow-700 hover:bg-yellow-100'
                                                        }`}
                                                    onClick={() => handleLearningItemClick(item)}
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <Icon size={12} />
                                                        <span className="truncate">{item.learningItem.name}</span>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>

                                    {/* Classes */}
                                    <div className="space-y-2 mb-3">
                                        {dayClasses.map((cls, clsIndex) => (
                                            <div
                                                key={cls.id}
                                                className={`p-2 rounded-md border cursor-pointer transition-all duration-200 hover:shadow-sm ${getClassColor(cls.name, clsIndex)}`}
                                                onClick={() => handleClassClick(cls)}
                                            >
                                                <div className="flex items-center gap-2 mb-1">
                                                    <GraduationCap size={14} />
                                                    <p className="text-xs font-medium truncate">
                                                        {cls.name}{cls.sessionNumber ? ` (${cls.sessionNumber})` : ''}
                                                    </p>
                                                </div>
                                                <div className="flex items-center gap-1 text-xs opacity-75">
                                                    <Clock size={12} />
                                                    <span>{cls.startTime} - {cls.endTime}</span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>



                                    {/* Empty state */}
                                    {dayClasses.length === 0 && dayLearningItems.length === 0 && (
                                        <p className="text-center text-gray-400 text-xs py-4">Không có lịch học</p>
                                    )}
                                </div>
                            );
                        })}
                    </div>
                ) : (
                    // Day View (Mobile)
                    <div className="space-y-4">
                        {/* Day selector */}
                        <div className="flex gap-1 overflow-x-auto pb-2">
                            {weekDays.map((day, index) => (
                                <button
                                    key={index}
                                    onClick={() => {
                                        setIsUserNavigating(true);
                                        setSelectedDay(index);
                                    }}
                                    className={`flex-shrink-0 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${selectedDay === index
                                            ? 'bg-gray-900 text-white'
                                            : day.isToday
                                                ? 'bg-cyan-100 text-cyan-700 border border-cyan-300'
                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    <div className="text-center">
                                        <div>{day.short}</div>
                                        <div className="text-xs opacity-75">{day.date}</div>
                                    </div>
                                </button>
                            ))}
                        </div>

                        {/* Selected day content */}
                        {(() => {
                            const selectedDayData = weekDays[selectedDay];
                            const dayClasses = processClassesForDay(selectedDayData.short);
                            const dayLearningItems = getLearningItemsForDay(selectedDayData.date);

                            return (
                                <div className="space-y-4">
                                    {/* Classes */}
                                    {dayClasses.length > 0 && (
                                        <div>
                                            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                                                <GraduationCap size={16} />
                                                Lớp học
                                            </h4>
                                            <div className="space-y-3">
                                                {dayClasses.map((cls, clsIndex) => (
                                                    <div
                                                        key={cls.id}
                                                        className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md ${getClassColor(cls.name, clsIndex)}`}
                                                        onClick={() => handleClassClick(cls)}
                                                    >
                                                        <div className="flex justify-between items-start mb-2">
                                                            <h5 className="font-medium">
                                                                {cls.name}{cls.sessionNumber ? ` (Buổi ${cls.sessionNumber})` : ''}
                                                            </h5>
                                                            <div className="flex items-center gap-1 text-sm opacity-75">
                                                                <Clock size={14} />
                                                                <span>{cls.startTime} - {cls.endTime}</span>
                                                            </div>
                                                        </div>
                                                        {cls.description && (
                                                            <p className="text-sm opacity-75">{cls.description}</p>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}

                                    {/* Learning Items */}
                                    {dayLearningItems.length > 0 && (
                                        <div>
                                            <h4 className="font-medium text-gray-900 mb-3">Bài tập</h4>
                                            <div className="space-y-2">
                                                {dayLearningItems.map((item, itemIndex) => {
                                                    const Icon = item.isDone ? CheckCircle : AlertCircle;
                                                    const now = new Date();
                                                    const deadline = new Date(item.learningItem.deadline);
                                                    const isOverdue = !item.isDone && deadline < now;

                                                    return (
                                                        <div
                                                            key={itemIndex}
                                                            className={`p-3 rounded-lg border-l-4 cursor-pointer transition-all duration-200 hover:shadow-md ${item.isDone
                                                                    ? 'bg-green-50 border-green-400 hover:bg-green-100'
                                                                    : isOverdue
                                                                        ? 'bg-red-50 border-red-400 hover:bg-red-100'
                                                                        : 'bg-yellow-50 border-yellow-400 hover:bg-yellow-100'
                                                                }`}
                                                            onClick={() => handleLearningItemClick(item, null)}
                                                        >
                                                            <div className="flex items-center gap-2 mb-1">
                                                                <Icon size={16} className={
                                                                    item.isDone
                                                                        ? 'text-green-600'
                                                                        : isOverdue
                                                                            ? 'text-red-600'
                                                                            : 'text-yellow-600'
                                                                } />
                                                                <span className="font-medium">{item.learningItem.name}</span>
                                                            </div>
                                                            <p className={`text-sm ${item.isDone
                                                                    ? 'text-green-700'
                                                                    : isOverdue
                                                                        ? 'text-red-700'
                                                                        : 'text-yellow-700'
                                                                }`}>
                                                                {item.isDone
                                                                    ? 'Đã hoàn thành'
                                                                    : isOverdue
                                                                        ? 'Quá hạn'
                                                                        : 'Chưa hoàn thành'
                                                                }
                                                            </p>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    )}

                                    {/* Empty state */}
                                    {dayClasses.length === 0 && dayLearningItems.length === 0 && (
                                        <div className="text-center py-8">
                                            <Calendar size={48} className="text-gray-300 mx-auto mb-3" />
                                            <p className="text-gray-500">Không có lịch học trong ngày này</p>
                                        </div>
                                    )}
                                </div>
                            );
                        })()}
                    </div>
                )}
            </div>

            {/* Modals */}
            <ClassDetailModal 
                isOpen={classModalOpen}
                onClose={() => setClassModalOpen(false)}
                classData={selectedClass}
            />
            
            <LearningItemDetailModal 
                isOpen={learningItemModalOpen}
                onClose={() => setLearningItemModalOpen(false)}
                learningItemData={selectedLearningItem}
                classData={selectedClass}
            />
        </div>
    );
};

export default ModernSchedule;
