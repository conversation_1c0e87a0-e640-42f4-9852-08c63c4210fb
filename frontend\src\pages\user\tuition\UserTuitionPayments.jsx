import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  fetchUserTuitionPayments,
  fetchUserTuitionSummary,

} from "src/features/tuition/tuitionSlice";
import { resetFilters } from "src/features/filter/filterSlice";
import { setCurrentPage } from "src/features/tuition/tuitionSlice";
import { formatCurrency, formatDate } from "src/utils/formatters";
import { setOpenStudentCardModal } from "src/features/auth/authSlice";
import Pagination from "src/components/Pagination";
import UserLayout from "src/layouts/UserLayout";
import PaymentModal from "src/components/PaymentModal";
import {
  CreditCard,
  Eye,
  FileText,
  Search,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Loader,
  User,
  Calendar,
  GraduationCap,
  Phone,
  BadgeInfo
} from "lucide-react";

// Component AvatarUser giống OverViewPage
const AvatarUser = () => {
    const { user } = useSelector(state => state.auth);

    return (
        <div className="aspect-square md:w-full w-24 rounded-full overflow-hidden border border-gray-300 flex items-center justify-center">
            {user?.avatarUrl ? (
                <img
                    src={user.avatarUrl}
                    alt="avatar"
                    className="w-full h-full object-cover"
                />
            ) : (
                <svg className="w-full h-full text-gray-400" viewBox="0 0 40 40" fill="none">
                    <path
                        d="M20 2.5C10.335 2.5 2.5 10.335 2.5 20C2.5 29.665 10.335 37.5 20 37.5C29.665 37.5 37.5 29.665 37.5 20C37.5 10.335 29.665 2.5 20 2.5ZM20 22.5C16.6983 22.5 14.1667 19.88 14.1667 16.6667C14.1667 13.4533 16.6983 10.8333 20 10.8333C23.3017 10.8333 25.8333 13.4533 25.8333 16.6667C25.8333 19.88 23.3017 22.5 20 22.5ZM10 32.3C10.1 30.38 10.8 29.03 11.73 28.07C12.72 27.05 14 26.41 15.2 26.03C15.41 25.96 15.73 26.01 16.09 26.26C16.88 26.8 18.25 27.5 20 27.5C21.75 27.5 23.12 26.8 23.91 26.26C24.27 26.01 24.59 25.96 24.8 26.03C26 26.41 27.28 27.05 28.27 28.07C29.2 29.03 29.9 30.39 30 32.29C27.18 34.59 23.65 35.84 20 35.83C16.35 35.84 12.82 34.58 10 32.3Z"
                        fill="#94A3B8"
                    />
                </svg>
            )}
        </div>
    );
};

const InfoRow = ({ icon, label, value }) => (
    <div className="flex items-center gap-2 text-xs">
        <span className="text-gray-600">{icon}</span>
        <span className="w-28 font-medium text-gray-900">{label}:</span>
        <span className="text-gray-700">{value}</span>
    </div>
);

// Component InformationUser giống OverViewPage
const InformationUser = () => {
    const { user } = useSelector(state => state.auth);
    const { userTuitionSummary } = useSelector(state => state.tuition);
    const dispatch = useDispatch();
    
    if (!user) return null;

    return (
        <div className="flex flex-col items-start gap-3 p-4 sticky top-[6rem]">
            <div className="flex flex-row md:flex-col items-center gap-3">
                <AvatarUser />
                <div className="text-center flex md:justify-between flex-row w-full md:mt-3 gap-2">
                    <div className="flex flex-col gap-1">
                        <p className="text-lg font-semibold text-gray-900 whitespace-nowrap overflow-hidden text-ellipsis">
                            {user.lastName} {user.firstName}
                        </p>
                        <p className="text-sm text-gray-500 hidden sm:block">@{user.username}</p>
                    </div>
                    <p className="text-sm sm:flex text-gray-500 hidden items-center justify-center gap-1">
                        <BadgeInfo size={16} className="text-gray-500 " /> {user.id}
                    </p>
                </div>
            </div>
            <hr className="w-full border-gray-200 my-3" />

            <div className="w-full flex flex-col gap-3 text-sm text-gray-700">
                <div className="sm:hidden flex">
                    <InfoRow icon={<BadgeInfo size={16} />} label="ID" value={user.id} />
                </div>
                <InfoRow icon={<User size={16} />} label="Giới tính" value={user.gender ? "Nam" : "Nữ"} />
                <InfoRow icon={<Calendar size={16} />} label="Ngày sinh" value={formatDate(user.birthDate)} />
                <InfoRow icon={<GraduationCap size={16} />} label="Lớp" value={user.class || "Chưa cập nhật"} />
                <InfoRow icon={<Phone size={16} />} label="Số điện thoại" value={user.phone || "Chưa cập nhật"} />
            </div>
            <hr className="w-full border-gray-200 my-3" />
            
            {/* Thống kê học phí */}
            <div className="w-full space-y-3">
                <p className="text-gray-900 font-semibold">Thống kê học phí</p>
                <div className="grid grid-cols-1 gap-3 text-sm">
                    <div className="bg-gray-50 p-3 rounded-md">
                        <div className="text-gray-500 text-xs">Tổng khoản:</div>
                        <div className="font-semibold text-gray-800">{userTuitionSummary?.totalPayments || 0}</div>
                    </div>
                    <div className="bg-green-50 p-3 rounded-md">
                        <div className="text-gray-500 text-xs">Đã thanh toán:</div>
                        <div className="font-semibold text-green-600">{userTuitionSummary?.paidPayments || 0}</div>
                    </div>
                    <div className="bg-red-50 p-3 rounded-md">
                        <div className="text-gray-500 text-xs">Chưa thanh toán:</div>
                        <div className="font-semibold text-red-600">{userTuitionSummary?.unpaidPayments || 0}</div>
                    </div>
                    <div className="bg-yellow-50 p-3 rounded-md">
                        <div className="text-gray-500 text-xs">Quá hạn:</div>
                        <div className="font-semibold text-yellow-600">{userTuitionSummary?.overduePayments || 0}</div>
                    </div>
                </div>
            </div>
            
            <hr className="w-full border-gray-200 my-3" />
            
            <button
                onClick={() => dispatch(setOpenStudentCardModal(true))}
                className="w-full text-sm p-1.5 rounded-md bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center"
            >
                Chỉnh sửa thông tin cá nhân
            </button>
        </div>
    );
};

const UserTuitionPayments = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const { tuitionPayments, userTuitionSummary, loading } = useSelector((state) => state.tuition);
  const { page: currentPage, totalPages, total: totalItems, pageSize: limit } = useSelector(
    (state) => state.tuition.pagination
  );

  const [isOverdue, setIsOverDue] = useState(false);
  const [isPaid, setIsPaid] = useState(null);

  // Lọc học phí theo tab đang chọn
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'pending', 'paid', 'overdue'

  // State cho modal thanh toán
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);

  useEffect(() => {
    dispatch(
      fetchUserTuitionPayments({
        page: currentPage,
        limit,
        sortOrder: "DESC",
        overdue: isOverdue,
        isPaid,
      })
    );
  }, [dispatch, currentPage, limit, isOverdue, isPaid]);

  useEffect(() => {
    dispatch(fetchUserTuitionSummary());
  }, [dispatch]);

  const handleView = (id) => {
    navigate(`/tuition-payment/${id}`);
  };

  const handleOpenPaymentModal = (payment) => {
    setSelectedPayment({
      id: payment.id,
      month: payment.monthFormatted,
      amount: "Liên hệ anh Triệu Minh để biết số tiền", // Không còn expectedAmount/paidAmount
      note: payment.note,
      description: `${user.firstName} ${user.lastName} ${user.highSchool} HP_${payment.monthFormatted.replace(' ', '_')}_${payment.id}`
    });
    setIsPaymentModalOpen(true);
  };

  const handleClosePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setSelectedPayment(null);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "PAID":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
            Đã thanh toán
          </span>
        );
      case "UNPAID":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
            Chưa thanh toán
          </span>
        );
      case "OVERDUE":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
            Quá hạn
          </span>
        );
      case "PARTIAL":
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
            Thanh toán một phần
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };



  // Hàm lấy biểu tượng theo trạng thái học phí
  const getPaymentIcon = (status) => {
    switch (status) {
      case 'PAID':
        return (
          <div className="p-3 bg-green-100 rounded-full">
            <CheckCircle className="w-6 h-6 text-green-600" />
          </div>
        );
      case 'UNPAID':
        return (
          <div className="p-3 bg-yellow-100 rounded-full">
            <CreditCard className="w-6 h-6 text-yellow-600" />
          </div>
        );
      case 'OVERDUE':
        return (
          <div className="p-3 bg-red-100 rounded-full">
            <AlertCircle className="w-6 h-6 text-red-600" />
          </div>
        );
      case 'PARTIAL':
        return (
          <div className="p-3 bg-blue-100 rounded-full">
            <DollarSign className="w-6 h-6 text-blue-600" />
          </div>
        );
      default:
        return (
          <div className="p-3 bg-gray-100 rounded-full">
            <CreditCard className="w-6 h-6 text-gray-600" />
          </div>
        );
    }
  };

  return (
    <UserLayout>
      <div className="container flex md:flex-row flex-col">
        <div className="md:w-1/4 w-full flex flex-col justify-start ">
          <InformationUser />
        </div>
        <div className="flex-1 flex flex-col justify-start p-4 gap-3">
          {/* Header Section */}
          <div className="flex flex-col gap-3">
            <h1 className="text-gray-900 font-semibold flex items-center gap-2">
              <CreditCard className="text-cyan-600 w-5 h-5" />
              Học phí của tôi
            </h1>

            {/* Tabs */}
            <div className="border-b border-gray-200 mb-6 overflow-x-auto">
              <div className="flex min-w-max sm:min-w-0">
                <button
                  onClick={() => {
                    setActiveTab('all');
                    setIsPaid(null);
                    setIsOverDue(false);
                    dispatch(setCurrentPage(1));
                  }}
                  className={`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${activeTab === 'all'
                    ? 'text-cyan-600 border-b-2 border-cyan-600'
                    : 'text-gray-500 hover:text-gray-700'
                    }`}
                >
                  Tất cả ({userTuitionSummary?.totalPayments || 0})
                </button>
                <button
                  onClick={() => {
                    setActiveTab('pending');
                    setIsPaid(false);
                    setIsOverDue(false);
                    dispatch(setCurrentPage(1));
                  }}
                  className={`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${activeTab === 'pending'
                    ? 'text-cyan-600 border-b-2 border-cyan-600'
                    : 'text-gray-500 hover:text-gray-700'
                    }`}
                >
                  Chưa thanh toán ({userTuitionSummary?.unpaidPayments || 0})
                </button>
                <button
                  onClick={() => {
                    setActiveTab('paid');
                    setIsPaid(true);
                    setIsOverDue(false);
                    dispatch(setCurrentPage(1));
                  }}
                  className={`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${activeTab === 'paid'
                    ? 'text-cyan-600 border-b-2 border-cyan-600'
                    : 'text-gray-500 hover:text-gray-700'
                    }`}
                >
                  Đã thanh toán ({userTuitionSummary?.paidPayments || 0})
                </button>
                <button
                  onClick={() => {
                    setActiveTab('overdue');
                    setIsPaid(null);
                    setIsOverDue(true);
                    dispatch(setCurrentPage(1));
                  }}
                  className={`px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium whitespace-nowrap ${activeTab === 'overdue'
                    ? 'text-cyan-600 border-b-2 border-cyan-600'
                    : 'text-gray-500 hover:text-gray-700'
                    }`}
                >
                  Quá hạn ({userTuitionSummary?.overduePayments || 0})
                </button>
              </div>
            </div>

            {/* Danh sách học phí */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {loading ? (
                <div className="p-8 text-center text-gray-500">
                  <Loader size={40} className="mx-auto mb-4 text-gray-300 animate-spin" />
                  <p>Đang tải thông tin học phí...</p>
                </div>
              ) : tuitionPayments.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  <CreditCard size={40} className="mx-auto mb-4 text-gray-300" />
                  <p>Không tìm thấy khoản học phí nào.</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-100">
                  {tuitionPayments.map((payment) => (
                    <div
                      key={payment.id}
                      className={`p-4 transition-colors ${payment.isOverdue ? 'bg-red-50' : !payment.isPaid ? 'bg-yellow-50' : ''}`}
                    >
                      <div className="flex gap-4">
                        {getPaymentIcon(payment.isPaid ? 'PAID' : payment.isOverdue ? 'OVERDUE' : 'UNPAID')}
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <h4 className="text-base font-semibold text-gray-800">
                              Học phí {payment.monthFormatted}
                            </h4>
                            <span className="text-sm text-gray-500">
                              Hạn: {new Date(payment.dueDate).toLocaleDateString("vi-VN")}
                            </span>
                          </div>

                          <div className="grid grid-cols-2 gap-4 mt-2">
                            <div>
                              <p className="text-xs text-gray-500">Ngày thanh toán</p>
                              <p className="text-sm font-medium">
                                {payment.paymentDate
                                  ? new Date(payment.paymentDate).toLocaleDateString("vi-VN")
                                  : "Chưa thanh toán"
                                }
                              </p>
                            </div>
                            <div>
                              <p className="text-xs text-gray-500">Ghi chú</p>
                              <p className="text-sm font-medium text-gray-600">
                                {payment.note || "Không có ghi chú"}
                              </p>
                            </div>
                          </div>

                          <div className="mt-2">
                            <p className="text-xs text-gray-500">Trạng thái</p>
                            <div className="mt-1">{getStatusBadge(payment.isPaid ? 'PAID' : payment.isOverdue ? 'OVERDUE' : 'UNPAID')}</div>
                          </div>

                          <div className="flex justify-end items-center mt-3">
                            <div className="flex gap-2">
                              <button
                                onClick={() => handleView(payment.id)}
                                className="flex items-center gap-1 text-sm text-cyan-600 hover:text-cyan-700 px-3 py-1.5 bg-cyan-50 hover:bg-cyan-100 rounded-md transition-colors"
                                title="Xem chi tiết"
                              >
                                <Eye size={16} />
                                <span>Xem chi tiết</span>
                              </button>

                              {!payment.isPaid && (
                                <button
                                  onClick={() => handleOpenPaymentModal(payment)}
                                  className="flex items-center gap-1 text-sm text-green-600 hover:text-green-700 px-3 py-1.5 bg-green-50 hover:bg-green-100 rounded-md transition-colors"
                                  title="Thanh toán"
                                >
                                  <FileText size={16} />
                                  <span>Thanh toán</span>
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Phân trang */}
              <div className="p-4 border-t border-gray-100">
                <Pagination
                  currentPage={currentPage}
                  totalItems={totalItems}
                  limit={limit}
                  onPageChange={(p) => dispatch(setCurrentPage(p))}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={handleClosePaymentModal}
        paymentInfo={selectedPayment}
      />
    </UserLayout>
  );
};

export default UserTuitionPayments;
