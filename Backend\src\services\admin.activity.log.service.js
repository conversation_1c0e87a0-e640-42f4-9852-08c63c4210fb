import db from '../models/index.js';
import { Op } from 'sequelize';
import AdminActivityResponse from '../dtos/responses/adminActivity/AdminActivityResponse.js';

export const logAdminActivity = async (adminId, action = 'unknown', targetId, description, transaction = null) => {
    if (adminId === 1) {
        return;
    }
    await db.AdminActivityLog.create({
        adminId,
        action,
        targetId,
        description
    }, { transaction });
};

export const getAllAdminActivities = async (search = '', limit = 20, cursor = null, sortOrder = 'DESC', startDate = null, endDate = null) => {
    // Tạo điều kiện tìm kiếm
    let whereClause = {};
    
    if (search.trim()) {
        whereClause[Op.or] = [
            { action: { [Op.like]: `%${search}%` } },
            { description: { [Op.like]: `%${search}%` } },
            { '$admin.firstName$': { [Op.like]: `%${search}%` } },
            { '$admin.lastName$': { [Op.like]: `%${search}%` } },
            { '$admin.username$': { [Op.like]: `%${search}%` } }
        ];
    }

    // Thêm điều kiện lọc theo khoảng thời gian
    if (startDate || endDate) {
        whereClause.createdAt = {};
        
        if (startDate) {
            whereClause.createdAt[Op.gte] = new Date(startDate);
        }
        
        if (endDate) {
            // Thêm 1 ngày để bao gồm cả ngày cuối
            const endOfDay = new Date(endDate);
            endOfDay.setDate(endOfDay.getDate() + 1);
            whereClause.createdAt[Op.lt] = endOfDay;
        }
    }

    // Thêm điều kiện cursor cho phân trang theo thời gian
    if (cursor) {
        const cursorDate = new Date(cursor);
        if (sortOrder.toUpperCase() === 'DESC') {
            whereClause.createdAt = { 
                ...whereClause.createdAt,
                [Op.lt]: cursorDate 
            };
        } else {
            whereClause.createdAt = { 
                ...whereClause.createdAt,
                [Op.gt]: cursorDate 
            };
        }
    }
    const activities = await db.AdminActivityLog.findAll({
        where: whereClause,
        include: [
            {
                model: db.User,
                as: 'admin',
                attributes: ['id', 'firstName', 'lastName', 'username', 'avatarUrl']
            }
        ],
        order: [['createdAt', sortOrder.toUpperCase()]],
        limit: limit + 1, // Lấy thêm 1 record để kiểm tra hasNextPage
        distinct: true
    });

    // Kiểm tra xem có trang tiếp theo không
    const hasNextPage = activities.length > limit;
    if (hasNextPage) {
        activities.pop(); // Xóa record thừa
    }

    // Format data using DTO
    const formattedActivities = activities.map(activity => new AdminActivityResponse(activity));

    // Lấy cursor cho trang tiếp theo
    const nextCursor = activities.length > 0 ? activities[activities.length - 1].createdAt : null;

    return {
        activities: formattedActivities,
        hasNextPage,
        nextCursor,
        limit,
        sortOrder
    };
};