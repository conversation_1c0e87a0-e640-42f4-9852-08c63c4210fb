import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { askQuestionWithAI, resetAiResponse } from "src/features/ai/aiSlice";
import { Bot, X, MessageCircle } from "lucide-react";
import ArticleContent from "../article/ArticleContent";
import QuestionDropdown from "./QuestionDropdown";
import OnlineLoading from "../loading/OnlineLoading";
// AI Chat Widget Component - Floating chat interface
const AIChatWidget = () => {
    const { question } = useSelector((state) => state.questions);
    const { userQuestion, aiResponse, loading: aiLoading } = useSelector((state) => state.ai);
    const dispatch = useDispatch();
    const [selectedMessageId, setSelectedMessageId] = useState(null);
    const [isOpen, setIsOpen] = useState(false);
    const thinkingMessages = [
        "AI đang suy nghĩ...",
        "<PERSON><PERSON> tìm kiếm câu trả lời hay nhất...",
        "Đang phân tích nội dung câu hỏi...",
        "Đang gọi trợ lý thông minh...",
        "Sắp xong rồi, chờ một chút nhé...",
    ];
    const handleAskAI = (messageId) => {
        if (question?.id) {
            // Reset previous response if selecting different question
            if (selectedMessageId !== messageId) {
                dispatch(resetAiResponse());
            }
            setSelectedMessageId(messageId);
            dispatch(askQuestionWithAI({ questionId: question.id, messageId }));
        }
    };

    const [thinkingIndex, setThinkingIndex] = useState(0);

    useEffect(() => {
        if (!aiLoading) return; // chỉ chạy khi đang loading

        const interval = setInterval(() => {
            setThinkingIndex((prevIndex) => (prevIndex + 1) % thinkingMessages.length);
        }, 2000);

        return () => clearInterval(interval); // clear interval khi unmount/loading kết thúc
    }, [aiLoading]);


    useEffect(() => {
        if (question?.id) {
            dispatch(resetAiResponse());
        }
    }, [question]);

    const toggleChat = () => {
        setIsOpen(!isOpen);
    };

    return (
        <>
            {/* Chat Widget */}
            {isOpen && (
                <div className="fixed bottom-20 right-4 md:right-6 md:w-[30rem] w-[90vw] bg-white rounded-2xl shadow-2xl border border-gray-200 z-50 overflow-hidden">
                    {/* Header */}
                    <div className="flex items-center justify-between p-4 bg-gradient-to-r from-sky-500 to-blue-600 text-white">
                        <div className="flex items-center gap-3">
                            <div className="p-1.5 bg-white/20 rounded-lg">
                                <Bot size={18} />
                            </div>
                            <div>
                                <h3 className="font-semibold text-sm">AI Assistant</h3>
                                <p className="text-xs text-sky-100">Hỏi về câu hỏi này</p>
                            </div>
                        </div>
                        <button
                            onClick={toggleChat}
                            className="p-1 hover:bg-white/20 rounded-lg transition-colors"
                        >
                            <X size={18} />
                        </button>
                    </div>
                    <div className="h-[60vh] overflow-y-auto">

                        {question ? (
                            <>
                                {/* Question Selection */}
                                <div className="p-4 border-b border-gray-100">
                                    <h4 className=" font-medium text-gray-600 mb-3 uppercase tracking-wide">
                                        Câu hỏi#{question.id}
                                    </h4>

                                    <QuestionDropdown
                                        userQuestion={userQuestion}
                                        selectedMessageId={selectedMessageId}
                                        aiLoading={aiLoading}
                                        aiResponse={aiResponse}
                                        handleAskAI={handleAskAI}
                                    />
                                </div>

                                {/* AI Response Area */}
                                <div className="p-4 min-h-[120px]">
                                    {aiLoading && (
                                        <div className="flex items-center gap-3 text-sky-600 py-4">
                                            <OnlineLoading type="AI" className="w-10 h-10" />
                                            <span className="text-sm">{thinkingMessages[thinkingIndex]}</span>
                                        </div>
                                    )}

                                    {aiResponse && !aiLoading && (
                                        <div className="space-y-3">
                                            <div className="flex items-center gap-2 mb-2">
                                                <div className="p-1 bg-sky-100 rounded-full">
                                                    <Bot size={12} className="text-sky-600" />
                                                </div>
                                                <span className="text-xs font-medium text-gray-600">AI Assistant</span>
                                            </div>
                                            {/* Lời nhắc */}
                                            <div className="text-xs text-gray-500 italic px-2">
                                                ⚠️ Đây là câu trả lời tham khảo từ AI. Các em hãy suy nghĩ kỹ và kiểm chứng lại khi cần nhé. Chúc các em học tập thật tốt! 💪
                                            </div>
                                            <div className="bg-gray-50 rounded-lg p-3 text-sm">
                                                <ArticleContent content={aiResponse} />
                                            </div>
                                        </div>
                                    )}

                                    {!aiResponse && !aiLoading && (
                                        <div className="text-center py-8 text-gray-400">
                                            <MessageCircle size={32} className="mx-auto mb-2 opacity-50" />
                                            <p className="text-sm">Chọn một câu hỏi để bắt đầu</p>
                                        </div>
                                    )}
                                </div>
                            </>
                        ) : (
                            <div className="p-6 h-full flex flex-col items-center justify-center text-center text-gray-500">
                                <MessageCircle className="w-10 h-10 mb-3 text-gray-300" />
                                <h4 className="text-base font-semibold mb-1">Chưa có câu hỏi nào được chọn</h4>
                                <p className="text-sm text-gray-400">
                                    Vui lòng chọn một câu hỏi từ danh sách để xem chi tiết và phản hồi từ AI.
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Floating Button */}
            <button
                onClick={toggleChat}
                className={`fixed bottom-6 right-6 md:w-14 md:h-14 w-10 h-10 bg-gradient-to-r from-sky-500 to-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-40 hover:scale-105 active:scale-95 ${isOpen ? 'rotate-0' : ''
                    }`}
            >
                {isOpen ? (
                    <X className="drop-shadow-sm md:w-5 md:h-5 w-4 h-4" />
                ) : (
                    <Bot size={16} className="drop-shadow-sm md:w-5 md:h-5 w-4 h-4" />
                )}
                {aiResponse && !isOpen && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                )}
            </button>
        </>
    );
};

export default AIChatWidget;
