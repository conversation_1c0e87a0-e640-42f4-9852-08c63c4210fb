import { useEffect, useRef } from "react";

/**
 * @param {Function} effect - Hàm effect bạn muốn thực thi sau delay.
 * @param {Array<any>} deps - Dependencies giống như trong useEffect.
 * @param {number} delay - Th<PERSON><PERSON> gian chờ (ms) sau khi deps dừng thay đổi.
 */
const useDebouncedEffect = (effect, deps, delay) => {
    const timeoutRef = useRef(null);

    useEffect(() => {
        // Clear previous timeout
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        // Set new timeout
        timeoutRef.current = setTimeout(() => {
            effect();
        }, delay);

        // Cleanup function
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, [...deps, delay]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);
};

export default useDebouncedEffect;
