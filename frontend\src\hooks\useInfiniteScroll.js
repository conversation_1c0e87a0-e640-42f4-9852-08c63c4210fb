import { useEffect, useRef } from 'react';

const useInfiniteScroll = (hasNextPage, isLoading, onLoadMore, threshold = 100) => {
    const scrollRef = useRef(null);

    useEffect(() => {
        const scrollElement = scrollRef.current;
        if (!scrollElement) return;

        const handleScroll = () => {
            const { scrollTop, scrollHeight, clientHeight } = scrollElement;
            
            // Check if user has scrolled to near the bottom
            if (scrollHeight - scrollTop - clientHeight < threshold) {
                if (hasNextPage && !isLoading) {
                    onLoadMore();
                }
            }
        };

        scrollElement.addEventListener('scroll', handleScroll);
        
        return () => {
            scrollElement.removeEventListener('scroll', handleScroll);
        };
    }, [hasNextPage, isLoading, onLoadMore, threshold]);

    return scrollRef;
};

export default useInfiniteScroll;
