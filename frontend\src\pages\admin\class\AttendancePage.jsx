import { use, useEffect, useState, useRef, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
    fetchAttendancesByLessonId,
    fetchAttendancesByLessonIdWithoutPagination,
    postAttendance,
    postAllAttendanceInLesson,
    updateAttendance,
    deleteAttendance,
    resetAttendanceState,
    setPage,
    fetchLessonAttendanceStatistics,
    clearLessonStatistics,
    setMonthTuition,
} from '../../../features/attendance/attendanceSlice';
import {
    getLearningItemsByClassIdInfinite,
    resetLearningItemsByClass
} from '../../../features/learningItem/learningItemSlice';
import { findUsers } from 'src/features/user/userSlice';
import { fetchLessonByClassId, addStudentToClass } from '../../../features/class/classSlice';
// import { setSuccessMessage } from '../features/notification/notificationSlice';
import LoadingSpinner from '../../../components/loading/LoadingSpinner';
import Pagination from '../../../components/Pagination';
import ConfirmModal from '../../../components/modal/ConfirmModal';
import UserSearchInput from '../../../components/UserSearchInput';
import { exportAttendancesToExcel } from '../../../utils/excelExport';
import { Calendar, Users, Plus, Save, X, Trash2, UserPlus, CheckCircle, XCircle, Clock, Filter, FileSpreadsheet, Search, Home, BookOpen, Award, ChevronDown, Download, FileImage, Loader2, MessageCircle } from 'lucide-react';
import ClassAdminLayout from 'src/layouts/ClassAdminLayout';
import { clearStudentNotJoined } from '../../../features/attendance/attendanceSlice';
import CustomTextareaDropdown from '../../../components/input/CustomTextareaDropdown';
import OutsideClickWrapper from 'src/components/common/OutsideClickWrapper';
import FEEDBACK from 'src/constants/feedbackConstants';

const getMonthOptions = () => {
    const start = new Date(2025, 5); // tháng 6 năm 2025 (tháng tính từ 0)
    const now = new Date();

    const months = [];
    let current = new Date(start);

    while (
        current.getFullYear() < now.getFullYear() ||
        (current.getFullYear() === now.getFullYear() && current.getMonth() <= now.getMonth())
    ) {
        const year = current.getFullYear();
        const month = String(current.getMonth() + 1).padStart(2, "0");
        months.push({
            label: `${month} - ${year}`,
            value: `${year}-${month}`,
        });

        // tăng 1 tháng
        current.setMonth(current.getMonth() + 1);
    }

    return months.reverse(); // đảo ngược để hiện tháng mới nhất trước
};


export function MonthDropdown({ monthTuition, setMonthTuition }) {
    const [open, setOpen] = useState(false);
    const options = getMonthOptions();

    const selected = options.find((opt) => opt.value === monthTuition);

    return (
        <th className="border border-gray-200 p-3 text-center relative">
            <div className="flex flex-col items-center">
                <span>Học phí</span>
                <button
                    onClick={() => setOpen(!open)}
                    className="text-sm text-gray-500 mt-1 flex items-center gap-1 border border-gray-300 px-2 py-[2px] rounded"
                >
                    {selected ? selected.label : "Chọn tháng"}
                    <ChevronDown size={14} />
                </button>

                {open && (
                    <div className="absolute top-full mt-1 bg-white border border-gray-300 shadow z-10 rounded w-[140px] max-h-[200px] overflow-y-auto">
                        {options.map((option) => (
                            <div
                                key={option.value}
                                onClick={() => {
                                    setMonthTuition(option.value);
                                    setOpen(false);
                                }}
                                className="px-2 py-1 hover:bg-gray-100 cursor-pointer text-sm"
                            >
                                {option.label}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </th>
    );
}


const AttendancePage = () => {
    const { classId } = useParams();
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { lessons, loadingAdd } = useSelector(state => state.classes);
    const {
        list: attendances,
        loading: attendanceLoading,
        page: currentPage,
        pageSize,
        total: totalItems,
        totalPages,
        lessonStatistics,
        userNotJoined,
        monthTuition,
        loadingExcelData
    } = useSelector(state => state.attendances);
    const { classDetail, loading: classLoading } = useSelector(state => state.classes);
    const {
        learningItemsByClass,
        loadingInfinite,
        paginationInfinite
    } = useSelector(state => state.learningItems);

    const [selectedLesson, setSelectedLesson] = useState(null);
    const [attendanceData, setAttendanceData] = useState({});
    const [isCreatingAttendance, setIsCreatingAttendance] = useState(false);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [attendanceToDelete, setAttendanceToDelete] = useState(null);
    const [selectedUser, setSelectedUser] = useState(null);
    const [userSearchTerm, setUserSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [tuitionFilter, setTuitionFilter] = useState('all');
    const [searchTerm, setSearchTerm] = useState('');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    const [defaultStatus, setDefaultStatus] = useState('present');
    const [defaultNote, setDefaultNote] = useState('');
    const [showAddStudentToClass, setShowAddStudentToClass] = useState(false);
    const [addStudent, setAddStudent] = useState(false);

    // BTVN selection states
    const [selectedBTVNs, setSelectedBTVNs] = useState([]);
    const [btvnSearchTerm, setBtvnSearchTerm] = useState('');
    const [btvnTypeFilter, setBtvnTypeFilter] = useState('all');    // Attendance sheet modal states
    const [showAttendanceSheet, setShowAttendanceSheet] = useState(false);
    const [attendanceSheetData, setAttendanceSheetData] = useState(null);
    const [generatingSheet, setGeneratingSheet] = useState(false);
    const [currentStudentInfo, setCurrentStudentInfo] = useState(null); // Lưu thông tin học sinh hiện tại    // Debounce state for attendance changes
    const [pendingChanges, setPendingChanges] = useState({});
    const [savingStates, setSavingStates] = useState({});
    const debounceTimersRef = useRef({});

    // Fetch class lessons on component mount
    useEffect(() => {
        if (classId) {
            dispatch(fetchLessonByClassId({ classId }));
            // Load learning items for BTVN selection
            dispatch(getLearningItemsByClassIdInfinite({
                classId,
                limit: 50, // Load more items for selection
                type: 'BTVN' // Only load BTVN items
            }));
        } return () => {
            dispatch(resetAttendanceState());
            dispatch(clearLessonStatistics());
            dispatch(resetLearningItemsByClass());

            // Clear all debounce timers
            Object.values(debounceTimersRef.current).forEach(timer => {
                clearTimeout(timer);
            });
            debounceTimersRef.current = {};
        };
    }, [dispatch, classId]);

    useEffect(() => {
        dispatch(findUsers(''));
    }, [dispatch])

    useEffect(() => {
        if (userNotJoined !== null) {
            setShowAddStudentToClass(true);
        } else {
            setShowAddStudentToClass(false);
        }
    }, [userNotJoined]);

    // Debounce search term
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
        }, 1000); // 500ms delay

        return () => clearTimeout(timer);
    }, [searchTerm]);

    // Reset page to 1 when search term changes
    useEffect(() => {
        if (debouncedSearchTerm !== '') {
            dispatch(setPage(1));
        }
    }, [debouncedSearchTerm, dispatch]);

    // Fetch attendances when lesson is selected or page changes
    useEffect(() => {
        if (selectedLesson) {
            const btvnListParam = selectedBTVNs.map(btvn => btvn.id);

            dispatch(fetchAttendancesByLessonId({
                lessonId: selectedLesson.id,
                status: statusFilter,
                search: debouncedSearchTerm,
                page: currentPage,
                limit: pageSize,
                tuition: tuitionFilter,
                month: monthTuition,
                btvnList: btvnListParam
            }));

            // Fetch lesson statistics
            dispatch(fetchLessonAttendanceStatistics(selectedLesson.id));
        }
    }, [dispatch, selectedLesson, currentPage, pageSize, statusFilter, debouncedSearchTerm, tuitionFilter, monthTuition, selectedBTVNs]);

    // Initialize attendance data when attendances are loaded
    useEffect(() => {
        if (attendances.length > 0) {
            const data = {};
            attendances.forEach(attendance => {
                data[attendance.userId] = {
                    id: attendance.id,
                    status: attendance.status,
                    note: attendance.note || ''
                };
            });
            setAttendanceData(data);
        } else {
            setAttendanceData({});
        }
    }, [attendances]);

    const handleLessonSelect = (lesson) => {
        setSelectedLesson(lesson);
        setAttendanceData({});
        setSelectedBTVNs([]); // Reset BTVN selection when changing lesson
        // Reset to page 1 when selecting new lesson
        dispatch(setPage(1));
    };

    // BTVN selection handlers
    const handleBTVNToggle = (btvn) => {
        setSelectedBTVNs(prev => {
            const isSelected = prev.some(item => item.id === btvn.id);
            if (isSelected) {
                return prev.filter(item => item.id !== btvn.id);
            } else {
                return [...prev, btvn];
            }
        });
    };

    const handleBTVNSelectAll = () => {
        const filteredBTVNs = getFilteredBTVNs();
        setSelectedBTVNs(filteredBTVNs);
    };

    const handleBTVNClearAll = () => {
        setSelectedBTVNs([]);
    };

    const getFilteredBTVNs = () => {
        return learningItemsByClass.filter(item => {
            const matchesSearch = !btvnSearchTerm ||
                item.name.toLowerCase().includes(btvnSearchTerm.toLowerCase()) ||
                item.description?.toLowerCase().includes(btvnSearchTerm.toLowerCase());

            const matchesType = btvnTypeFilter === 'all' || item.type === btvnTypeFilter;

            return matchesSearch && matchesType;
        });
    };

    // Attendance sheet handlers
    const handleGenerateAttendanceSheet = async (attendance) => {
        setGeneratingSheet(true);
        try {
            // Lưu thông tin học sinh hiện tại
            setCurrentStudentInfo({
                name: attendance.user.fullName,
                phone: attendance.user.phone,
                studentPhone: attendance.user.password
            });

            const sheetData = await generateAttendanceSheetImage({
                student: {
                    id: attendance.user.id,
                    name: `${attendance.user.fullName}`,
                    highSchool: attendance.user.highSchool,
                    class: attendance.user.class,
                    phone: attendance.user.phone,
                    studentPhone: attendance.user.password,
                    isPaid: attendance.user.isPaid
                }, lesson: {
                    name: selectedLesson.name,
                    day: new Date(selectedLesson.day).toLocaleDateString('vi-VN'),
                    class: classDetail?.name || 'Không xác định',
                    teacher: classDetail?.teacher || 'Chưa xác định'
                },
                attendance: {
                    status: attendance.status,
                    note: attendance.note,
                    assignmentFeedback: attendance.assignmentFeedback,
                    attendanceTime: attendance.attendanceTime
                },
                btvnStatuses: attendance.btvnStatuses,
                btvnSummary: attendance.btvnSummary,
                selectedBTVNs: selectedBTVNs,
                monthTuition: monthTuition
            });

            setAttendanceSheetData(sheetData);
            setShowAttendanceSheet(true);
        } catch (error) {
            console.error('Error generating attendance sheet:', error);
            alert('Có lỗi khi tạo phiếu điểm danh!');
        } finally {
            setGeneratingSheet(false);
        }
    };

    const handleDownloadAttendanceSheet = () => {
        if (!attendanceSheetData) return;

        // Create download link
        const link = document.createElement('a');
        link.href = attendanceSheetData.imageUrl;
        link.download = attendanceSheetData.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleSendZaloMessage = () => {
        if (!attendanceSheetData || !currentStudentInfo) return;

        // Lấy số ĐT (ưu tiên phụ huynh)
        const rawPhone = currentStudentInfo.phone || currentStudentInfo.studentPhone;
        if (!rawPhone) {
            alert('Không có số điện thoại phụ huynh để gửi tin nhắn!');
            return;
        }

        // Chuẩn hóa số: bỏ ký tự lạ, đổi +84... -> 0...
        const normalizePhone = (s) => {
            let d = (s + '').replace(/\D/g, '');
            if (d.startsWith('84')) d = '0' + d.slice(2);
            return d;
        };
        const phone = normalizePhone(rawPhone);

        const studentName = currentStudentInfo.name;
        const lessonName = selectedLesson?.name || 'Buổi học';
        const lessonDate = selectedLesson ? new Date(selectedLesson.day).toLocaleDateString('vi-VN') : '';

        const message = `Xin chào quý phụ huynh!

🎓 PHIẾU ĐIỂM DANH - ${studentName}
📚 Buổi học: ${lessonName}
📅 Ngày: ${lessonDate}

Quý phụ huynh vui lòng xem phiếu điểm danh chi tiết của con trong hình ảnh đính kèm.

Cảm ơn quý phụ huynh đã tin tưởng và đồng hành cùng Toán Thầy Bee! 🐝

---
Toán Thầy Bee
📞 Hotline: 0123456789`;

        const encoded = encodeURIComponent(message);

        // Link web (hoạt động cả PC & mobile)
        const webUrl = `https://zalo.me/${phone}?text=${encoded}`;

        // URL scheme (mở thẳng app Zalo – chỉ mobile có cài app)
        const appUrl = `zalo://msg?phone=${phone}&text=${encoded}`;

        const isMobile = /Android|iPhone|iPad|iPod/i.test(navigator.userAgent);

        if (isMobile) {
            // Thử mở app trước; nếu không khởi chạy, sau ~1s fallback sang web
            const start = Date.now();
            window.location.href = appUrl;
            setTimeout(() => {
                // Nếu app không chiếm quyền điều hướng (thường < 1.5s), mở web
                if (Date.now() - start < 1500) {
                    window.location.href = webUrl;
                }
            }, 1200);
        } else {
            // Desktop: mở Zalo Web
            window.open(webUrl, '_blank');
        }
    };


    // Generate attendance sheet image
    const generateAttendanceSheetImage = async (data) => {
        const loadImage = (src) =>
            new Promise((resolve, reject) => {
                const img = new Image();
                img.crossOrigin = 'anonymous';
                img.onload = () => resolve(img);
                img.onerror = reject;
                img.src = src + '?t=' + Date.now();
            });
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // High DPI support for crisp rendering
            const devicePixelRatio = window.devicePixelRatio || 1;
            const scaleFactor = Math.max(devicePixelRatio, 2); // Minimum 2x for crisp text

            // Set canvas size - A5 portrait ratio (148mm x 210mm) with high DPI
            const baseWidth = 700;
            const baseHeight = 950;

            canvas.width = baseWidth * scaleFactor;
            canvas.height = baseHeight * scaleFactor;

            // Scale the canvas back down using CSS for display
            canvas.style.width = baseWidth + 'px';
            canvas.style.height = baseHeight + 'px';

            // Scale the drawing context to match device pixel ratio
            ctx.scale(scaleFactor, scaleFactor);

            // Enable text anti-aliasing and image smoothing
            ctx.textBaseline = 'top';
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';

            // Background with subtle gradient
            const gradient = ctx.createLinearGradient(0, 0, 0, baseHeight);
            gradient.addColorStop(0, '#ffffff');
            gradient.addColorStop(1, '#f8fafc');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, baseWidth, baseHeight);

            // Header background with gradient
            const headerGradient = ctx.createLinearGradient(0, 0, 0, 120);
            headerGradient.addColorStop(0, '#3b82f6');
            headerGradient.addColorStop(1, '#1d4ed8');
            ctx.fillStyle = headerGradient;
            ctx.fillRect(0, 0, baseWidth, 120);

            // Header border decoration
            ctx.fillStyle = '#1e40af';
            ctx.fillRect(0, 115, baseWidth, 5);

            // Logo URL
            const logoUrl = 'https://toanthaybee.edu.vn/logo1.png'; const drawContent = () => {
                // Center name
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 32px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle'; // Căn giữa theo trục y
                ctx.fillText('TOÁN THẦY BEE', baseWidth / 2, 35); // Điều chỉnh y position

                // Subtitle
                ctx.font = 'bold 18px Arial';
                ctx.fillText('PHIẾU ĐIỂM DANH HỌC SINH', baseWidth / 2, 60); // Điều chỉnh y position                // Lesson and class info in header
                ctx.font = '14px Arial';
                ctx.fillStyle = '#ffffff';
                ctx.fillText(`${data.lesson.name} - ${data.lesson.class}`, baseWidth / 2, 80); // Điều chỉnh y position

                // Lesson date below
                ctx.font = '12px Arial';
                ctx.fillStyle = '#ffffff';
                ctx.fillText(`Ngày học: ${data.lesson.day}`, baseWidth / 2, 100); // Điều chỉnh y position

                // Reset styles for content
                ctx.fillStyle = '#1f2937';
                ctx.textAlign = 'left';
                ctx.textBaseline = 'top'; // Reset text baseline cho content

                let yPos = 160;
                const margin = 40;

                // 1. THÔNG TIN HỌC SINH
                ctx.font = 'bold 18px Arial';
                ctx.fillText('THÔNG TIN HỌC SINH', margin, yPos);
                yPos += 30;

                // Student info in 2 columns layout
                ctx.font = '15px Arial';
                // Row 1: Họ tên - Lớp
                ctx.fillText(`Họ tên: ${data.student.name}`, margin, yPos);
                ctx.fillText(`Lớp: ${data.student.class || 'Không xác định'}`, margin + 280, yPos);
                yPos += 25;

                // Row 2: Trường
                ctx.fillText(`Trường: ${data.student.highSchool || 'Không xác định'}`, margin, yPos);
                yPos += 25;

                // Row 3: Điện thoại
                ctx.fillText(`ĐT PH: ${data.student.phone || 'Không có'}`, margin, yPos);
                ctx.fillText(`ĐT HS: ${data.student.studentPhone || 'Không có'}`, margin + 280, yPos);
                yPos += 40;

                // 2. THÔNG TIN ĐIỂM DANH
                ctx.font = 'bold 18px Arial';
                ctx.fillText('THÔNG TIN ĐIỂM DANH', margin, yPos);
                yPos += 30;

                // Status with color
                const statusText = data.attendance.status === 'present' ? 'CÓ MẶT' :
                    data.attendance.status === 'absent' ? 'VẮNG MẶT' : 'ĐI MUỘN';
                const statusColor = data.attendance.status === 'present' ? '#16a34a' :
                    data.attendance.status === 'absent' ? '#dc2626' : '#ca8a04';

                ctx.fillStyle = statusColor;
                ctx.font = 'bold 16px Arial';
                ctx.fillText(`Trạng thái: ${statusText}`, margin, yPos);
                yPos += 25;                // Attendance time (only if present or late)
                if (data.attendance.status !== 'absent' && data.attendance.attendanceTime) {
                    ctx.fillStyle = '#F44336'; // Blue color for attendance time
                    ctx.font = 'bold 16px Arial'; // Bold and slightly larger font
                    ctx.fillText(`Thời gian đến lớp: ${new Date(data.attendance.attendanceTime).toLocaleString('vi-VN')}`, margin, yPos);
                    yPos += 25;
                }

                ctx.fillStyle = '#000000';
                yPos += 15;                // 3. HỌC PHÍ - Only show if payment info is available
                if (data.student.isPaid !== null) {
                    ctx.font = 'bold 18px Arial';
                    ctx.fillText('HỌC PHÍ', margin, yPos);
                    yPos += 30;

                    // Format month tuition for display
                    const formatMonth = (monthString) => {
                        if (!monthString) return 'Không xác định';
                        const [year, month] = monthString.split('-');
                        return `${month}/${year}`;
                    };

                    ctx.font = '15px Arial';
                    ctx.fillText(`Tháng: ${formatMonth(data.monthTuition)}`, margin, yPos);
                    yPos += 25;

                    // Tuition status with color
                    const tuitionText = data.student.isPaid === true ? 'ĐÃ ĐÓNG' : 'CHƯA ĐÓNG';
                    const tuitionColor = data.student.isPaid === true ? '#16a34a' : '#dc2626';

                    ctx.fillStyle = tuitionColor;
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText(`Trạng thái: ${tuitionText}`, margin, yPos);
                    yPos += 40;
                }

                ctx.fillStyle = '#000000';                // 4. NHẬN XÉT VÀ ĐÁNH GIÁ - HIGHLIGHTED SECTION
                // Draw background for comment section
                const commentSectionY = yPos;
                const commentHeight = 400; // Increased height for BTVN feedback

                // Background gradient for comment section
                const commentGradient = ctx.createLinearGradient(0, commentSectionY, 0, commentSectionY + commentHeight);
                commentGradient.addColorStop(0, '#f8fafc');
                commentGradient.addColorStop(1, '#e2e8f0');
                ctx.fillStyle = commentGradient;
                ctx.fillRect(30, commentSectionY - 10, baseWidth - 60, commentHeight);

                // Border for comment section
                ctx.strokeStyle = '#cbd5e1';
                ctx.lineWidth = 2;
                ctx.strokeRect(30, commentSectionY - 10, baseWidth - 60, commentHeight);

                // Main title with background
                ctx.fillStyle = '#1e40af';
                ctx.fillRect(40, yPos, baseWidth - 80, 35);
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle'; // Căn giữa theo trục y
                ctx.fillText('NHẬN XÉT VÀ ĐÁNH GIÁ', baseWidth / 2, yPos + 17.5); // Căn giữa trong khung 35px

                ctx.textAlign = 'left';
                ctx.textBaseline = 'top'; // Reset về top cho phần content sau
                yPos += 70;                // 1. Thông tin BTVN
                ctx.fillStyle = '#1f2937';
                ctx.font = 'bold 18px Arial';
                ctx.fillText('1. Thông tin bài tập về nhà (BTVN):', 50, yPos);
                yPos += 30;
                if (data.btvnStatuses && Object.keys(data.btvnStatuses).length > 0) {
                    ctx.font = '16px Arial';
                    Object.entries(data.btvnStatuses).forEach(([key, status]) => {
                        const statusIcon = status.isDone ? '✓' : '✗';
                        const statusColor = status.isDone ? '#16a34a' : '#dc2626';

                        ctx.fillStyle = statusColor;
                        const btvnName = status.btvnInfo?.name || 'Unknown';
                        const lessonName = status.btvnInfo?.lesson?.name || '';
                        let btvnText = `${statusIcon} ${btvnName}`;

                        if (lessonName) {
                            btvnText += ` (${lessonName})`;
                        }

                        if (status.type === 'exam' && status.score !== null) {
                            btvnText += ` - Điểm: ${status.score} / 10`;
                        }

                        ctx.fillText(btvnText, 70, yPos);
                        yPos += 25;

                        // Hiển thị feedback BTVN
                        if (status.feedback) {
                            ctx.fillStyle = '#374151'; // Dark gray for feedback
                            ctx.font = '14px Arial';

                            // Xử lý tất cả các dạng xuống dòng
                            let feedbackText = status.feedback
                                .replace(/\\n/g, '\n')  // Chuyển \\n thành \n
                                .replace(/\r\n/g, '\n') // Windows line ending
                                .replace(/\r/g, '\n')   // Mac line ending
                                .replace(/\n\n+/g, '\n'); // Loại bỏ nhiều \n liên tiếp

                            // Tách theo \n hoặc theo cấu trúc "+)"
                            let feedbackLines = feedbackText.split('\n');
                            
                            // Nếu không có \n, thử tách theo "+)"
                            if (feedbackLines.length === 1) {
                                feedbackLines = feedbackText.split('+)').map((line, index) => {
                                    if (index === 0) return line.trim();
                                    return '+)' + line.trim();
                                }).filter(line => line.length > 0);
                            } else {
                                feedbackLines = feedbackLines.map(line => line.trim()).filter(line => line.length > 0);
                            }

                            let isFirstLine = true;

                            for (const line of feedbackLines) {
                                const feedbackWords = line.split(' ').filter(word => word.length > 0);
                                let feedbackLine = isFirstLine ? '  Nhận xét: ' : '  ';
                                const maxFeedbackWidth = baseWidth - 140;

                                for (let n = 0; n < feedbackWords.length; n++) {
                                    const testLine = feedbackLine + feedbackWords[n] + ' ';
                                    const metrics = ctx.measureText(testLine);
                                    const testWidth = metrics.width;

                                    if (testWidth > maxFeedbackWidth && n > 0 && feedbackLine.length > (isFirstLine ? 12 : 2)) {
                                        ctx.fillText(feedbackLine, 70, yPos);
                                        feedbackLine = '    ' + feedbackWords[n] + ' ';
                                        yPos += 20;
                                    } else {
                                        feedbackLine = testLine;
                                    }
                                }
                                ctx.fillText(feedbackLine, 70, yPos);
                                yPos += 20; // Xuống dòng cho mỗi line từ \n
                                isFirstLine = false;
                            }
                            yPos += 5; // Thêm một chút khoảng cách
                        }

                        ctx.fillStyle = '#1f2937';
                        ctx.font = '16px Arial';
                    });
                    yPos += 15;
                } else {
                    ctx.font = '16px Arial';
                    ctx.fillStyle = '#6b7280';
                    ctx.fillText('Chưa có bài tập về nhà được giao', 70, yPos);
                    ctx.fillStyle = '#1f2937';
                    yPos += 30;
                }

                // 2. Nhận xét bài tập trên lớp
                ctx.font = 'bold 18px Arial';
                ctx.fillText('2. Nhận xét bài tập trên lớp:', 50, yPos);
                yPos += 30; if (data.attendance.assignmentFeedback && data.attendance.assignmentFeedback.trim()) {
                    ctx.font = '16px Arial';
                    
                    // Xử lý tất cả các dạng xuống dòng cho assignmentFeedback
                    let assignmentText = data.attendance.assignmentFeedback
                        .replace(/\\n/g, '\n')  // Chuyển \\n thành \n
                        .replace(/\r\n/g, '\n') // Windows line ending
                        .replace(/\r/g, '\n')   // Mac line ending
                        .replace(/\n\n+/g, '\n'); // Loại bỏ nhiều \n liên tiếp

                    // Tách theo \n hoặc theo cấu trúc "+)"
                    let assignmentLines = assignmentText.split('\n');
                    
                    // Nếu không có \n, thử tách theo "+)"
                    if (assignmentLines.length === 1) {
                        assignmentLines = assignmentText.split('+)').map((line, index) => {
                            if (index === 0) return line.trim();
                            return '+)' + line.trim();
                        }).filter(line => line.length > 0);
                    } else {
                        assignmentLines = assignmentLines.map(line => line.trim()).filter(line => line.length > 0);
                    }

                    // Render từng dòng
                    for (const line of assignmentLines) {
                        const words = line.split(' ').filter(word => word.length > 0);
                        let currentLine = '';
                        const maxWidth = baseWidth - 120;

                        for (let n = 0; n < words.length; n++) {
                            const testLine = currentLine + words[n] + ' ';
                            const metrics = ctx.measureText(testLine);
                            const testWidth = metrics.width;

                            if (testWidth > maxWidth && n > 0) {
                                ctx.fillText(currentLine, 70, yPos);
                                currentLine = words[n] + ' ';
                                yPos += 25;
                            } else {
                                currentLine = testLine;
                            }
                        }
                        ctx.fillText(currentLine, 70, yPos);
                        yPos += 25; // Xuống dòng cho mỗi line từ \n
                    }
                    yPos += 5; // Thêm một chút khoảng cách
                } else {
                    ctx.font = '16px Arial';
                    ctx.fillStyle = '#6b7280';
                    ctx.fillText('Chưa có nhận xét về bài tập trên lớp', 70, yPos);
                    ctx.fillStyle = '#1f2937';
                    yPos += 30;
                }

                // 3. Nhận xét chung
                ctx.font = 'bold 18px Arial';
                ctx.fillText('3. Nhận xét chung:', 50, yPos);
                yPos += 30; if (data.attendance.note && data.attendance.note.trim()) {
                    ctx.font = '16px Arial';
                    const generalComment = data.attendance.note;
                    const words = generalComment.split(' ');
                    let line = '';
                    const maxWidth = baseWidth - 120; // Leave margin

                    for (let n = 0; n < words.length; n++) {
                        const testLine = line + words[n] + ' ';
                        const metrics = ctx.measureText(testLine);
                        const testWidth = metrics.width;

                        if (testWidth > maxWidth && n > 0) {
                            ctx.fillText(line, 70, yPos);
                            line = words[n] + ' ';
                            yPos += 25;
                        } else {
                            line = testLine;
                        }
                    }
                    ctx.fillText(line, 70, yPos);
                    yPos += 30;
                } else {
                    ctx.font = '16px Arial';
                    ctx.fillStyle = '#6b7280';
                    ctx.fillText('Chưa có nhận xét chung', 70, yPos);
                    ctx.fillStyle = '#1f2937';
                    yPos += 30;
                }                // Teacher signature section - positioned at bottom right
                const signatureY = baseHeight - 80; // Moved closer to bottom
                const signatureX = baseWidth - 200;

                ctx.fillStyle = '#1f2937';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Giáo viên giảng dạy', signatureX + 100, signatureY);

                // Teacher name with signature-like font
                ctx.font = 'italic bold 20px cursive, "Brush Script MT", "Lucida Handwriting", fantasy';
                ctx.fillStyle = '#2563eb'; // Blue color for signature
                const teacherName = data.lesson.teacher || 'Chưa xác định';
                ctx.fillText(teacherName, signatureX + 100, signatureY + 25);

                // Signature line - đặt dưới tên giáo viên
                ctx.strokeStyle = '#666666';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(signatureX + 20, signatureY + 45); // Tăng từ +35 lên +45
                ctx.lineTo(signatureX + 180, signatureY + 45);
                ctx.stroke();

                // Convert to blob and create download URL with high quality
                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const filename = `phieu-diem-danh-${data.student.name.replace(/\s+/g, '-')}-${new Date().getTime()}.png`;

                    resolve({
                        imageUrl: url,
                        filename: filename,
                        canvas: canvas
                    });
                }, 'image/png', 1.0); // Maximum quality
            };

            // Try to load logo first, then draw content
            loadImage(logoUrl)
                .then((logoImg) => {
                    // Draw white circle background for logo
                    ctx.fillStyle = '#ffffff';
                    ctx.beginPath();
                    ctx.arc(100, 60, 35, 0, 2 * Math.PI);
                    ctx.fill();

                    // Draw logo with circular clipping
                    ctx.save();
                    ctx.beginPath();
                    ctx.arc(100, 60, 35, 0, 2 * Math.PI);
                    ctx.clip();
                    ctx.drawImage(logoImg, 65, 25, 70, 70);
                    ctx.restore();

                    // Draw the rest of the content
                    drawContent();
                })
                .catch((error) => {
                    console.warn('Could not load logo, drawing without it:', error);
                    // Draw white circle background
                    ctx.fillStyle = '#ffffff';
                    ctx.beginPath();
                    ctx.arc(100, 60, 35, 0, 2 * Math.PI);
                    ctx.fill();

                    // Draw placeholder text instead
                    ctx.fillStyle = '#f59e0b';
                    ctx.font = 'bold 24px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('TB', 100, 70);

                    // Draw the rest of the content
                    drawContent();
                });
        });
    };

    useEffect(() => {
        if (selectedLesson && !monthTuition) {
            const lessonDate = new Date(selectedLesson.day);
            const formattedMonth = `${lessonDate.getFullYear()}-${String(lessonDate.getMonth() + 1).padStart(2, '0')}`;
            dispatch(setMonthTuition(formattedMonth));
        }
    }, [selectedLesson, monthTuition]);


    const handlePageChange = (page) => {
        dispatch(setPage(page));
    };

    const handleAttendanceChange = (studentId, field, value) => {
        let resetStatics = false;
        if (field === 'status') {
            resetStatics = true;
        }
        setAttendanceData(prev => {
            const currentData = prev[studentId] || {};
            return {
                ...prev,
                [studentId]: {
                    id: currentData.id || null,
                    status: currentData.status || 'absent',
                    note: currentData.note || '',
                    ...currentData,
                    [field]: value
                }
            };
        });

        handleSaveAttendance(studentId, { [field]: value }, resetStatics);
    }

    const handleCreateAllAttendance = async () => {
        if (!selectedLesson) return;

        setIsCreatingAttendance(true);
        try {
            // console.log('Creating attendance for all students in lesson:', selectedLesson.id, classId);
            await dispatch(postAllAttendanceInLesson({
                classId,
                lessonId: selectedLesson.id
            })).unwrap().finally(() => {
                dispatch(fetchAttendancesByLessonId({
                    lessonId: selectedLesson.id,
                    status: statusFilter,
                    search: debouncedSearchTerm,
                    page: currentPage,
                    limit: pageSize,
                    tuition: tuitionFilter,
                    month: monthTuition
                }));

                // Update lesson statistics after creating all attendance
                dispatch(fetchLessonAttendanceStatistics(selectedLesson.id));
            })
        } catch (error) {
            console.error('Error creating attendance:', error);
        } finally {
            setIsCreatingAttendance(false);
        }
    };

    const handleSaveAttendance = async (studentId, autoSaveData = null, resetStatics = false) => {
        const data = autoSaveData || attendanceData[studentId];
        if (!data) return;

        try {
            // Get current attendance data for this student
            const currentAttendance = attendances.find(att => att.userId === studentId);
            const attendanceId = currentAttendance?.id || attendanceData[studentId]?.id;

            if (attendanceId) {
                // Update existing attendance
                const updateData = autoSaveData ? {
                    ...currentAttendance,
                    ...autoSaveData
                } : {
                    status: data.status,
                    note: data.note
                };

                await dispatch(updateAttendance({
                    attendanceId: attendanceId,
                    data: updateData
                })).unwrap();
            } else {
                // Create new attendance
                const createData = autoSaveData ? {
                    userId: studentId,
                    lessonId: selectedLesson.id,
                    status: 'present',
                    note: '',
                    classId: classId,
                    ...autoSaveData
                } : {
                    userId: studentId,
                    lessonId: selectedLesson.id,
                    status: data.status,
                    note: data.note,
                    classId: classId,
                };

                await dispatch(postAttendance(createData)).unwrap();
            }

            // Update lesson statistics after successful save
            if (selectedLesson && resetStatics) {
                dispatch(fetchLessonAttendanceStatistics(selectedLesson.id));
            }
        } catch (error) {
            console.error('Error saving attendance:', error);
        }
    };



    const handleDeleteClick = (attendanceId) => {
        setAttendanceToDelete(attendanceId);
        setShowDeleteModal(true);
    };

    const handleDeleteConfirm = async () => {
        if (!attendanceToDelete) return;

        try {
            await dispatch(deleteAttendance(attendanceToDelete)).unwrap();

            // Refresh attendance list
            // dispatch(fetchAttendancesByLessonId({
            //     lessonId: selectedLesson.id,
            //     status: statusFilter,
            //     search: debouncedSearchTerm,
            //     page: currentPage,
            //     limit: pageSize
            // }));

            // Update lesson statistics after successful delete
            dispatch(fetchLessonAttendanceStatistics(selectedLesson.id));
        } catch (error) {
            console.error('Error deleting attendance:', error);
        } finally {
            setShowDeleteModal(false);
            setAttendanceToDelete(null);
        }
    };

    const handleDeleteCancel = () => {
        setShowDeleteModal(false);
        setAttendanceToDelete(null);
    };

    const handleDeleteAddStudentToClass = () => {
        dispatch(clearStudentNotJoined());
    }

    const handleAddStudentToClass = async () => {
        if (!userNotJoined || !classId) return;

        try {
            await dispatch(addStudentToClass({
                classId: parseInt(classId),
                studentId: userNotJoined
            })).unwrap().finally(() => {
                // Refresh the student list
                dispatch(clearStudentNotJoined());
            });
        } catch (error) {
            console.error('Error adding student to class:', error);
        }
    }

    // Helper function to get status styling
    const getStatusStyle = (status) => {
        switch (status) {
            case 'present':
                return 'bg-green-100 text-green-800 border-green-300';
            case 'absent':
                return 'bg-red-100 text-red-800 border-red-300';
            case 'late':
                return 'bg-yellow-100 text-yellow-800 border-yellow-300';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-300';
        }
    };

    // Helper function to get status icon
    const getStatusIcon = (status) => {
        switch (status) {
            case 'present':
                return <CheckCircle size={14} className="text-green-600" />;
            case 'absent':
                return <XCircle size={14} className="text-red-600" />;
            case 'late':
                return <Clock size={14} className="text-yellow-600" />;
            default:
                return null;
        }
    };

    // Helper function to get academic performance based on average score
    const getAcademicPerformance = (averageScore) => {
        // Kiểm tra nếu không có điểm hoặc không phải số
        if (averageScore === null || averageScore === undefined || isNaN(Number(averageScore))) {
            return {
                text: 'Chưa làm bài',
                color: 'text-gray-600',
                bgColor: 'bg-gray-100'
            };
        }

        const score = Number(averageScore);

        if (score < 5) {
            return {
                text: 'Yếu',
                color: 'text-red-700',
                bgColor: 'bg-red-100'
            };
        } else if (score < 7) {
            return {
                text: 'Trung bình',
                color: 'text-red-700',
                bgColor: 'bg-red-100'
            };
        } else if (score < 8.75) {
            return {
                text: 'Khá',
                color: 'text-green-700',
                bgColor: 'bg-green-100'
            };
        } else {
            return {
                text: 'Giỏi',
                color: 'text-yellow-700',
                bgColor: 'bg-yellow-100'
            };
        }
    };

    // Helper function to check if note has changed
    const hasNoteChanged = (studentId) => {
        const currentAttendance = attendances.find(att => att.userId === studentId);
        const localData = attendanceData[studentId];

        if (!localData) return false;

        const originalNote = currentAttendance?.note || '';
        const currentNote = localData.note || '';

        return originalNote !== currentNote;
    };

    // Helper function to render BTVN status for multiple BTVNs
    const renderMultipleBTVNStatus = (btvnStatuses, btvnSummary) => {
        if (!btvnStatuses || Object.keys(btvnStatuses).length === 0) {
            return (
                <div className="flex items-center justify-center gap-1 text-gray-400">
                    <BookOpen size={14} />
                    <span className="text-xs">Không có BTVN</span>
                </div>
            );
        }

        // Hiển thị tổng quan trước
        if (btvnSummary) {
            return (
                <div className="space-y-2">

                    {/* Chi tiết từng BTVN */}
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                        {Object.entries(btvnStatuses).map(([key, status]) => (
                            <div key={key} className="flex items-center justify-between p-1 bg-white border rounded text-xs">
                                <div className="flex-1 truncate">
                                    <div className="font-medium">
                                        {status.btvnInfo?.name || 'Unknown'} - {status.btvnInfo?.lesson?.name ? status.btvnInfo?.lesson?.name.length > 20 ? status.btvnInfo?.lesson?.name.slice(0, 17) + '...' : status.btvnInfo?.lesson?.name : ''}
                                    </div>
                                </div>
                                <div className="flex items-center gap-1">
                                    {status.isDone ? (
                                        <>
                                            <CheckCircle size={12} className="text-green-600" />
                                            {status.type === 'exam' && status.score !== undefined && (
                                                <span className="text-green-600 font-medium">{status.score}</span>
                                            )}
                                        </>
                                    ) : (
                                        <XCircle size={12} className="text-red-600" />
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            );
        }

        return (
            <div className="flex items-center justify-center gap-1 text-gray-400">
                <BookOpen size={14} />
                <span className="text-xs">Đang tải...</span>
            </div>
        );
    };

    // Helper function to render BTVN status (keep old function for backward compatibility)
    const renderBTVNStatus = (btvnStatus) => {
        if (!btvnStatus) {
            return (
                <div className="flex items-center justify-center gap-1 text-gray-400">
                    <BookOpen size={14} />
                    <span className="text-xs">Không có BTVN</span>
                </div>
            );
        }

        const { isDone, type, score, studyTime } = btvnStatus;

        if (isDone) {
            return (
                <div className="flex items-center justify-center gap-1">
                    <CheckCircle size={14} className="text-green-600" />
                    <div className="text-center">
                        <div className="text-xs font-medium text-green-700">Đã hoàn thành</div>
                        {type === 'exam' && score !== undefined && (
                            <div className="text-xs text-green-600">Điểm: {score}</div>
                        )}
                        {type === 'learningItem' && studyTime && (
                            <div className="text-xs text-gray-500">
                                {new Date(studyTime).toLocaleDateString('vi-VN')}
                            </div>
                        )}
                    </div>
                </div>
            );
        } else {
            return (
                <div className="flex items-center justify-center gap-1">
                    <XCircle size={14} className="text-red-600" />
                    <div className="text-center">
                        <div className="text-xs font-medium text-red-700">Chưa hoàn thành</div>
                    </div>
                </div>
            );
        }
    };

    // Helper function to render weekly attendances
    const renderWeeklyAttendances = (weeklyAttendances) => {
        if (!weeklyAttendances || weeklyAttendances.length === 0) {
            return (
                <div className="text-center text-gray-400">
                    <span className="text-xs">Không có buổi học khác</span>
                </div>
            );
        }

        return (
            <div className="space-y-1">
                {weeklyAttendances.map((attendance, index) => (
                    <div
                        title={attendance.lesson.name}
                        key={attendance.id || index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                        <div className="flex-1">
                            <div className="font-medium text-gray-700 truncate">
                                {attendance.lesson.name.length > 20 ? attendance.lesson.name.slice(0, 17) + '...' : attendance.lesson.name} - {attendance.lesson?.class?.name}
                            </div>
                            <div className="text-gray-500">
                                {new Date(attendance.lesson.day).toLocaleDateString('vi-VN')}
                            </div>
                        </div>
                        <div className="flex items-center gap-1">
                            {getStatusIcon(attendance.status)}
                            <span className={`px-1 py-0.5 rounded text-xs font-medium ${getStatusStyle(attendance.status)}`}>
                                {attendance.status === 'present' ? 'Có mặt' :
                                    attendance.status === 'absent' ? 'Vắng' : 'Muộn'}
                            </span>
                        </div>
                    </div>
                ))}
            </div>
        );
    };

    const renderTuition = (isPaid) => {
        if (isPaid === null) {
            return (
                <div className="text-center text-gray-400">
                    <span className="text-xs">Chưa có</span>
                </div>
            );
        }
        return (
            <div className="flex items-center justify-center gap-1">
                {isPaid ? (
                    <CheckCircle size={14} className="text-green-600" />
                ) : (
                    <XCircle size={14} className="text-red-600" />
                )}
            </div>
        );
    };


    const handleUserSelect = (user) => {
        setSelectedUser(user);
        setUserSearchTerm(`${user.lastName} ${user.firstName}`);
    };

    const handleUserSearchChange = (value) => {
        setUserSearchTerm(value);
        if (!value) {
            setSelectedUser(null);
        }
    };

    const handleUserSearchClear = () => {
        setSelectedUser(null);
        setUserSearchTerm('');
    };

    const handleCreateSingleAttendance = async () => {
        if (!selectedUser || !selectedLesson) {
            return;
        }

        try {
            await dispatch(postAttendance({
                userId: selectedUser.id,
                lessonId: selectedLesson.id,
                status: defaultStatus,
                note: defaultNote,
                classId
            })).unwrap();

            // Clear selection
            setSelectedUser(null);
            setUserSearchTerm('');
            setDefaultNote('');

            // Refresh attendance list
            dispatch(fetchAttendancesByLessonId({
                lessonId: selectedLesson.id,
                status: statusFilter,
                tuition: tuitionFilter,
                search: debouncedSearchTerm,
                page: currentPage,
                limit: pageSize,
                month: monthTuition
            }));

            // Update lesson statistics after creating single attendance
            dispatch(fetchLessonAttendanceStatistics(selectedLesson.id));
        } catch (error) {
            console.error('Error creating single attendance:', error);
        }
    };

    const handleExportToExcel = async () => {
        if (!selectedLesson) {
            return;
        }

        try {
            // Fetch all attendances for export (limit 1000, all statuses)
            const result = await dispatch(fetchAttendancesByLessonIdWithoutPagination({
                lessonId: selectedLesson.id,
                status: 'all', // Get all statuses
                tuition: 'all',
                month: monthTuition,
                btvnList: selectedBTVNs.map(btvn => btvn.id)
            })).unwrap();

            if (result && result.data && result.data.length > 0) {
                // Export to Excel
                exportAttendancesToExcel(
                    result.data,
                    selectedLesson.name,
                    classDetail?.name
                );
            } else {
                alert('Không có dữ liệu điểm danh để xuất');
            }
        } catch (error) {
            console.error('Error exporting to Excel:', error);
            alert('Có lỗi xảy ra khi xuất Excel');
        }
    };

    if (classLoading) {
        return (
            <div className="flex items-center justify-center h-screen">
                <LoadingSpinner
                    size="4rem"
                    showText={true}
                    text="Đang tải thông tin lớp học..."
                />
            </div>
        );
    }

    return (
        <ClassAdminLayout>

            {/* Content */}
            <div className="flex-1 p-6 pb-20">

                {/* Lesson Selection */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6 max-h-[20rem] overflow-y-auto">
                    <div className="flex items-center gap-2 mb-4">
                        <Calendar className="text-sky-600" size={20} />
                        <h3 className="text-lg font-semibold text-gray-800">Chọn buổi học</h3>
                    </div>

                    <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                        {lessons?.map((lesson) => (
                            <div
                                key={lesson.id}
                                onClick={() => handleLessonSelect(lesson)}
                                className={`p-3 rounded-lg border cursor-pointer transition-all ${selectedLesson?.id === lesson.id
                                    ? 'border-sky-500 bg-sky-50'
                                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                    }`}
                            >
                                <span className="font-medium text-gray-800">{lesson.name} </span>
                                <span className="text-sm text-gray-500">
                                    {new Date(lesson.day).toLocaleDateString('vi-VN')}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>

                <div className="flex flex-row gap-6">
                    {/* BTVN Selection */}
                    {selectedLesson && (
                        <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                            <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center gap-2">
                                    <BookOpen className="text-sky-600" size={20} />
                                    <h3 className="text-lg font-semibold text-gray-800">Chọn BTVN để theo dõi</h3>
                                </div>
                            </div>

                            {/* Selected BTVNs Display */}
                            {selectedBTVNs.length > 0 && (
                                <div className="mb-4">
                                    <div className="flex items-center gap-2 mb-2">
                                        <span className="text-sm font-medium text-gray-700">
                                            BTVN đã chọn ({selectedBTVNs.length}):
                                        </span>
                                        <button
                                            onClick={handleBTVNClearAll}
                                            className="text-xs text-red-600 hover:text-red-800"
                                        >
                                            Xóa tất cả
                                        </button>
                                    </div>
                                    <div className="flex flex-wrap gap-2">
                                        {selectedBTVNs.map(btvn => (
                                            <div
                                                key={btvn.id}
                                                className="flex items-center gap-1 px-2 py-1 bg-sky-100 text-sky-800 rounded-lg text-sm"
                                            >
                                                <span>{btvn.name}</span>
                                                <button
                                                    onClick={() => handleBTVNToggle(btvn)}
                                                    className="hover:text-red-600"
                                                >
                                                    <X size={14} />
                                                </button>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* BTVN Selector Panel */}
                            <div className="border-t pt-4">
                                <div className="flex gap-3 mb-4">
                                    <input
                                        type="text"
                                        placeholder="Tìm kiếm BTVN..."
                                        value={btvnSearchTerm}
                                        onChange={(e) => setBtvnSearchTerm(e.target.value)}
                                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm"
                                    />
                                    <select
                                        value={btvnTypeFilter}
                                        onChange={(e) => setBtvnTypeFilter(e.target.value)}
                                        className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
                                    >
                                        <option value="all">Tất cả loại</option>
                                        <option value="BTVN">BTVN</option>
                                        <option value="VID">Video</option>
                                        <option value="DOC">Tài liệu</option>
                                    </select>
                                    <button
                                        onClick={handleBTVNSelectAll}
                                        className="px-3 py-2 bg-green-100 text-green-700 rounded-lg text-sm hover:bg-green-200"
                                    >
                                        Chọn tất cả
                                    </button>
                                </div>

                                {loadingInfinite ? (
                                    <div className="flex items-center justify-center py-8">
                                        <LoadingSpinner size="2rem" showText={true} text="Đang tải BTVN..." />
                                    </div>
                                ) : (
                                    <div className="max-h-64 overflow-y-auto space-y-2">
                                        {getFilteredBTVNs().map(btvn => {
                                            const isSelected = selectedBTVNs.some(item => item.id === btvn.id);
                                            return (
                                                <div
                                                    key={btvn.id}
                                                    onClick={() => handleBTVNToggle(btvn)}
                                                    className={`p-3 border rounded-lg cursor-pointer transition-all ${isSelected
                                                        ? 'border-sky-500 bg-sky-50'
                                                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                                        }`}
                                                >
                                                    <div className="flex items-center gap-3">
                                                        <input
                                                            type="checkbox"
                                                            checked={isSelected}
                                                            onChange={() => { }}
                                                            className="text-sky-600"
                                                        />
                                                        <div className="flex-1">
                                                            <div className="font-medium text-gray-800">{btvn.name}</div>
                                                            {btvn.description && (
                                                                <div className="text-sm text-gray-600 mt-1">{btvn.description}</div>
                                                            )}
                                                            <div className="flex items-center gap-2 mt-2">
                                                                <span className={`px-2 py-1 rounded text-xs font-medium ${btvn.type === 'BTVN' ? 'bg-red-100 text-red-800' :
                                                                    btvn.type === 'VID' ? 'bg-blue-100 text-blue-800' :
                                                                        'bg-green-100 text-green-800'
                                                                    }`}>
                                                                    {btvn.type}
                                                                </span>
                                                                <span className="text-xs text-gray-500">
                                                                    {btvn.lesson.name}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                        {getFilteredBTVNs().length === 0 && (
                                            <div className="text-center py-8 text-gray-500">
                                                Không tìm thấy BTVN nào
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {/* User Search Section */}
                    {selectedLesson && (
                        <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                            <div className="flex items-center gap-2 mb-4">
                                <UserPlus className="text-sky-600" size={20} />
                                <h3 className="text-lg font-semibold text-gray-800">Tạo điểm danh cho học sinh</h3>
                            </div>

                            {/* Default Status Selection */}
                            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center gap-2 mb-2">
                                    <CheckCircle className="text-sky-600" size={16} />
                                    <label className="text-sm font-medium text-gray-700">Trạng thái mặc định:</label>
                                </div>
                                <select
                                    value={defaultStatus}
                                    onChange={(e) => setDefaultStatus(e.target.value)}
                                    className={`px-3 py-2 border rounded-md text-sm font-medium ${getStatusStyle(defaultStatus)}`}
                                >
                                    <option value="present">Có mặt</option>
                                    <option value="absent">Vắng mặt</option>
                                    <option value="late">Đi muộn</option>
                                </select>
                                <div className="flex items-center mt-2">
                                    <input
                                        type="checkbox"
                                        checked={addStudent}
                                        onChange={() => setAddStudent(!addStudent)}
                                        className="ml-2"
                                    />
                                    <span className="text-sm text-gray-600 ml-1">Thêm học sinh chưa vào lớp</span>

                                </div>
                                <p className="text-xs text-gray-500 mt-1">
                                    Trạng thái này sẽ được áp dụng khi tạo điểm danh mới
                                </p>
                            </div>

                            <div className="space-y-4">
                                {/* User Search Input with higher z-index */}
                                <div className="relative z-50">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Chọn học sinh:
                                    </label>
                                    <UserSearchInput
                                        value={userSearchTerm}
                                        selectedUserId={selectedUser?.id || ''}
                                        onChange={handleUserSearchChange}
                                        onSelect={handleUserSelect}
                                        onClear={handleUserSearchClear}
                                        placeholder="Tìm kiếm học sinh..."
                                        className="w-full"
                                    />
                                </div>

                                {/* Note Input */}
                                <div className="relative z-10">
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Ghi chú (tùy chọn):
                                    </label>
                                    <input
                                        type="text"
                                        value={defaultNote}
                                        onChange={(e) => setDefaultNote(e.target.value)}
                                        placeholder="Nhập ghi chú cho điểm danh..."
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                                    />
                                </div>

                                <div className="relative z-0">
                                    <button
                                        onClick={handleCreateSingleAttendance}
                                        disabled={!selectedUser}
                                        className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        <UserPlus size={16} />
                                        Tạo điểm danh
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Attendance Management */}
                {selectedLesson && (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-2">
                                <Users className="text-sky-600" size={20} />
                                <h3 className="text-lg font-semibold text-gray-800">
                                    Điểm danh: {selectedLesson.name}
                                </h3>
                            </div>

                            <div className="flex items-center gap-3">
                                {/* Search Input */}
                                <div className="flex items-center gap-2">
                                    <Search className="text-gray-500" size={16} />
                                    <input
                                        type="text"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        placeholder="Tìm kiếm học sinh..."
                                        className="px-3 py-1 border border-gray-300 rounded text-sm w-48"
                                    />
                                </div>

                                {/* Status Filter */}
                                <div className="flex items-center gap-2">
                                    <Filter className="text-gray-500" size={16} />
                                    <select
                                        value={statusFilter}
                                        onChange={(e) => setStatusFilter(e.target.value)}
                                        className="px-3 py-1 border border-gray-300 rounded text-sm"
                                    >
                                        <option value="all">Tất cả trạng thái</option>
                                        <option value="present">Có mặt</option>
                                        <option value="absent">Vắng mặt</option>
                                        <option value="late">Đi muộn</option>
                                    </select>
                                </div>

                                <div className="flex items-center gap-2">
                                    <select
                                        value={tuitionFilter}
                                        onChange={(e) => setTuitionFilter(e.target.value)}
                                        className="px-3 py-1 border border-gray-300 rounded text-sm"
                                    >
                                        <option value="all">Tất cả</option>
                                        <option value="true">Đã thanh toán</option>
                                        <option value="false">Chưa thanh toán</option>
                                    </select>
                                </div>

                                {/* {attendances.length === 0 && ( */}
                                <button
                                    onClick={handleCreateAllAttendance}
                                    disabled={isCreatingAttendance}
                                    className="flex items-center gap-2 px-4 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 disabled:opacity-50"
                                    title="Tạo điểm danh cho tất cả học sinh trong lớp với trạng thái mặc định"
                                >
                                    <Plus size={16} />
                                    {isCreatingAttendance ? 'Đang tạo...' : `Tạo điểm danh`}
                                </button>
                                {/* )} */}

                                {/* Export Excel Button */}
                                <button
                                    onClick={handleExportToExcel}
                                    className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                                    title="Xuất Excel"
                                    disabled={loadingExcelData}
                                >
                                    {loadingExcelData ? (
                                        <Loader2 className="w-4 h-4 animate-spin" />
                                    ) : (
                                        <FileSpreadsheet size={16} />
                                    )}
                                    Xuất Excel
                                </button>
                            </div>
                        </div>

                        {attendanceLoading ? (
                            <div className="flex items-center justify-center py-12">
                                <LoadingSpinner
                                    size="3rem"
                                    showText={true}
                                    text="Đang tải dữ liệu điểm danh..."
                                />
                            </div>
                        ) : attendances.length > 0 ? (
                            <>
                                {/* Status Legend with Statistics */}
                                <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                                    <h4 className="text-sm font-medium text-gray-700 mb-3">Thống kê điểm danh:</h4>

                                    {/* Statistics Summary */}
                                    {lessonStatistics.loading ? (
                                        <div className="flex justify-center py-4">
                                            <LoadingSpinner size="2rem" />
                                        </div>
                                    ) : lessonStatistics.data ? (
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <Users size={16} className="text-gray-600" />
                                                    <span className="text-sm font-medium text-gray-700">Tổng số</span>
                                                </div>
                                                <div className="text-xl font-bold text-gray-900 mt-1">
                                                    {lessonStatistics.data.statistics.total}
                                                </div>
                                            </div>

                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <CheckCircle size={16} className="text-green-600" />
                                                    <span className="text-sm font-medium text-gray-700">Có mặt</span>
                                                </div>
                                                <div className="text-xl font-bold text-green-600 mt-1">
                                                    {lessonStatistics.data.statistics.present}
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    {lessonStatistics.data.statistics.presentPercentage}%
                                                </div>
                                            </div>

                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <XCircle size={16} className="text-red-600" />
                                                    <span className="text-sm font-medium text-gray-700">Vắng mặt</span>
                                                </div>
                                                <div className="text-xl font-bold text-red-600 mt-1">
                                                    {lessonStatistics.data.statistics.absent}
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    {lessonStatistics.data.statistics.absentPercentage}%
                                                </div>
                                            </div>

                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <Clock size={16} className="text-yellow-600" />
                                                    <span className="text-sm font-medium text-gray-700">Đi muộn</span>
                                                </div>
                                                <div className="text-xl font-bold text-yellow-600 mt-1">
                                                    {lessonStatistics.data.statistics.late}
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    {lessonStatistics.data.statistics.latePercentage}%
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <Users size={16} className="text-gray-600" />
                                                    <span className="text-sm font-medium text-gray-700">Tổng số</span>
                                                </div>
                                                <div className="text-xl font-bold text-gray-900 mt-1">0</div>
                                            </div>
                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <CheckCircle size={16} className="text-green-600" />
                                                    <span className="text-sm font-medium text-gray-700">Có mặt</span>
                                                </div>
                                                <div className="text-xl font-bold text-green-600 mt-1">0</div>
                                                <div className="text-xs text-gray-500">0%</div>
                                            </div>
                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <XCircle size={16} className="text-red-600" />
                                                    <span className="text-sm font-medium text-gray-700">Vắng mặt</span>
                                                </div>
                                                <div className="text-xl font-bold text-red-600 mt-1">0</div>
                                                <div className="text-xs text-gray-500">0%</div>
                                            </div>
                                            <div className="bg-white p-3 rounded-lg border border-gray-200">
                                                <div className="flex items-center gap-2">
                                                    <Clock size={16} className="text-yellow-600" />
                                                    <span className="text-sm font-medium text-gray-700">Đi muộn</span>
                                                </div>
                                                <div className="text-xl font-bold text-yellow-600 mt-1">0</div>
                                                <div className="text-xs text-gray-500">0%</div>
                                            </div>
                                        </div>
                                    )}

                                    {/* Status Legend */}
                                    <div className="border-t border-gray-200 pt-3">
                                        <h5 className="text-xs font-medium text-gray-600 mb-2">Chú thích trạng thái:</h5>
                                        <div className="flex flex-wrap gap-3">
                                            <div className="flex items-center gap-2">
                                                <CheckCircle size={14} className="text-green-600" />
                                                <span className="px-2 py-1 bg-green-100 text-green-800 border border-green-300 rounded text-xs font-medium">
                                                    Có mặt
                                                </span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <XCircle size={14} className="text-red-600" />
                                                <span className="px-2 py-1 bg-red-100 text-red-800 border border-red-300 rounded text-xs font-medium">
                                                    Vắng mặt
                                                </span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <Clock size={14} className="text-yellow-600" />
                                                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 border border-yellow-300 rounded text-xs font-medium">
                                                    Đi muộn
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="overflow-x-auto relative min-h-[50vh]" style={{ overflow: 'visible' }}>
                                    <table className="w-full border-collapse border border-gray-200 relative z-10">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="border border-gray-200 p-3 text-center">STT</th>
                                                <th className="border border-gray-200 p-3 text-left">Học sinh</th>
                                                <th className="border border-gray-200 p-3 text-center">Học lực</th>
                                                <th className="border border-gray-200 p-3 text-center">Trạng thái</th>

                                                <MonthDropdown
                                                    monthTuition={monthTuition}
                                                    setMonthTuition={(month) => dispatch(setMonthTuition(month))}
                                                />

                                                <th className="border border-gray-200 p-3 text-left">NX Chung</th>
                                                <th className="border border-gray-200 p-3 text-left">NX BTTL</th>
                                                <th className="border border-gray-200 p-3 text-center">
                                                    <div className="flex flex-col items-center">
                                                        <span>BTVN</span>
                                                        {selectedBTVNs.length > 0 && (
                                                            <span className="text-xs text-gray-500 mt-1">
                                                                ({selectedBTVNs.length} mục)
                                                            </span>
                                                        )}
                                                    </div>
                                                </th>
                                                <th className="border border-gray-200 p-3 text-center">Điểm danh tuần</th>
                                                <th className="border border-gray-200 p-3 text-center">
                                                    <div className="flex flex-col items-center">
                                                        Zalo
                                                    </div>

                                                </th>
                                                <th className="border border-gray-200 p-3 text-center">Thao tác</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {attendances.map((attendance, idx) => (
                                                <tr key={attendance.id} className="hover:bg-gray-50">
                                                    <td className="border border-gray-200 p-3 text-center">
                                                        {idx + 1 + (currentPage - 1) * pageSize}
                                                    </td>
                                                    <td className="border border-gray-200 p-3">
                                                        <div
                                                            className="font-medium text-blue-600 hover:text-blue-800 cursor-pointer hover:underline"
                                                            onClick={() => {
                                                                window.open(`/admin/student-management/${attendance.userId}/attendance`, '_blank');
                                                            }}
                                                        >
                                                            {attendance.user?.fullName}
                                                        </div>
                                                        <div className="text-sm text-gray-500">
                                                            {attendance?.userId} | {attendance.user?.highSchool} | {attendance.user?.class} | {attendance.user?.phone}
                                                        </div>
                                                    </td>
                                                    <td className="border border-gray-200 p-3 text-center">
                                                        {(() => {
                                                            const performance = getAcademicPerformance(attendance.user?.averageScore);
                                                            const score = attendance.user?.averageScore;
                                                            return (
                                                                <div className="flex flex-col items-center justify-center">
                                                                    <span className={`px-2 py-1 rounded text-xs font-medium ${performance.color} ${performance.bgColor}`}>
                                                                        {performance.text}
                                                                    </span>
                                                                    {score !== null && score !== undefined && !isNaN(Number(score)) && (
                                                                        <span className="text-xs text-gray-500 ml-1">
                                                                            ({Number(score).toFixed(1)})
                                                                        </span>
                                                                    )}
                                                                </div>
                                                            );
                                                        })()}
                                                    </td>
                                                    <td className="border border-gray-200 p-3 text-center">
                                                        <div className="flex items-center justify-center gap-2">
                                                            <select
                                                                value={attendanceData[attendance.userId]?.status || attendance.status}
                                                                onChange={(e) => handleAttendanceChange(attendance.userId, 'status', e.target.value)}
                                                                className={`px-2 py-1 border rounded text-sm font-medium ${getStatusStyle(attendanceData[attendance.userId]?.status || attendance.status)}`}
                                                            >
                                                                <option value="present">Có mặt</option>
                                                                <option value="absent">Vắng mặt</option>
                                                                <option value="late">Đi muộn</option>
                                                            </select>
                                                        </div>
                                                    </td>
                                                    <td className="border border-gray-200 p-3">
                                                        {renderTuition(attendance.user?.isPaid)}
                                                    </td>
                                                    <td className="border border-gray-200 p-3 relative" style={{ zIndex: 100 - idx }}>
                                                        <CustomTextareaDropdown
                                                            rows={3}
                                                            value={attendance.note || ''}
                                                            onChange={(e) => handleAttendanceChange(attendance.userId, 'note', e.target.value)}
                                                            placeholder="Nhập ghi chú..."
                                                            className="w-full"
                                                            showSavingIndicator={true}
                                                            isLoading={savingStates[`${attendance.userId}_note`]}
                                                            isPending={pendingChanges[`${attendance.userId}_note`]}
                                                            defaultSuggestions={[
                                                                "Con nói chuyện riêng trong giờ",
                                                                "Con dùng điện thoại trong giờ",
                                                                "Con nói chuyện riêng và dùng điện thoại trong giờ",
                                                                "Con không hoàn thành bài tập trên lớp",
                                                            ]}
                                                        />
                                                    </td>
                                                    <td className="border border-gray-200 p-3 relative" style={{ zIndex: 100 - idx }}>
                                                        <CustomTextareaDropdown
                                                            rows={3}
                                                            value={attendance.assignmentFeedback || ''}
                                                            onChange={(e) => handleAttendanceChange(attendance.userId, 'assignmentFeedback', e.target.value)}
                                                            placeholder="Nhận xét bài tập..."
                                                            className="w-full"
                                                            showSavingIndicator={true}
                                                            isLoading={savingStates[`${attendance.userId}_assignmentFeedback`]}
                                                            isPending={pendingChanges[`${attendance.userId}_assignmentFeedback`]}
                                                            defaultSuggestions={FEEDBACK.CLASSWORK}
                                                        />
                                                    </td>
                                                    <td className="border border-gray-200 p-3">
                                                        {renderMultipleBTVNStatus(attendance.btvnStatuses, attendance.btvnSummary)}
                                                    </td>
                                                    <td className="border border-gray-200 p-3">
                                                        {renderWeeklyAttendances(attendance.weeklyAttendances)}
                                                    </td>
                                                    <td className="border border-gray-200 p-3 text-center">
                                                        <div className="flex items-center justify-center gap-2">
                                                            <input
                                                                type="checkbox"
                                                                checked={attendance.hasMessagedParent}
                                                                onChange={(e) => handleAttendanceChange(attendance.userId, 'hasMessagedParent', e.target.checked)}
                                                                className="cursor-pointer form-checkbox h-4 w-4 text-blue-600"
                                                            />
                                                        </div>
                                                    </td>
                                                    <td className="border border-gray-200 p-3 text-center">
                                                        <div className="flex flex-col items-center gap-2 justify-center">
                                                            {attendance.id && (
                                                                <button
                                                                    onClick={() => handleDeleteClick(attendance.id)}
                                                                    className="flex items-center gap-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                                                                >
                                                                    <Trash2 size={14} />
                                                                </button>
                                                            )}
                                                            <button
                                                                onClick={() => handleGenerateAttendanceSheet(attendance)}
                                                                disabled={generatingSheet}
                                                                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
                                                                title="Xuất phiếu điểm danh"
                                                            >
                                                                <FileImage size={14} />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>

                                {/* Pagination */}
                                <div className="mt-4 flex justify-center">
                                    <Pagination
                                        currentPage={currentPage}
                                        totalItems={totalItems}
                                        limit={pageSize}
                                        onPageChange={handlePageChange}
                                    />
                                </div>
                            </>
                        ) : (
                            <div className="text-center py-8 text-gray-500">
                                <Users size={48} className="mx-auto mb-4 text-gray-300" />
                                <p>Chưa có dữ liệu điểm danh cho buổi học này</p>
                                <p className="text-sm">Nhấn "Tạo điểm danh" để bắt đầu</p>
                            </div>
                        )}
                    </div>
                )}

                {!selectedLesson && (
                    <div className="flex-1 flex items-center justify-center text-gray-500">
                        <div className="text-center">
                            <Calendar size={48} className="mx-auto mb-4 text-gray-300" />
                            <p>Vui lòng chọn một buổi học để bắt đầu điểm danh</p>
                        </div>
                    </div>
                )}

                {/* Confirm Delete Modal */}
                <ConfirmModal
                    isOpen={showDeleteModal}
                    onClose={handleDeleteCancel}
                    onConfirm={handleDeleteConfirm}
                    title="Xác nhận xóa"
                    message="Bạn có chắc chắn muốn xóa bản ghi điểm danh này? Hành động này không thể hoàn tác."
                />


                <ConfirmModal
                    isOpen={showAddStudentToClass && addStudent}
                    onClose={handleDeleteAddStudentToClass}
                    onConfirm={handleAddStudentToClass}
                    loading={loadingAdd}
                    title="Xác nhận thêm học sinh vào lớp"
                    message="Học sinh này chưa có trong lớp học. Bạn có chắc chắn muốn thêm học sinh này vào lớp không?"
                    color="green"
                />

                {/* Attendance Sheet Modal */}
                {showAttendanceSheet && attendanceSheetData && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <OutsideClickWrapper
                            onClickOutside={() => setShowAttendanceSheet(false)}
                            className="bg-white rounded-lg p-6 max-w-4xl ">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-semibold text-gray-800">Phiếu điểm danh</h3>
                                <button
                                    onClick={() => setShowAttendanceSheet(false)}
                                    className="text-gray-500 hover:text-gray-700"
                                >
                                    ✕
                                </button>
                            </div>

                            <div className="text-center mb-4">
                                <img
                                    src={attendanceSheetData.imageUrl}
                                    alt="Phiếu điểm danh"
                                    className="w-auto max-w-full max-h-[70vh] border border-gray-300 rounded-lg shadow-sm"
                                />
                            </div>


                            <div className="flex justify-center gap-4">
                                <button
                                    onClick={handleDownloadAttendanceSheet}
                                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                                >
                                    <Download size={16} />
                                    Tải về
                                </button>
                                {currentStudentInfo && (currentStudentInfo.phone || currentStudentInfo.studentPhone) && (
                                    <button
                                        onClick={handleSendZaloMessage}
                                        className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                                        title={`Nhắn tin Zalo đến ${currentStudentInfo.phone || currentStudentInfo.studentPhone}`}
                                    >
                                        <MessageCircle size={16} />
                                        Mở Zalo PH
                                    </button>
                                )}
                                <button
                                    onClick={() => setShowAttendanceSheet(false)}
                                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                                >
                                    Đóng
                                </button>
                            </div>
                        </OutsideClickWrapper>
                    </div>
                )}


            </div>
        </ClassAdminLayout>
    );
};

export default AttendancePage;
