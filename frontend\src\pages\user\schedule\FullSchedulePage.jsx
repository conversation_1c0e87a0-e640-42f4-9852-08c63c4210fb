import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Calendar, Clock, ChevronLeft, ChevronRight, CheckCircle, User, Phone, GraduationCap, BadgeInfo, LayoutPanelTop, Columns3, LayoutGrid } from 'lucide-react';
import UserLayout from '../../../layouts/UserLayout';
import { fetchClassesOverview } from '../../../features/class/classSlice';
import { getLearningItemWeekend } from '../../../features/learningItem/learningItemSlice';
import CalendarMonth from 'src/components/calendar/CalenderMonth';
import { resetCalendar, setSelectedDay, setCalendarView } from 'src/features/calendar/calendarSlice';
import WeekView from 'src/components/calendar/WeekView';
import DayView from 'src/components/calendar/DayView';
import MonthView from 'src/components/calendar/MonthView';
import { formatDate } from '../../../utils/formatters';
import { setOpenStudentCardModal } from "../../../features/auth/authSlice";



const ViewControls = () => {
    const dispatch = useDispatch();
    return (
        <div className="flex flex-col items-start gap-3 p-4 sticky top-[6rem]">
            {/* Calendar controls - style giống OverViewPage */}
            <div className="w-full space-y-3">
                <p className="text-gray-900 font-semibold">Điều khiển lịch</p>
                <button
                    onClick={() => dispatch(resetCalendar())}
                    className="w-full text-sm p-1.5 rounded-md bg-cyan-100 text-cyan-700 font-medium hover:bg-cyan-200 transition-colors duration-200 flex items-center justify-center gap-2"
                >
                    <Calendar size={16} />
                    Về hôm nay
                </button>
            </div>

            <hr className="w-full border-gray-200 my-3" />

            {/* View controls */}
            <CalendarControls />
        </div>
    );
};

const CalendarControls = () => {
    const dispatch = useDispatch();
    const { view } = useSelector((state) => state.calendar);

    const handleViewChange = (newView) => {
        dispatch(setCalendarView(newView));
    };

    return (
        <div className="w-full space-y-3">
            <p className="text-gray-900 font-semibold">Chế độ xem</p>

            <div className="flex flex-col gap-2">
                <button
                    onClick={() => handleViewChange('day')}
                    className={`w-full px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-2 ${view === 'day'
                            ? 'bg-cyan-100 text-cyan-700 border border-cyan-200'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                >
                    <LayoutPanelTop size={16} />
                    Ngày
                </button>
                <button
                    onClick={() => handleViewChange('week')}
                    className={`w-full px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-2 ${view === 'week'
                            ? 'bg-cyan-100 text-cyan-700 border border-cyan-200'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                >
                    <Columns3 size={16} />
                    Tuần
                </button>
                <button
                    onClick={() => handleViewChange('month')}
                    className={`w-full px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-2 ${view === 'month'
                            ? 'bg-cyan-100 text-cyan-700 border border-cyan-200'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                >
                    <LayoutGrid size={16} />
                    Tháng
                </button>
            </div>

            {/* Mini Calendar */}
            <CalendarMonth />
        </div>
    );
};

// Component hiển thị calendar - style giống OverViewPage
const CalendarContainer = () => {
    const { view } = useSelector((state) => state.calendar);
    const [isMobile, setIsMobile] = useState(false);

    const formatDateLocal = (dateStr) => {
        const d = new Date(dateStr);
        return `${String(d.getDate()).padStart(2, '0')}-${String(d.getMonth() + 1).padStart(2, '0')}`;
    };

    // Check if the device is mobile
    useEffect(() => {
        const checkIfMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };

        checkIfMobile();
        window.addEventListener('resize', checkIfMobile);

        return () => {
            window.removeEventListener('resize', checkIfMobile);
        };
    }, []);

    const getClassHeight = (startTime, endTime) => {
        // Format times to HH:MM if they are in HH:MM:SS format
        const formattedStartTime = startTime.length > 5 ? startTime.substring(0, 5) : startTime;
        const formattedEndTime = endTime.length > 5 ? endTime.substring(0, 5) : endTime;

        const [sh, sm] = formattedStartTime.split(':').map(Number);
        const [eh, em] = formattedEndTime.split(':').map(Number);
        const durationHours = (eh - sh) + (em - sm) / 60;
        return `${durationHours * 3}rem`;
    };

    const getWeekDays = (startOfWeek) => {
        if (!startOfWeek) return [];

        const labels = [
            { short: 'T2', long: 'Thứ 2' },
            { short: 'T3', long: 'Thứ 3' },
            { short: 'T4', long: 'Thứ 4' },
            { short: 'T5', long: 'Thứ 5' },
            { short: 'T6', long: 'Thứ 6' },
            { short: 'T7', long: 'Thứ 7' },
            { short: 'CN', long: 'Chủ nhật' },
        ];

        const days = [];
        for (let i = 0; i < 7; i++) {
            const date = new Date(startOfWeek);
            date.setDate(startOfWeek.getDate() + i);

            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');

            days.push({
                ...labels[i],
                date: `${day}-${month}`,
                fullDate: date,
            });
        }

        return days;
    };

    const { startOfWeek } = useSelector((state) => state.calendar);
    const weekDays = getWeekDays(new Date(startOfWeek));

    return (
        <div className="flex flex-col gap-3">
            <p className="text-gray-900 font-semibold">Lịch học của bạn</p>
            <div className="bg-white rounded-lg border border-gray-200">
                {view === 'week' ? (
                    <WeekView weekDays={weekDays} formatDate={formatDateLocal} getClassHeight={getClassHeight} isMobile={isMobile} />
                ) : view === 'month' ? (
                    <MonthView isMobile={isMobile} />
                ) : view === 'day' ? (
                    <DayView formatDate={formatDateLocal} getClassHeight={getClassHeight} isMobile={isMobile} />
                ) : (
                    <CalendarMonth />
                )}
            </div>
        </div>
    );
};

const FullSchedulePage = () => {
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(fetchClassesOverview());
    }, [dispatch]);

    return (
        <UserLayout>
            <div className="container flex md:flex-row flex-col">
                <div className="md:w-1/4 w-full flex flex-col justify-start">
                    <ViewControls />
                </div>
                <div className="flex-1 flex flex-col justify-start p-4 gap-3">
                    <CalendarContainer />
                </div>
            </div>
        </UserLayout>
    );
};

export default FullSchedulePage;