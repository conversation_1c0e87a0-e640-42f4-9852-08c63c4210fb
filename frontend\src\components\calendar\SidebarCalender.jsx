import { Calendar, ChevronDown, LayoutPanelTop, Columns3, LayoutGrid } from 'lucide-react';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { resetCalendar, setCalendarView } from 'src/features/calendar/calendarSlice';
import CalendarMonth from 'src/components/calendar/CalenderMonth';

const CalendarControls = () => {
    const dispatch = useDispatch();
    const { view } = useSelector((state) => state.calendar);

    const handleViewChange = (newView) => {
        dispatch(setCalendarView(newView));
    };

    const vietnameseViews = {
        day: 'Ngày',
        week: 'Tuần',
        month: 'Tháng',
    };

    return (
        <div className="w-full space-y-3">
            {/* Title - style giống OverViewPage */}
            <p className="text-gray-900 font-semibold">Chế độ xem</p>
            
            {/* View controls - style giống OverViewPage buttons */}
            <div className="flex flex-col gap-2">
                <button
                    onClick={() => handleViewChange('day')}
                    className={`w-full px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-2 ${
                        view === 'day' 
                            ? 'bg-cyan-100 text-cyan-700 border border-cyan-200' 
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                >
                    <LayoutPanelTop size={16} />
                    Ngày
                </button>
                <button
                    onClick={() => handleViewChange('week')}
                    className={`w-full px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-2 ${
                        view === 'week' 
                            ? 'bg-cyan-100 text-cyan-700 border border-cyan-200' 
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                >
                    <Columns3 size={16} />
                    Tuần
                </button>
                <button
                    onClick={() => handleViewChange('month')}
                    className={`w-full px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center justify-center gap-2 ${
                        view === 'month' 
                            ? 'bg-cyan-100 text-cyan-700 border border-cyan-200' 
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                >
                    <LayoutGrid size={16} />
                    Tháng
                </button>
            </div>

            {/* Mini Calendar */}
            <CalendarMonth />
        </div>
    );
};

export default CalendarControls;
