import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import {
  fetchUserTuitionPaymentById,
  clearTuitionPayment,
} from "src/features/tuition/tuitionSlice";
import { formatCurrency, formatDate } from "src/utils/formatters";
import { setOpenStudentCardModal } from "src/features/auth/authSlice";
import UserLayout from "src/layouts/UserLayout";
import PaymentModal from "src/components/PaymentModal";
import {
  CreditCard,
  ArrowLeft,
  FileText,
  Calendar,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock,
  Receipt,
  Loader,
  ChevronRight,
  User,
  GraduationCap,
  Phone,
  BadgeInfo,
} from "lucide-react";

// Component AvatarUser giống OverViewPage
const AvatarUser = () => {
    const { user } = useSelector(state => state.auth);

    return (
        <div className="aspect-square md:w-full w-24 rounded-full overflow-hidden border border-gray-300 flex items-center justify-center">
            {user?.avatarUrl ? (
                <img
                    src={user.avatarUrl}
                    alt="avatar"
                    className="w-full h-full object-cover"
                />
            ) : (
                <svg className="w-full h-full text-gray-400" viewBox="0 0 40 40" fill="none">
                    <path
                        d="M20 2.5C10.335 2.5 2.5 10.335 2.5 20C2.5 29.665 10.335 37.5 20 37.5C29.665 37.5 37.5 29.665 37.5 20C37.5 10.335 29.665 2.5 20 2.5ZM20 22.5C16.6983 22.5 14.1667 19.88 14.1667 16.6667C14.1667 13.4533 16.6983 10.8333 20 10.8333C23.3017 10.8333 25.8333 13.4533 25.8333 16.6667C25.8333 19.88 23.3017 22.5 20 22.5ZM10 32.3C10.1 30.38 10.8 29.03 11.73 28.07C12.72 27.05 14 26.41 15.2 26.03C15.41 25.96 15.73 26.01 16.09 26.26C16.88 26.8 18.25 27.5 20 27.5C21.75 27.5 23.12 26.8 23.91 26.26C24.27 26.01 24.59 25.96 24.8 26.03C26 26.41 27.28 27.05 28.27 28.07C29.2 29.03 29.9 30.39 30 32.29C27.18 34.59 23.65 35.84 20 35.83C16.35 35.84 12.82 34.58 10 32.3Z"
                        fill="#94A3B8"
                    />
                </svg>
            )}
        </div>
    );
};

const InfoRow = ({ icon, label, value }) => (
    <div className="flex items-center gap-2 text-xs">
        <span className="text-gray-600">{icon}</span>
        <span className="w-28 font-medium text-gray-900">{label}:</span>
        <span className="text-gray-700">{value}</span>
    </div>
);

// Component InformationUser giống OverViewPage
const InformationUser = () => {
    const { user } = useSelector(state => state.auth);
    const { tuitionPayment } = useSelector(state => state.tuition);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    
    if (!user) return null;

    return (
        <div className="flex flex-col items-start gap-3 p-4 sticky top-[6rem]">
            <div className="flex flex-row md:flex-col items-center gap-3">
                <AvatarUser />
                <div className="text-center flex md:justify-between flex-row w-full md:mt-3 gap-2">
                    <div className="flex flex-col gap-1">
                        <p className="text-lg font-semibold text-gray-900 whitespace-nowrap overflow-hidden text-ellipsis">
                            {user.lastName} {user.firstName}
                        </p>
                        <p className="text-sm text-gray-500 hidden sm:block">@{user.username}</p>
                    </div>
                    <p className="text-sm sm:flex text-gray-500 hidden items-center justify-center gap-1">
                        <BadgeInfo size={16} className="text-gray-500 " /> {user.id}
                    </p>
                </div>
            </div>
            <hr className="w-full border-gray-200 my-3" />

            <div className="w-full flex flex-col gap-3 text-sm text-gray-700">
                <div className="sm:hidden flex">
                    <InfoRow icon={<BadgeInfo size={16} />} label="ID" value={user.id} />
                </div>
                <InfoRow icon={<User size={16} />} label="Giới tính" value={user.gender ? "Nam" : "Nữ"} />
                <InfoRow icon={<Calendar size={16} />} label="Ngày sinh" value={formatDate(user.birthDate)} />
                <InfoRow icon={<GraduationCap size={16} />} label="Lớp" value={user.class || "Chưa cập nhật"} />
                <InfoRow icon={<Phone size={16} />} label="Số điện thoại" value={user.phone || "Chưa cập nhật"} />
            </div>
            <hr className="w-full border-gray-200 my-3" />
            
            {/* Quick Actions */}
            <div className="w-full space-y-3">
                <p className="text-gray-900 font-semibold">Thao tác nhanh</p>
                <button
                    onClick={() => navigate("/tuition-payments")}
                    className="w-full text-sm p-1.5 rounded-md bg-cyan-100 text-cyan-700 font-medium hover:bg-cyan-200 transition-colors duration-200 flex items-center justify-center gap-2"
                >
                    <ArrowLeft size={16} />
                    Quay lại danh sách
                </button>
                
                {tuitionPayment && !tuitionPayment.isPaid && (
                    <button
                        onClick={() => {
                            // Trigger payment modal from parent component
                            const event = new CustomEvent('openPaymentModal');
                            window.dispatchEvent(event);
                        }}
                        className="w-full text-sm p-1.5 rounded-md bg-green-100 text-green-700 font-medium hover:bg-green-200 transition-colors duration-200 flex items-center justify-center gap-2"
                    >
                        <FileText size={16} />
                        Thanh toán ngay
                    </button>
                )}
            </div>
            
            <hr className="w-full border-gray-200 my-3" />
            
            <button
                onClick={() => dispatch(setOpenStudentCardModal(true))}
                className="w-full text-sm p-1.5 rounded-md bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center"
            >
                Chỉnh sửa thông tin cá nhân
            </button>
        </div>
    );
};

const UserTuitionPaymentDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const { tuitionPayment, loading, studentClassTuitions } = useSelector((state) => state.tuition);
  // paymentProgress removed - không còn sử dụng với schema mới
  const [classTuitionsLoading, setClassTuitionsLoading] = useState(false);

  // State cho modal thanh toán
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState(null);

  useEffect(() => {
    dispatch(fetchUserTuitionPaymentById(id));

    return () => {
      dispatch(clearTuitionPayment());
    };
  }, [dispatch, id]);

  // Listen for payment modal event from sidebar
  useEffect(() => {
    const handleOpenPaymentModal = () => {
      if (tuitionPayment && !tuitionPayment.isPaid) {
        handleOpenPaymentModal();
      }
    };

    window.addEventListener('openPaymentModal', handleOpenPaymentModal);
    return () => window.removeEventListener('openPaymentModal', handleOpenPaymentModal);
  }, [tuitionPayment]);

  // useEffect(() => {
  //   if (tuitionPayment) {
  //     setClassTuitionsLoading(true);
  //     dispatch(fetchStudentClassTuitionsByMonth(tuitionPayment.month))
  //       .unwrap()
  //       .then(() => {
  //         setClassTuitionsLoading(false);
  //       })
  //       .catch((error) => {
  //         console.error("Error fetching class tuitions:", error);
  //         setClassTuitionsLoading(false);
  //       });
  //   }
  // }, [dispatch, tuitionPayment]);

  // useEffect for paymentProgress removed - không còn sử dụng với schema mới

  const handleOpenPaymentModal = () => {
    if (!tuitionPayment) return;

    setPaymentInfo({
      id: tuitionPayment.id,
      month: tuitionPayment.monthFormatted,
      amount: "Liên hệ anh Triệu Minh để biết số tiền", // Không còn expectedAmount/paidAmount
      note: tuitionPayment.note,
      description: `${user.firstName} ${user.lastName} ${user.highSchool} HP_${tuitionPayment.monthFormatted.replace(' ', '_')}_${tuitionPayment.id}`
    });
    setIsPaymentModalOpen(true);
  };

  const handleClosePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setPaymentInfo(null);
  };

  const getStatusBadge = (status, isOverdue) => {
    if (status === "PAID") {
      return (
        <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
          Đã thanh toán
        </span>
      );
    } else if (isOverdue) {
      return (
        <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
          Quá hạn
        </span>
      );
    } else if (status === "PARTIAL") {
      return (
        <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
          Thanh toán một phần
        </span>
      );
    } else {
      return (
        <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
          Chưa thanh toán
        </span>
      );
    }
  };

  const getPaymentStatusIcon = (status, isOverdue) => {
    if (status === "PAID") {
      return (
        <div className="p-3 bg-green-100 rounded-full">
          <CheckCircle className="w-6 h-6 text-green-600" />
        </div>
      );
    } else if (isOverdue) {
      return (
        <div className="p-3 bg-red-100 rounded-full">
          <AlertCircle className="w-6 h-6 text-red-600" />
        </div>
      );
    } else if (status === "PARTIAL") {
      return (
        <div className="p-3 bg-blue-100 rounded-full">
          <DollarSign className="w-6 h-6 text-blue-600" />
        </div>
      );
    } else {
      return (
        <div className="p-3 bg-yellow-100 rounded-full">
          <CreditCard className="w-6 h-6 text-yellow-600" />
        </div>
      );
    }
  };

  if (loading) {
    return (
      <UserLayout>
        <div className="container flex md:flex-row flex-col">
          <div className="md:w-1/4 w-full flex flex-col justify-start ">
            <InformationUser />
          </div>
          <div className="flex-1 flex flex-col justify-start p-4 gap-3">
            <div className="p-8 text-center text-gray-500">
              <Loader size={40} className="mx-auto mb-4 text-gray-300 animate-spin" />
              <p>Đang tải thông tin học phí...</p>
            </div>
          </div>
        </div>
      </UserLayout>
    );
  }

  if (!tuitionPayment) {
    return (
      <UserLayout>
        <div className="container flex md:flex-row flex-col">
          <div className="md:w-1/4 w-full flex flex-col justify-start ">
            <InformationUser />
          </div>
          <div className="flex-1 flex flex-col justify-start p-4 gap-3">
            <div className="p-8 text-center text-gray-500">
              <AlertCircle size={40} className="mx-auto mb-4 text-gray-300" />
              <p>Không tìm thấy thông tin học phí.</p>
              <button
                onClick={() => navigate("/tuition-payments")}
                className="mt-4 px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 transition-colors"
              >
                Quay lại danh sách học phí
              </button>
            </div>
          </div>
        </div>
      </UserLayout>
    );
  }

  return (
    <UserLayout>
      <div className="container flex md:flex-row flex-col">
        <div className="md:w-1/4 w-full flex flex-col justify-start ">
          <InformationUser />
        </div>
        <div className="flex-1 flex flex-col justify-start p-4 gap-3">
          {/* Breadcrumb */}
          <div className="flex items-center mb-6 text-sm">
            <button
              onClick={() => navigate("/tuition-payments")}
              className="text-gray-500 hover:text-cyan-600 flex items-center gap-1"
            >
              Danh sách học phí
            </button>
            <ChevronRight size={16} className="mx-2 text-gray-400" />
            <span className="text-gray-700">Chi tiết học phí</span>
          </div>

          {/* Header */}
          <div className="flex flex-col gap-3">
            <div className="flex justify-between items-center">
              <h1 className="text-gray-900 font-semibold flex items-center gap-2">
                <CreditCard className="text-cyan-600" />
                Chi tiết học phí {tuitionPayment.monthFormatted}
              </h1>
              {getStatusBadge(tuitionPayment.isPaid ? 'PAID' : tuitionPayment.isOverdue ? 'OVERDUE' : 'UNPAID', tuitionPayment.isOverdue)}
            </div>

            {/* Thông tin chính */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6 flex gap-6">
                {getPaymentStatusIcon(tuitionPayment.isPaid ? 'PAID' : tuitionPayment.isOverdue ? 'OVERDUE' : 'UNPAID', tuitionPayment.isOverdue)}
                <div className="flex-1">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Trạng thái thanh toán</p>
                      <p className="text-xl font-semibold">
                        {tuitionPayment.isPaid ? (
                          <span className="text-green-600">Đã thanh toán</span>
                        ) : tuitionPayment.isOverdue ? (
                          <span className="text-red-600">Quá hạn</span>
                        ) : (
                          <span className="text-yellow-600">Chưa thanh toán</span>
                        )}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Ghi chú</p>
                      <p className="text-xl font-semibold text-gray-600">
                        {tuitionPayment.note || "Không có ghi chú"}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Hạn thanh toán</p>
                      <p className="text-base flex items-center gap-1">
                        <Calendar size={16} className="text-gray-400" />
                        {tuitionPayment.dueDateFormatted || "Chưa có hạn thanh toán"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Ngày thanh toán</p>
                      <p className="text-base flex items-center gap-1">
                        <Calendar size={16} className="text-gray-400" />
                        {tuitionPayment.paymentDateFormatted || "Chưa thanh toán"}
                      </p>
                    </div>
                  </div>

                  {tuitionPayment.note && (
                    <div className="mt-6 p-4 bg-gray-50 rounded-md">
                      <p className="text-sm text-gray-500 mb-1">Ghi chú</p>
                      <p className="text-base">{tuitionPayment.note}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Action buttons - moved to sidebar for consistency */}
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={handleClosePaymentModal}
        paymentInfo={paymentInfo}
      />
    </UserLayout>
  );
};

export default UserTuitionPaymentDetail;
