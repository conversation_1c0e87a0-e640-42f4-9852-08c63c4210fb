import UserLayout from "../../../layouts/UserLayout";
import { useSelector, useDispatch } from "react-redux";
import { useParams, useLocation } from "react-router-dom";
import { fetchExamRatingStatistics, fetchPublicExamById, saveExamForUser, fetchRelatedExamsIfNeeded, rateExamForUser, setStar } from "../../../features/exam/examDetailSlice";
import { fetchCodesByType } from "../../../features/code/codeSlice";
import { useEffect, useState, useRef } from "react";
import {
    StarIcon,
    Info,
    RefreshCcw,
    BookOpen,
    Calendar,
    Award,
    FileText,
    BarChart2,
    History,
    QrCode as QrCodeIcon,
    Pin,
    Eye,
    Play,
    Clock,
    GraduationCap,
    ListOrdered,
    StickyNote,
    ExternalLink,
    Send,
    Smile,
    Share2
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { setView } from "../../../features/exam/examDetailSlice";
import { formatDate } from "src/utils/formatters";
import QRCodeComponent from "src/components/QrCode";
import YouTubePlayer from "src/components/YouTubePlayer";
import { fetchCommentsByExamId, postComment, putComment, deleteComment, setCurrentPage } from "src/features/comments/ExamCommentsSlice";
import CommentSection from "src/components/comment/CommentSection";
import LoadingText from "src/components/loading/LoadingText";
import RankingView from "src/components/examDetail/RankingView";
import PreviewView from "src/components/examDetail/PreviewView";
import HistoryView from "src/components/examDetail/HistoryView";
import { setErrorMessage } from "src/features/state/stateApiSlice";
import ExamOverviewHeader from "src/components/header/ExamOverviewHeader";
import ActionButton from "src/components/button/ActionButton";
import UserType from "src/constants/UserType";

const checkRole = (check, role = UserType.STUDENT) => {
    return check || role !== UserType.STUDENT;
}

const ActionButtons = ({ handleClickHistory, handleClickRanking, handleClickPreviewExam }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { view, exam } = useSelector((state) => state.examDetail);
    return (
        <div className="flex flex-wrap gap-2">
            <ActionButton
                icon={Info}
                title="Thông tin chi tiết"
                shortTitle="Chi tiết"
                isActive={view === "detail"}
                onClick={() => dispatch(setView("detail"))}
            />
            <ActionButton
                icon={Award}
                title="Bảng xếp hạng"
                shortTitle="Xếp hạng"
                isActive={view === "ranking"}
                onClick={() => handleClickRanking()}
            />
            <ActionButton
                icon={Eye}
                title="Xem đề thi"
                shortTitle="Xem đề"
                isActive={view === "preview"}
                onClick={() => handleClickPreviewExam()}
            />
            <ActionButton
                icon={History}
                title="Lịch sử làm bài"
                shortTitle="Lịch sử"
                isActive={view === "history"}
                onClick={() => handleClickHistory()}
            />
        </div>
    );
};

const DoExamButton = ({ onClick, disabled = false }) => {
    const { loading } = useSelector((state) => state.states);
    return (
        <LoadingText loading={loading} w="w-20" h="h-[30px]">
            <button
                onClick={onClick}
                disabled={disabled}
                className={`flex-shrink-0 flex items-center gap-2 px-3 py-[7px] rounded-md text-xs font-medium transition-all
                ${disabled
                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                        : 'bg-green-600 text-white hover:bg-green-700'}
            `}
            >
                <Play size={16} />
                <span>Làm bài</span>
            </button>
        </LoadingText>

    );
};

const InfoRow = ({ icon: Icon, label, value, w = "w-48" }) => {
    const { loading } = useSelector((state) => state.states);
    return (
        <div className="flex items-center justify-between gap-2 text-sm text-gray-800">
            <div className="flex items-center gap-2">
                <Icon size={16} className="text-sky-600" />
                <span className="font-medium text-gray-800">{label}:</span>
            </div>
            <LoadingText loading={loading} w={w}>
                <span>{value}</span>
            </LoadingText>
        </div>
    );
};

const InfoExam = () => {
    const { exam, loading } = useSelector((state) => state.examDetail);
    const { codes } = useSelector((state) => state.codes);
    return (
        <div className="flex flex-col border border-gray-300 rounded-md">
            {/** Header **/}
            <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                <LoadingText loading={loading} w="w-48">
                    <p className="font-Inter text-sm font-semibold">{exam?.name}</p>
                </LoadingText>
                <div className="flex items-center gap-2 text-gray-500">
                    <RefreshCcw size={16} className="flex-shrink-0" />
                    <LoadingText loading={loading} w="w-40" color="bg-gray-200">
                        <p className="text-xs"><span className="hidden sm:inline">Cập nhật:</span> {formatDate(exam?.updatedAt)}</p>
                    </LoadingText>
                </div>
            </div>
            <div className="flex flex-row gap-3 p-4 bg-white ">
                <div className="w-full flex flex-col sm:flex-row gap-4 items-center">
                    {/* Bên trái: ảnh hoặc QR */}
                    <div className="w-full sm:w-1/3 flex justify-center items-center">
                        {exam?.imageUrl ? (
                            <img
                                src={exam.imageUrl}
                                alt="Exam"
                                className="w-full max-w-[200px] rounded-md shadow border"
                            />
                        ) : (
                            <div className="bg-white p-2 rounded-md border">
                                <QRCodeComponent
                                    url={`https://toanthaybee.edu.vn/practice/exam/${exam?.id}`} // hoặc `https://toanthaybee.edu.vn/practice/exam/${exam.id}` hoặc `https://toanthaybee.edu.vn/practice/exam/${exam.id}`
                                    size={128}
                                />
                            </div>
                        )}
                    </div>

                    {/* Bên phải: thông tin */}
                    <div className="w-full flex flex-col sm:gap-4 gap-2">
                        <InfoRow
                            icon={BookOpen}
                            label="Loại đề"
                            value={codes?.["exam type"]?.find(c => c.code === exam?.typeOfExam)?.description || exam?.typeOfExam || "Không rõ"}
                            w="w-56"
                        />
                        <InfoRow
                            icon={GraduationCap}
                            label="Khối"
                            value={exam?.class || "Không rõ"}
                            w="w-48"
                        />
                        <InfoRow
                            icon={Calendar}
                            label="Năm"
                            value={exam?.year || "Không rõ"}
                            w="w-40"
                        />
                        <InfoRow
                            icon={Clock}
                            label="Thời gian"
                            value={exam?.testDuration ? `${exam.testDuration} phút` : "Vô thời hạn"}
                            w="w-56"
                        />
                        <InfoRow
                            icon={ListOrdered}
                            label="Chương"
                            value={codes?.["chapter"]?.find(c => c.code === exam?.chapter)?.description || exam?.chapter || "Không rõ"}
                            w="w-56"
                        />
                        <InfoRow
                            icon={BarChart2}
                            label="Tỉ lệ đạt"
                            value={exam?.passRate ? `${exam.passRate}%` : "Không có"}
                            w="w-48"
                        />
                    </div>
                </div>

                {/* Mô tả */}
                {exam?.description && (
                    <div className="flex items-start gap-2 text-sm text-gray-600">
                        <StickyNote size={16} className="text-sky-600 mt-0.5" />
                        <span className="font-medium text-gray-800">Mô tả:</span>
                        <span className="leading-relaxed">{exam.description}</span>
                    </div>
                )}
            </div>

        </div>
    )
}

const SolutionVideo = () => {
    const { exam, loading, ratingStatistics, loadingRatingStatistics } = useSelector((state) => state.examDetail);
    const { user } = useSelector((state) => state.auth);
    return (
        <div className="flex flex-col border border-gray-300 rounded-md">
            <div className="flex justify-between p-3 border-b border-gray-300 bg-[#f6f8fa]">
                <p className="text-sm font-Inter font-semibold">Video Chữa bài</p>
                <LoadingText loading={loadingRatingStatistics || loading} w="w-40">
                    {ratingStatistics?.studentStatus?.isDone && exam?.seeCorrectAnswer && exam?.solutionUrl ? (
                        <button
                            onClick={() => window.open(exam?.solutionUrl, "_blank")}
                            className="text-xs flex items-center gap-2 px-2 py-1 rounded-md transition-all text-gray-700 hover:bg-gray-200"
                        >
                            <ExternalLink size={16} />
                            <span>Mở trong tab mới</span>
                        </button>
                    ) : (
                        <button
                            className="text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 cursor-not-allowed"
                        >
                            <span>Chưa làm</span>
                        </button>
                    )}
                </LoadingText>

            </div>
            {(loading || loadingRatingStatistics) && (<div className="p-4 w-full h-[436px]">
                <LoadingText loading={loading || loadingRatingStatistics} w="w-full" h="h-full" rounded="rounded-md" />
            </div>)}

            {!loading && !loadingRatingStatistics && (
                <>
                    {ratingStatistics?.studentStatus?.isDone && exam?.seeCorrectAnswer && exam?.solutionUrl && (
                        <div className="p-4">
                            <YouTubePlayer url={exam?.solutionUrl} />
                        </div>
                    )}
                    {ratingStatistics?.studentStatus?.isDone && exam?.seeCorrectAnswer && !exam?.solutionUrl && (
                        <div className="p-4">
                            <p className="text-sm text-gray-600">Chưa có video hướng dẫn</p>
                        </div>
                    )}  
                    {!ratingStatistics?.studentStatus?.isDone && (
                        <div className="p-4">
                            <p className="text-sm text-gray-600">Chỉ có thể xem sau khi làm bài</p>
                        </div>
                    )}
                </>
            )}


        </div>
    )
}

const RelatedExamCardSkeleton = () => {
    return (
        <div className="bg-white rounded-md border border-gray-200 p-3 sm:p-4 flex flex-col gap-2 animate-pulse">
            <div className="flex justify-between items-center">
                <LoadingText loading={true} w="w-40" />
                <div className="w-20 h-6 rounded-md bg-gray-200" />
            </div>

            <div className="flex items-center gap-2 text-xs text-gray-500">
                <Clock size={14} />
                <LoadingText loading={true} w="w-20" h="h-3" />
            </div>

            <div className="flex items-center gap-2 text-xs text-gray-500">
                <Calendar size={14} />
                <LoadingText loading={true} w="w-24" h="h-3" />
            </div>
        </div>
    );
};


const RelatedExamCard = ({ exam, onClick, compact = false }) => {
    return (
        <div
            onClick={onClick}
            className=" bg-white rounded-md border border-gray-200 cursor-pointer p-3 sm:p-4 flex flex-col gap-2"
        >
            <div className="flex justify-between items-center">
                <p className="text-sm font-semibold text-gray-800 line-clamp-2">
                    {exam.name}
                </p>
                <button
                    onClick={onClick}
                    className="whitespace-nowrap text-xs flex items-center gap-2 px-2 py-1 rounded-md border transition-all border-gray-300 bg-[#f6f8fa] text-gray-700 hover:bg-gray-200"
                >
                    <span>Xem đề</span>
                </button>
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-500">
                <Clock size={14} />
                <span>{exam.testDuration ? `${exam.testDuration} phút` : "Vô thời hạn"}</span>
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-500">
                <Calendar size={14} />
                <span>Năm: {exam.year || "Không rõ"}</span>
            </div>
        </div>
    );
};

const RightPanel = ({ examId }) => {
    const { relatedExams, loadingRelatedExams, view } = useSelector((state) => state.examDetail);
    const navigate = useNavigate();
    return (
        <div className="sm:w-1/4 w-full flex flex-col gap-4 sticky top-28 self-start max-h-[calc(100vh-2rem)] overflow-y-auto">
            <p className="text-lg font-semibold">Bài thi khác</p>
            <div className="flex flex-col gap-2">
                {loadingRelatedExams ? (
                    <>
                        <RelatedExamCardSkeleton />
                        <RelatedExamCardSkeleton />
                        <RelatedExamCardSkeleton />
                        <RelatedExamCardSkeleton />
                        <RelatedExamCardSkeleton />
                    </>
                ) :
                    (relatedExams.map((exam) => (
                        <RelatedExamCard
                            key={exam.id}
                            exam={exam}
                            onClick={() => navigate(`/practice/exam/${exam.id}`)}
                            compact={true}
                        />
                    )))}
            </div>
        </div>
    )
}

const InformaTionView = ({ examId }) => {
    const navigate = useNavigate();
    const { exam, loading } = useSelector((state) => state.examDetail);
    const { comments, pagination } = useSelector((state) => state.comments);
    const shareLink = `${window.location.origin}/practice/exam/${examId}`;
    const dispatch = useDispatch();

    const handleCopyLink = () => {
        navigator.clipboard.writeText(shareLink);
    };

    const handleSendComment = (content) => {
        dispatch(postComment({ examId: exam.id, content }));
    };

    const handleUpdateComment = (commentId, content) => {
        dispatch(putComment({ commentId, content }));
    };

    const handleDeleteComment = (commentId) => {
        dispatch(deleteComment(commentId));
    };

    const handleReplyComment = (content, parentCommentId) => {
        dispatch(postComment({ examId: exam.id, content, parentCommentId }));
    };
    return (
        <>
            <InfoExam />
            <SolutionVideo />
            <CommentSection
                comments={comments}
                onSubmit={handleSendComment}
                onUpdate={handleUpdateComment}
                onDelete={handleDeleteComment}
                onReply={handleReplyComment}
            />
        </>

    )
}


const ExamDetail = () => {
    const { examId } = useParams();
    const location = useLocation();
    const { exam, loading, view, ratingStatistics, loadingRatingStatistics } = useSelector((state) => state.examDetail);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const { pagination } = useSelector((state) => state.comments);
    const { page } = pagination;
    const { user } = useSelector((state) => state.auth);

    const handleClickRanking = () => {
        // Cho phép xem BXH nếu:
        // 1. Đã làm bài và được phép xem đáp án, HOẶC
        // 2. Không cho phép làm bài nữa (hết thời gian)
        if (checkRole((ratingStatistics?.studentStatus?.isDone && exam?.seeCorrectAnswer) || (!exam?.acceptDoExam && exam?.seeCorrectAnswer), user?.userType)) {
            dispatch(setView("ranking"));
        } else if (!exam?.seeCorrectAnswer) {
            dispatch(setErrorMessage("Bạn chưa được phép xem BXH"));
        } else {
            dispatch(setErrorMessage("Bạn chưa làm bài thi này"));
        }
    }

    const handleClickPreviewExam = () => {
        // Cho phép xem đề thi nếu:
        // 1. Đã làm bài và được phép xem đáp án, HOẶC
        // 2. Không cho phép làm bài nữa (hết thời gian)
        if (checkRole((ratingStatistics?.studentStatus?.isDone && exam?.seeCorrectAnswer) || (!exam?.acceptDoExam && exam?.seeCorrectAnswer), user?.userType)) {
            dispatch(setView("preview"));
        } else if (!exam?.seeCorrectAnswer) {
            dispatch(setErrorMessage("Bạn chưa được phép xem đề thi"));
        } else {
            dispatch(setErrorMessage("Bạn chưa làm bài thi này"));
        }
    };
    const handleClickHistory = () => {
        if (checkRole(ratingStatistics?.studentStatus?.isDone && exam?.seeCorrectAnswer, user?.userType)) {
            dispatch(setView("history"));
            return;
        }
        if (!ratingStatistics?.studentStatus?.isDone) {
            dispatch(setErrorMessage("Bạn chưa làm bài thi này"));
            return;
        }
        if (!exam?.seeCorrectAnswer) {
            dispatch(setErrorMessage("Bạn chưa được phép xem lịch sử"));
            return;
        }

    };

    const handleDoExam = () => {
        navigate(`/practice/exam/${exam.id}/do`);
    };

    useEffect(() => {
        dispatch(fetchPublicExamById(examId));
        dispatch(fetchRelatedExamsIfNeeded(examId));
        dispatch(fetchCodesByType(["chapter", "exam type", "user type"]));
    }, [dispatch, examId]);

    useEffect(() => {
        dispatch(fetchCommentsByExamId({ examId, page }));
    }, [dispatch, examId, page]);

    const [firstRender, setFirstRender] = useState(false);

    useEffect(() => {
        if (firstRender) return;
        dispatch(setView("detail"));
        setFirstRender(true);
    }, [dispatch, firstRender]);

    // Handle URL query parameters for view switching
    useEffect(() => {
        if (!firstRender) return;
        const searchParams = new URLSearchParams(location.search);
        const viewParam = searchParams.get('view');
        if (viewParam) {
            if (viewParam === "detail") dispatch(setView(viewParam));
            else if (viewParam === "ranking") handleClickRanking();
            else if (viewParam === "preview") handleClickPreviewExam();
            else if (viewParam === "history") handleClickHistory();
        }
    }, [location.search, dispatch, firstRender]);

    return (
        <UserLayout>
            <div className="container flex flex-col mb-9">
                <ExamOverviewHeader exam={exam} />
                <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1 flex flex-col gap-4">
                        <div className="flex flex-row justify-between items-center gap-2">
                            <ActionButtons handleClickHistory={handleClickHistory}
                                handleClickRanking={handleClickRanking}
                                handleClickPreviewExam={handleClickPreviewExam}
                            />
                            <DoExamButton
                                onClick={handleDoExam}
                                disabled={!exam?.acceptDoExam}
                            />
                        </div>
                        {view === "detail" && <InformaTionView examId={examId} />}
                        {view === "ranking" && <RankingView examId={examId} />}
                        {view === "preview" && <PreviewView examId={examId} />}
                        {view === "history" && <HistoryView examId={examId} />}

                    </div>
                    <RightPanel examId={examId} />
                </div>
            </div>
        </UserLayout>
    )
}

// Component hiển thị đề thi liên quan


export default ExamDetail;
