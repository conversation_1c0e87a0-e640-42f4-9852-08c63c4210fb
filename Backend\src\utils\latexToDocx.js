import axios from 'axios';
import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Download image từ URL và convert sang buffer với tỷ lệ khung hình được bảo toàn
 * @param {string} imageUrl - URL của hình ảnh
 * @param {number} maxWidth - Chiều rộng tối đa
 * @param {number} maxHeight - Chiều cao tối đa
 * @returns {Promise<{buffer: Buffer, width: number, height: number}|null>} - Buffer và kích thước thực
 */
export const downloadImage = async (imageUrl, maxWidth = 600, maxHeight = 400) => {
    try {
        if (!imageUrl) return null;

        const response = await axios.get(imageUrl, {
            responseType: 'arraybuffer',
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        // L<PERSON>y thông tin kích thước gốc
        const originalImage = sharp(response.data);
        const metadata = await originalImage.metadata();

        // Tính toán kích thước mới giữ tỷ lệ khung hình
        let newWidth = metadata.width;
        let newHeight = metadata.height;

        // Nếu ảnh lớn hơn giới hạn, scale down giữ tỷ lệ
        if (newWidth > maxWidth || newHeight > maxHeight) {
            const widthRatio = maxWidth / newWidth;
            const heightRatio = maxHeight / newHeight;
            const ratio = Math.min(widthRatio, heightRatio);

            newWidth = Math.round(newWidth * ratio);
            newHeight = Math.round(newHeight * ratio);
        }

        // Resize ảnh với tỷ lệ chính xác
        const buffer = await originalImage
            .resize(newWidth, newHeight, {
                fit: 'contain', // Giữ tỷ lệ, không crop
                background: { r: 255, g: 255, b: 255, alpha: 1 } // Nền trắng nếu cần
            })
            .jpeg({
                quality: 85,
                progressive: true
            })
            .toBuffer();

        return {
            buffer,
            width: newWidth,
            height: newHeight
        };
    } catch (error) {
        console.warn('Error downloading image:', imageUrl, error.message);
        return null;
    }
};

/**
 * Phân tích text và trả về array chứa text và math objects - Đơn giản hóa
 * @param {string} text - Text chứa LaTeX expressions
 * @returns {Array} - Array chứa các text runs và math objects
 */
export const parseTextWithMath = (text) => {
    if (!text) return [{ type: 'text', content: text || '' }];

    const parts = [];
    let lastIndex = 0;

    // Regex để tìm LaTeX math expressions
    const mathRegex = /(\$\$[\s\S]*?\$\$|\$[^$]*?\$|\\\([^)]*?\\\)|\\\[[^\]]*?\\\])/g;

    let match;
    while ((match = mathRegex.exec(text)) !== null) {
        // Thêm text trước math expression
        if (match.index > lastIndex) {
            const beforeText = text.substring(lastIndex, match.index);
            if (beforeText) {
                const trimmedBeforeText = beforeText.trim();
                if (trimmedBeforeText) {
                    parts.push({ type: 'text', content: trimmedBeforeText });
                }
            }
        }

        // Xử lý math expression - giữ nguyên không convert
        const fullMatch = match[0];
        parts.push({
            type: 'math',
            content: fullMatch, // Giữ nguyên LaTeX gốc
            displayMode: fullMatch.startsWith('$$') || fullMatch.startsWith('\\[')
        });

        lastIndex = match.index + match[0].length;
    }

    // Thêm text còn lại
    if (lastIndex < text.length) {
        const remainingText = text.substring(lastIndex);
        if (remainingText) {
            const trimmedRemainingText = remainingText.trim();
            if (trimmedRemainingText) {
                parts.push({ type: 'text', content: trimmedRemainingText });
            }
        }
    }

    // Nếu không có math expressions, trả về text gốc
    if (parts.length === 0) {
        const trimmedText = text.trim();
        if (trimmedText) {
            parts.push({ type: 'text', content: trimmedText });
        }
    }

    return parts;
};

/**
 * Làm sạch text, loại bỏ các ký tự đặc biệt có thể gây lỗi trong DOCX
 * @param {string} text 
 * @returns {string}
 */
export const sanitizeText = (text) => {
    if (!text) return '';

    return text
        .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Loại bỏ control characters
        .replace(/\r\n/g, '\n') // Chuẩn hóa line breaks
        .replace(/\r/g, '\n');
};
