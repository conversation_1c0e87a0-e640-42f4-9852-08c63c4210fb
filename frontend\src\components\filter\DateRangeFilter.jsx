import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setDateRange, resetActivities } from "../../features/adminActivityLog/adminActivityLogSlice";
import { Calendar, ChevronDown } from "lucide-react";

const DateRangeFilter = () => {
    const dispatch = useDispatch();
    const { startDate, endDate } = useSelector((state) => state.adminActivityLog);
    const [showCustomRange, setShowCustomRange] = useState(false);
    const [localStartDate, setLocalStartDate] = useState('');
    const [localEndDate, setLocalEndDate] = useState('');

    // Các preset thời gian
    const getPresetRanges = () => {
        const now = new Date();
        
        // Hôm nay
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayEnd = new Date();
        todayEnd.setHours(23, 59, 59, 999);
        
        // Hôm qua
        const yesterday = new Date();
        yesterday.setDate(now.getDate() - 1);
        yesterday.setHours(0, 0, 0, 0);
        const yesterdayEnd = new Date();
        yesterdayEnd.setDate(now.getDate() - 1);
        yesterdayEnd.setHours(23, 59, 59, 999);
        
        // Tuần này (Thứ 2 đến Chủ nhật)
        const currentDay = now.getDay();
        const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay;
        const thisWeekStart = new Date(now);
        thisWeekStart.setDate(now.getDate() + mondayOffset);
        thisWeekStart.setHours(0, 0, 0, 0);
        const thisWeekEnd = new Date(thisWeekStart);
        thisWeekEnd.setDate(thisWeekStart.getDate() + 6);
        thisWeekEnd.setHours(23, 59, 59, 999);
        
        // Tuần trước
        const lastWeekStart = new Date(thisWeekStart);
        lastWeekStart.setDate(thisWeekStart.getDate() - 7);
        const lastWeekEnd = new Date(thisWeekEnd);
        lastWeekEnd.setDate(thisWeekEnd.getDate() - 7);
        
        // Tháng này
        const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        thisMonthStart.setHours(0, 0, 0, 0);
        const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        thisMonthEnd.setHours(23, 59, 59, 999);
        
        // Tháng trước
        const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        lastMonthStart.setHours(0, 0, 0, 0);
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
        lastMonthEnd.setHours(23, 59, 59, 999);
        
        return [
            { label: 'Hôm nay', startDate: today.toISOString(), endDate: todayEnd.toISOString() },
            { label: 'Hôm qua', startDate: yesterday.toISOString(), endDate: yesterdayEnd.toISOString() },
            { label: 'Tuần này', startDate: thisWeekStart.toISOString(), endDate: thisWeekEnd.toISOString() },
            { label: 'Tuần trước', startDate: lastWeekStart.toISOString(), endDate: lastWeekEnd.toISOString() },
            { label: 'Tháng này', startDate: thisMonthStart.toISOString(), endDate: thisMonthEnd.toISOString() },
            { label: 'Tháng trước', startDate: lastMonthStart.toISOString(), endDate: lastMonthEnd.toISOString() }
        ];
    };

    const presetRanges = getPresetRanges();

    // Format date để hiển thị
    const formatDisplayDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    };

    // Format date cho input
    const formatInputDate = (dateString) => {
        const date = new Date(dateString);
        return date.toISOString().split('T')[0];
    };

    // Khởi tạo local dates
    useEffect(() => {
        if (startDate && endDate) {
            // console.log('startDate', startDate, 'endDate', endDate);
            setLocalStartDate(formatInputDate(startDate));
            setLocalEndDate(formatInputDate(endDate));
        }
    }, [startDate, endDate]);

    // Tìm label của preset hiện tại
    const getCurrentPresetLabel = () => {
        const currentPreset = presetRanges.find(preset => 
            new Date(preset.startDate).toDateString() === new Date(startDate).toDateString() &&
            new Date(preset.endDate).toDateString() === new Date(endDate).toDateString()
        );
        return currentPreset ? currentPreset.label : 'Tùy chỉnh';
    };

    const handlePresetSelect = (preset) => {
        dispatch(resetActivities());
        dispatch(setDateRange({
            startDate: preset.startDate,
            endDate: preset.endDate
        }));
        setShowCustomRange(false);
    };

    const handleCustomDateApply = () => {
        if (localStartDate && localEndDate) {
            const start = new Date(localStartDate);
            start.setHours(0, 0, 0, 0);
            const end = new Date(localEndDate);
            end.setHours(23, 59, 59, 999);
            
            dispatch(resetActivities());
            dispatch(setDateRange({
                startDate: start.toISOString(),
                endDate: end.toISOString()
            }));
            setShowCustomRange(false);
        }
    };

    return (
        <div className="relative">
            <div className="flex items-center gap-2">
                <Calendar size={16} className="text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Thời gian:</span>
                
                {/* Dropdown trigger */}
                <div className="relative">
                    <button
                        onClick={() => setShowCustomRange(!showCustomRange)}
                        className="flex items-center gap-2 px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors text-sm"
                    >
                        <span>{getCurrentPresetLabel()}</span>
                        <span className="text-gray-500 text-xs">
                            ({formatDisplayDate(startDate)} - {formatDisplayDate(endDate)})
                        </span>
                        <ChevronDown size={16} className={`transition-transform ${showCustomRange ? 'rotate-180' : ''}`} />
                    </button>

                    {/* Dropdown menu */}
                    {showCustomRange && (
                        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10 min-w-[300px]">
                            <div className="p-3">
                                {/* Preset options */}
                                <div className="space-y-1 mb-3">
                                    <h4 className="text-sm font-medium text-gray-700 mb-2">Thời gian có sẵn:</h4>
                                    {presetRanges.map((preset, index) => (
                                        <button
                                            key={index}
                                            onClick={() => handlePresetSelect(preset)}
                                            className="w-full text-left px-2 py-1 text-sm hover:bg-gray-100 rounded transition-colors"
                                        >
                                            {preset.label}
                                        </button>
                                    ))}
                                </div>

                                {/* Custom date range */}
                                <div className="border-t pt-3">
                                    <h4 className="text-sm font-medium text-gray-700 mb-2">Tùy chỉnh:</h4>
                                    <div className="space-y-2">
                                        <div>
                                            <label className="block text-xs text-gray-600 mb-1">Từ ngày:</label>
                                            <input
                                                type="date"
                                                value={localStartDate}
                                                onChange={(e) => setLocalStartDate(e.target.value)}
                                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-xs text-gray-600 mb-1">Đến ngày:</label>
                                            <input
                                                type="date"
                                                value={localEndDate}
                                                onChange={(e) => setLocalEndDate(e.target.value)}
                                                className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                                            />
                                        </div>
                                        <div className="flex gap-2">
                                            <button
                                                onClick={handleCustomDateApply}
                                                disabled={!localStartDate || !localEndDate}
                                                className="flex-1 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                            >
                                                Áp dụng
                                            </button>
                                            <button
                                                onClick={() => setShowCustomRange(false)}
                                                className="flex-1 px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 transition-colors"
                                            >
                                                Hủy
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Backdrop to close dropdown when clicking outside */}
            {showCustomRange && (
                <div 
                    className="fixed inset-0 z-0" 
                    onClick={() => setShowCustomRange(false)}
                />
            )}
        </div>
    );
};

export default DateRangeFilter;
