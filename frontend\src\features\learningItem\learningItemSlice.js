import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as LearningItemApi from "../../services/learningItemApi";
import { apiHandler } from "../../utils/apiHandler";


export const getUncompletedLearningItem = createAsyncThunk(
    "learningItems/getUncompletedLearningItem",
    async ({ page = 1, limit = 10 }, { dispatch }) => {
        return await apiHandler(dispatch, LearningItemApi.getUncompletedLearningItemAPI, { page, limit }, () => { }, false, false, false, false);
    }
);

export const getLearningItemWeekend = createAsyncThunk(
    "learningItems/getLearningItemWeekend",
    async ({ startOfWeek, endOfWeek }, { dispatch }) => {
        return await apiHandler(dispatch, LearningItemApi.getLearningItemWeekendAPI, { startOfWeek, endOfWeek }, () => { }, false, false, false, false);
    }
);

export const getLearningItemDay = createAsyncThunk(
    "learningItems/getLearningItemDay",
    async (day, { dispatch }) => {
        return await apiHandler(dispatch, LearningItemApi.getLearningItemDayAPI, day, () => { }, false, false, false, false);
    }
);

export const getLearningItemMonth = createAsyncThunk(
    "learningItems/getLearningItemMonth",
    async ({ firstDay, lastDay }, { dispatch }) => {
        return await apiHandler(dispatch, LearningItemApi.getLearningItemMonthAPI, { firstDay, lastDay }, () => { }, false, false, false, false);
    }
);

export const getLearningItemsByClassIdInfinite = createAsyncThunk(
    "learningItems/getLearningItemsByClassIdInfinite",
    async ({ classId, cursor = null, limit = 10, type = null, search = '', sortBy = 'createdAt', sortOrder = 'DESC' }, { dispatch }) => {
        return await apiHandler(
            dispatch, 
            LearningItemApi.getLearningItemsByClassIdInfiniteAPI, 
            { classId, cursor, limit, type, search, sortBy, sortOrder }, 
            () => { }, 
            false, 
            false, 
            false, 
            false
        );
    }
);

const initialState = {
    learningItems: [],
    learningItemsWeekend: [],
    learningItemsDay: [],
    learningItemsMonth: [],
    learningItemsByClass: [], // Cho infinite scroll
    loading: false,
    loadingInfinite: false,

    pagination: {
        page: 1,
        limit: 10,
        totalItems: 0,
        totalPages: 0
    },

    paginationInfinite: {
        hasMore: false,
        nextCursor: null,
        limit: 10,
        count: 0,
        isLoadingMore: false
    }
};const learningItemSlice = createSlice({
    name: "learningItems",
    initialState,
    reducers: {
        resetLearningItems: (state) => {
            state.learningItems = [];
            state.pagination = {
                page: 1,
                pageSize: 10,
                total: 0,
                totalPages: 0
            };
        },
        resetLearningItemsByClass: (state) => {
            state.learningItemsByClass = [];
            state.paginationInfinite = {
                hasMore: false,
                nextCursor: null,
                limit: 10,
                count: 0,
                isLoadingMore: false
            };
        },
        setCurrentPageLT: (state, action) => {
            state.pagination.page = action.payload;
        },
        setLoadingMore: (state, action) => {
            state.paginationInfinite.isLoadingMore = action.payload;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(getUncompletedLearningItem.pending, (state) => {
                state.loading = true;
                state.learningItems = [];
            })
            .addCase(getUncompletedLearningItem.fulfilled, (state, action) => {
                let { data } = action.payload;
                if (!action.payload || !action.payload.data) {
                    state.loading = false;
                    return;
                }

                state.loading = false;
                state.learningItems = data.data;
                state.pagination = data.pagination;
            })
            .addCase(getUncompletedLearningItem.rejected, (state) => {
                state.loading = false;
            })
            .addCase(getLearningItemWeekend.pending, (state) => {
                state.learningItemsWeekend = [];
            })
            .addCase(getLearningItemWeekend.fulfilled, (state, action) => {
                let { data } = action.payload;
                if (!action.payload || !action.payload.data) {
                    return;
                }

                state.learningItemsWeekend = data;
                // console.log("getLearningItemWeekend", state.learningItemsWeekend);
            })
            .addCase(getLearningItemWeekend.rejected, (state) => {
                state.learningItemsWeekend = [];
            })
            .addCase(getLearningItemDay.pending, (state) => {
                state.learningItemsDay = [];
            })
            .addCase(getLearningItemDay.fulfilled, (state, action) => {
                let { data } = action.payload;

                if (!data) {
                    return;
                }
                // console.log("getLearningItemDay", data);
                state.learningItemsDay = data;
                // console.log("getLearningItemDay", state.learningItems);
            })
            .addCase(getLearningItemDay.rejected, (state) => {
                state.learningItemsDay = [];
            })
            .addCase(getLearningItemMonth.pending, (state) => {
                state.learningItemsMonth = [];
            })
            .addCase(getLearningItemMonth.fulfilled, (state, action) => {
                let { data } = action.payload;

                if (!data) {
                    return;
                }
                // console.log("getLearningItemMonth", data);
                state.learningItemsMonth = data;
                // console.log("getLearningItemMonth", state.learningItems);
            })
            .addCase(getLearningItemMonth.rejected, (state) => {
                state.learningItemsMonth = [];
            })
            // Infinite scroll learning items by class
            .addCase(getLearningItemsByClassIdInfinite.pending, (state, action) => {
                // Nếu không có cursor (trang đầu), reset data
                if (!action.meta.arg.cursor) {
                    state.loadingInfinite = true;
                    state.learningItemsByClass = [];
                    state.paginationInfinite.isLoadingMore = false;
                } else {
                    // Load more (có cursor), chỉ set loading more
                    state.paginationInfinite.isLoadingMore = true;
                }
            })
            .addCase(getLearningItemsByClassIdInfinite.fulfilled, (state, action) => {
                const { data, pagination } = action.payload || {};
                if (!data) {
                    state.loadingInfinite = false;
                    state.paginationInfinite.isLoadingMore = false;
                    return;
                }

                // Nếu không có cursor (trang đầu), reset data
                if (!action.meta.arg.cursor) {
                    state.learningItemsByClass = data;
                    state.loadingInfinite = false;
                } else {
                    // Load more, append data
                    state.learningItemsByClass = [...state.learningItemsByClass, ...data];
                }

                // Update pagination info
                state.paginationInfinite = {
                    hasMore: pagination?.hasMore || false,
                    nextCursor: pagination?.nextCursor || null,
                    limit: pagination?.limit || 10,
                    count: pagination?.count || 0,
                    isLoadingMore: false
                };
            })
            .addCase(getLearningItemsByClassIdInfinite.rejected, (state) => {
                state.loadingInfinite = false;
                state.paginationInfinite.isLoadingMore = false;
            });
    }

});

export const { resetLearningItems, resetLearningItemsByClass, setCurrentPageLT, setLoadingMore } = learningItemSlice.actions;

export default learningItemSlice.reducer;
