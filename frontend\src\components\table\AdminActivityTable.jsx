import { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchAdminActivities, setSortOrder } from "../../features/adminActivityLog/adminActivityLogSlice";
import LoadingData from "../loading/LoadingData";
import useInfiniteScroll from "../../hooks/useInfiniteScroll";
import { Eye, ChevronUp, ChevronDown, RotateCcw, Loader2 } from "lucide-react";

const AdminActivityTable = () => {
    const dispatch = useDispatch();
    const { 
        activities, 
        loading, 
        loadingMore, 
        hasNextPage, 
        nextCursor,
        search, 
        limit, 
        sortOrder, 
        totalLoaded,
        startDate,
        endDate
    } = useSelector((state) => state.adminActivityLog);    const [selectedActivity, setSelectedActivity] = useState(null);

    const handleLoadMore = useCallback(() => {
        if (hasNextPage && nextCursor && !loadingMore) {
            dispatch(fetchAdminActivities({
                search,
                limit,
                cursor: nextCursor,
                sortOrder,
                startDate,
                endDate
            }));
        }
    }, [hasNextPage, nextCursor, loadingMore, dispatch, search, limit, sortOrder, startDate, endDate]);

    // Initialize infinite scroll
    const scrollRef = useInfiniteScroll(hasNextPage, loadingMore, handleLoadMore, 100);

    useEffect(() => {
        // Load initial data
        dispatch(fetchAdminActivities({
            search,
            limit,
            cursor: null,
            sortOrder,
            startDate,
            endDate
        }));    }, [dispatch, search, limit, sortOrder, startDate, endDate]);

    const handleRefresh = () => {
        dispatch(fetchAdminActivities({
            search,
            limit,
            cursor: null,
            sortOrder,
            startDate,
            endDate
        }));
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('vi-VN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    };

    const getActionColor = (action) => {
        const actionColors = {
            'CREATE': 'text-green-600 bg-green-50',
            'UPDATE': 'text-blue-600 bg-blue-50',
            'DELETE': 'text-red-600 bg-red-50',
            'LOGIN': 'text-purple-600 bg-purple-50',
            'LOGOUT': 'text-gray-600 bg-gray-50',
            'DEFAULT': 'text-gray-600 bg-gray-50'
        };
        return actionColors[action] || actionColors['DEFAULT'];
    };

    return (
        <LoadingData
            loading={loading}
            isNoData={activities.length === 0}
            loadText="Đang tải danh sách hoạt động admin"
            noDataText="Không có hoạt động admin nào."
        >
            <div className="flex flex-col gap-4 min-h-0 text-sm">
                {/* Header with total count and sort controls */}
                <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-4">                        <span className="text-gray-600">
                            Đã tải: <span className="font-semibold text-blue-600">{totalLoaded}</span> hoạt động
                        </span>
                        {hasNextPage && (
                            <span className="text-sm text-gray-500">
                                (Kéo xuống để tải thêm)
                            </span>
                        )}
                    </div>
                    <div className="flex items-center gap-2">
                        <button
                            onClick={() => dispatch(setSortOrder())}
                            className="flex items-center gap-1 px-3 py-1 bg-white border rounded-md hover:bg-gray-50 transition-colors"
                        >
                            <span className="text-sm">Thời gian</span>
                            {sortOrder === 'DESC' ? 
                                <ChevronDown size={16} /> : 
                                <ChevronUp size={16} />
                            }
                        </button>
                        <button
                            onClick={handleRefresh}
                            className="flex items-center gap-1 px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                            disabled={loading}
                        >
                            <RotateCcw size={16} />
                            <span className="text-sm">Làm mới</span>
                        </button>
                    </div>
                </div>                {/* Table */}
                <div ref={scrollRef} className="flex-grow h-[70vh] overflow-y-auto hide-scrollbar">
                    <table className="w-full border-collapse border border-[#E7E7ED]">
                        <thead className="bg-[#F6FAFD] ">
                            <tr className="border border-[#E7E7ED]">
                                <th className="p-3 text-center w-16">ID</th>
                                <th className="p-3 text-center w-32">Admin</th>
                                <th className="p-3 text-center w-24">Hành động</th>
                                <th className="p-3 text-center">Mô tả</th>
                                <th className="p-3 text-center w-40">Thời gian</th>
                                <th className="p-3 text-center w-20">Chi tiết</th>
                            </tr>
                        </thead>
                        <tbody>
                            {activities.map((activity, index) => (
                                <tr
                                    key={`${activity.id}-${index}`}
                                    className="border border-[#E7E7ED] hover:bg-gray-50 transition-colors"
                                >
                                    <td className="p-3 text-center font-mono text-xs">
                                        {activity.id}
                                    </td>
                                    <td className="p-3 text-center">
                                        <div className="flex flex-col items-center gap-1">
                                            {activity.admin?.avatarUrl && (
                                                <img
                                                    src={activity.admin.avatarUrl}
                                                    alt="Admin Avatar"
                                                    className="w-8 h-8 rounded-full object-cover"
                                                />
                                            )}
                                            <div className="text-xs">
                                                <div className="font-medium">
                                                    {activity.admin?.fullName || 'N/A'}
                                                </div>
                                                <div className="text-gray-500">
                                                    @{activity.admin?.username || 'N/A'}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="p-3 text-center">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getActionColor(activity.action)}`}>
                                            {activity.action}
                                        </span>
                                    </td>
                                    <td className="p-3">
                                        <div className="max-w-md overflow-hidden text-ellipsis">
                                            {activity.description || 'Không có mô tả'}
                                        </div>
                                    </td>
                                    <td className="p-3 text-center text-xs font-mono">
                                        {formatDate(activity.createdAt)}
                                    </td>
                                    <td className="p-3 text-center">
                                        <button
                                            onClick={() => setSelectedActivity(activity)}
                                            className="p-1 text-blue-500 hover:text-blue-700 hover:bg-blue-50 rounded transition-colors"
                                            title="Xem chi tiết"
                                        >
                                            <Eye size={16} />
                                        </button>
                                    </td>
                                </tr>
                            ))}                        </tbody>
                    </table>

                    {/* Loading More Indicator */}
                    {loadingMore && (
                        <div className="flex justify-center items-center p-4 border-t border-gray-200">
                            <Loader2 className="animate-spin text-blue-500 mr-2" size={20} />
                            <span className="text-gray-600">Đang tải thêm...</span>
                        </div>
                    )}

                    {/* End of data indicator */}
                    {!hasNextPage && activities.length > 0 && (
                        <div className="flex justify-center items-center p-4 border-t border-gray-200">
                            <span className="text-gray-500 text-sm">
                                Đã tải hết tất cả dữ liệu ({totalLoaded} hoạt động)
                            </span>
                        </div>
                    )}
                </div>
            </div>

            {/* Activity Detail Modal */}
            {selectedActivity && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
                        <div className="p-6">
                            <div className="flex justify-between items-center mb-4">
                                <h2 className="text-xl font-semibold">Chi tiết hoạt động</h2>
                                <button
                                    onClick={() => setSelectedActivity(null)}
                                    className="text-gray-500 hover:text-gray-700"
                                >
                                    ✕
                                </button>
                            </div>
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">ID:</label>
                                        <p className="text-sm font-mono">{selectedActivity.id}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Target ID:</label>
                                        <p className="text-sm font-mono">{selectedActivity.targetId || 'N/A'}</p>
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">Admin:</label>
                                    <div className="flex items-center gap-2 mt-1">
                                        {selectedActivity.admin?.avatarUrl && (
                                            <img
                                                src={selectedActivity.admin.avatarUrl}
                                                alt="Admin Avatar"
                                                className="w-10 h-10 rounded-full object-cover"
                                            />
                                        )}
                                        <div>
                                            <p className="font-medium">{selectedActivity.admin?.fullName}</p>
                                            <p className="text-sm text-gray-500">@{selectedActivity.admin?.username}</p>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">Hành động:</label>
                                    <span className={`inline-block mt-1 px-3 py-1 rounded-full text-sm font-medium ${getActionColor(selectedActivity.action)}`}>
                                        {selectedActivity.action}
                                    </span>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-gray-600">Mô tả:</label>
                                    <p className="mt-1 p-3 bg-gray-50 rounded-md text-sm">
                                        {selectedActivity.description || 'Không có mô tả'}
                                    </p>
                                </div>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Thời gian tạo:</label>
                                        <p className="text-sm font-mono">{formatDate(selectedActivity.createdAt)}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-600">Cập nhật lần cuối:</label>
                                        <p className="text-sm font-mono">{formatDate(selectedActivity.updatedAt)}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </LoadingData>
    );
};

export default AdminActivityTable;
