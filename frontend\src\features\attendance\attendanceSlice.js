import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { apiHandler } from "../../utils/apiHandler";
import * as AttendanceApi from "../../services/attendanceApi";

// ========== THUNKS ==========

// [GET] Lấy danh sách điểm danh theo lessonId (có tìm kiếm, phân trang)
export const fetchAttendancesByLessonId = createAsyncThunk(
    "attendances/fetchAttendancesByLessonId",
    async (params, { dispatch }) => {
        return await apiHandler(dispatch, AttendanceApi.getAttendanceListAPI, params, null, true, false);
    }
);

export const fetchAttendancesByLessonIdWithoutPagination = createAsyncThunk(
    "attendances/fetchAttendancesByLessonIdWithoutPagination",
    async ({ lessonId, status, tuition, btvnList, month = null }, { dispatch }) => {
        return await apiHandler(dispatch, AttendanceApi.getAttendanceListAPI, { lessonId, status, tuition, btvnList, month, page: 1, limit: 1000 }, () => { }, false, false, false, false
        );
    }
);

// [POST] Tạo bản ghi điểm danh đơn
export const postAttendance = createAsyncThunk(
    "attendances/postAttendance",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, AttendanceApi.postAttendanceAPI, data, null, true, false);
    }
);

// [POST] Tạo toàn bộ điểm danh cho một lesson
export const postAllAttendanceInLesson = createAsyncThunk(
    "attendances/postAllAttendanceInLesson",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, AttendanceApi.postBulkAttendanceAPI, data, null, true, false);
    }
);

// [PUT] Cập nhật điểm danh
export const updateAttendance = createAsyncThunk(
    "attendances/updateAttendance",
    async ({ attendanceId, data }, { dispatch }) => {
        return await apiHandler(dispatch, AttendanceApi.putAttendanceAPI, { attendanceId, data }, null, true, false);
    }
);

// [DELETE] Xóa điểm danh
export const deleteAttendance = createAsyncThunk(
    "attendances/deleteAttendance",
    async (attendanceId, { dispatch }) => {
        return await apiHandler(dispatch, AttendanceApi.deleteAttendanceAPI, attendanceId, null, true, false);
    }
);

// [GET] Lấy điểm danh của user theo tháng
export const fetchUserAttendances = createAsyncThunk(
    "attendances/fetchUserAttendances",
    async ({ params = {} }, { rejectWithValue }) => {
        try {
            const response = await AttendanceApi.getUserAttendancesAPI(params);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || 'Có lỗi xảy ra khi tải dữ liệu điểm danh');
        }
    }
);

export const fetchUserAttendancesAdmin = createAsyncThunk(
    "attendances/fetchUserAttendances",
    async ({ userId, params = {} }, { rejectWithValue }) => {
        try {
            const response = await AttendanceApi.getUserAttendancesAPIAdmin(userId, params);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || 'Có lỗi xảy ra khi tải dữ liệu điểm danh');
        }
    }
);

// ========== STATISTICS THUNKS ==========

// [GET] Thống kê điểm danh cho một buổi học cụ thể
export const fetchLessonAttendanceStatistics = createAsyncThunk(
    "attendances/fetchLessonAttendanceStatistics",
    async (lessonId, { rejectWithValue }) => {
        try {
            const response = await AttendanceApi.getLessonAttendanceStatisticsAPI(lessonId);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || 'Có lỗi xảy ra khi tải thống kê buổi học');
        }
    }
);

// [GET] Thống kê điểm danh cho một lớp học
export const fetchClassAttendanceStatistics = createAsyncThunk(
    "attendances/fetchClassAttendanceStatistics",
    async ({ classId, params = {} }, { rejectWithValue }) => {
        try {
            const response = await AttendanceApi.getClassAttendanceStatisticsAPI(classId, params);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || 'Có lỗi xảy ra khi tải thống kê lớp học');
        }
    }
);

// [GET] Thống kê điểm danh tổng quan
export const fetchOverallAttendanceStatistics = createAsyncThunk(
    "attendances/fetchOverallAttendanceStatistics",
    async (params = {}, { rejectWithValue }) => {
        try {
            const response = await AttendanceApi.getOverallAttendanceStatisticsAPI(params);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || 'Có lỗi xảy ra khi tải thống kê tổng quan');
        }
    }
);

// ========== STATE & SLICE ==========

const initialState = {
    list: [],              // danh sách điểm danh
    pageSize: 30,
    page: 1,
    total: 0,
    totalPages: 0,
    loading: false,
    error: null,
    monthTuition: null, // tháng học phí, dùng để hiển thị trong bảng điểm danh
    userNotJoined: null,
    // btvnInfo: null,        // thông tin BTVN
    // User attendances data
    loadingUpdate: false,
    loadingExcelData: false,
    userAttendances: {
        userId: null,
        totalAttendances: 0,
        monthlyData: []
    },
    userAttendancesLoading: false,
    userAttendancesError: null,
    selectedYear: new Date().getFullYear(),
    selectedMonth: null, // null = all months

    // Statistics data
    lessonStatistics: {
        data: null,
        loading: false,
        error: null
    },
    classStatistics: {
        data: null,
        loading: false,
        error: null,
        filters: {
            startDate: null,
            endDate: null
        }
    },
    overallStatistics: {
        data: null,
        loading: false,
        error: null,
        filters: {
            startDate: null,
            endDate: null,
            classId: null
        }
    }
};

const attendanceSlice = createSlice({
    name: 'attendances',
    initialState,
    reducers: {
        resetAttendanceState: () => initialState,

        setMonthTuition: (state, action) => {
            state.monthTuition = action.payload;
        },

        setPage: (state, action) => {
            state.page = action.payload;
        },

        setLimit: (state, action) => {
            state.pageSize = action.payload;
        },

        // User attendances reducers
        setSelectedYear: (state, action) => {
            state.selectedYear = action.payload;
        },

        setSelectedMonth: (state, action) => {
            state.selectedMonth = action.payload;
        },

        clearUserAttendancesFilters: (state) => {
            state.selectedYear = new Date().getFullYear();
            state.selectedMonth = null;
        },

        clearUserAttendancesError: (state) => {
            state.userAttendancesError = null;
        },

        resetUserAttendances: (state) => {
            state.userAttendances = {
                userId: null,
                totalAttendances: 0,
                monthlyData: []
            };
            state.userAttendancesError = null;
        },

        // Statistics reducers
        clearLessonStatistics: (state) => {
            state.lessonStatistics = {
                data: null,
                loading: false,
                error: null
            };
        },

        clearClassStatistics: (state) => {
            state.classStatistics = {
                data: null,
                loading: false,
                error: null,
                filters: {
                    startDate: null,
                    endDate: null
                }
            };
        },

        setClassStatisticsFilters: (state, action) => {
            state.classStatistics.filters = {
                ...state.classStatistics.filters,
                ...action.payload
            };
        },

        clearOverallStatistics: (state) => {
            state.overallStatistics = {
                data: null,
                loading: false,
                error: null,
                filters: {
                    startDate: null,
                    endDate: null,
                    classId: null
                }
            };
        },

        setOverallStatisticsFilters: (state, action) => {
            state.overallStatistics.filters = {
                ...state.overallStatistics.filters,
                ...action.payload
            };
        },

        clearStudentNotJoined: (state) => {
            state.userNotJoined = null;
        },

        clearAllStatistics: (state) => {
            state.lessonStatistics = {
                data: null,
                loading: false,
                error: null
            };
            state.classStatistics = {
                data: null,
                loading: false,
                error: null,
                filters: {
                    startDate: null,
                    endDate: null
                }
            };
            state.overallStatistics = {
                data: null,
                loading: false,
                error: null,
                filters: {
                    startDate: null,
                    endDate: null,
                    classId: null
                }
            };
        }
    },
    extraReducers: (builder) => {
        builder
            // FETCH
            .addCase(fetchAttendancesByLessonId.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchAttendancesByLessonId.fulfilled, (state, action) => {
                state.loading = false;
                state.list = action.payload.data;
                state.page = action.payload.pagination.page;
                state.pageSize = action.payload.pagination.pageSize;
                state.total = action.payload.pagination.total;
                state.totalPages = action.payload.pagination.totalPages;
            })
            .addCase(fetchAttendancesByLessonId.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
            })

            // CREATE
            .addCase(postAttendance.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(postAttendance.fulfilled, (state, action) => {
                state.loading = false;
                // console.log('postAttendance', action.payload);
                // console.log('postAttendance action.payload.data', action.payload.data, action.payload.data.classStatus);
                if (action.payload && action.payload.data.classStatus !== 'JS') {
                    state.userNotJoined = action.payload.data.userId;
                }
            })
            .addCase(postAttendance.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
            })

            // CREATE ALL
            .addCase(postAllAttendanceInLesson.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(postAllAttendanceInLesson.fulfilled, (state, action) => {
                state.loading = false;
                state.list.push(...action.payload);
            })
            .addCase(postAllAttendanceInLesson.rejected, (state, action) => {
                state.loading = false;
                state.error = action.error.message;
            })

            // UPDATE
            .addCase(updateAttendance.pending, (state) => {
                state.loadingUpdate = true;
                state.error = null;
            })
            .addCase(updateAttendance.fulfilled, (state, action) => {
                state.loadingUpdate = false;
                const index = state.list.findIndex(item => item.id == action.payload.data.id);
                if (index !== -1) {
                    state.list[index] = {
                        ...state.list[index],
                        note: action.payload.data.note,
                        status: action.payload.data.status,
                        assignmentFeedback: action.payload.data.assignmentFeedback,
                        hasMessagedParent: action.payload.data.hasMessagedParent,
                    }
                }
            })
            .addCase(updateAttendance.rejected, (state, action) => {
                state.loadingUpdate = false;
                state.error = action.error.message;
            })

            // DELETE
            .addCase(deleteAttendance.fulfilled, (state, action) => {
                // console.log('deleteAttendance', action.payload);
                state.list = state.list.filter(item => item.id != action.payload.id);
            })

            // FETCH USER ATTENDANCES
            .addCase(fetchUserAttendances.pending, (state) => {
                state.userAttendancesLoading = true;
                state.userAttendancesError = null;
            })
            .addCase(fetchUserAttendances.fulfilled, (state, action) => {
                state.userAttendancesLoading = false;
                state.userAttendances = action.payload.data;
                state.userAttendancesError = null;
            })
            .addCase(fetchUserAttendances.rejected, (state, action) => {
                state.userAttendancesLoading = false;
                state.userAttendancesError = action.payload || action.error.message;
            })

            // LESSON STATISTICS
            .addCase(fetchLessonAttendanceStatistics.pending, (state) => {
                state.lessonStatistics.loading = true;
                state.lessonStatistics.error = null;
            })
            .addCase(fetchLessonAttendanceStatistics.fulfilled, (state, action) => {
                state.lessonStatistics.loading = false;
                state.lessonStatistics.data = action.payload.data;
                state.lessonStatistics.error = null;
            })
            .addCase(fetchLessonAttendanceStatistics.rejected, (state, action) => {
                state.lessonStatistics.loading = false;
                state.lessonStatistics.error = action.payload || action.error.message;
            })

            // CLASS STATISTICS
            .addCase(fetchClassAttendanceStatistics.pending, (state) => {
                state.classStatistics.loading = true;
                state.classStatistics.error = null;
            })
            .addCase(fetchClassAttendanceStatistics.fulfilled, (state, action) => {
                state.classStatistics.loading = false;
                state.classStatistics.data = action.payload.data;
                state.classStatistics.error = null;
            })
            .addCase(fetchClassAttendanceStatistics.rejected, (state, action) => {
                state.classStatistics.loading = false;
                state.classStatistics.error = action.payload || action.error.message;
            })

            // OVERALL STATISTICS
            .addCase(fetchOverallAttendanceStatistics.pending, (state) => {
                state.overallStatistics.loading = true;
                state.overallStatistics.error = null;
            })
            .addCase(fetchOverallAttendanceStatistics.fulfilled, (state, action) => {
                state.overallStatistics.loading = false;
                state.overallStatistics.data = action.payload.data;
                state.overallStatistics.error = null;
            })
            .addCase(fetchOverallAttendanceStatistics.rejected, (state, action) => {
                state.overallStatistics.loading = false;
                state.overallStatistics.error = action.payload || action.error.message;
            })
            // FETCH ATTENDANCES BY LESSON ID WITHOUT PAGINATION
            .addCase(fetchAttendancesByLessonIdWithoutPagination.pending, (state) => {
                state.loadingExcelData = true;
            })
            .addCase(fetchAttendancesByLessonIdWithoutPagination.fulfilled, (state, action) => {
                state.loadingExcelData = false;
            })
            .addCase(fetchAttendancesByLessonIdWithoutPagination.rejected, (state, action) => {
                state.loadingExcelData = false;
            })
    }
});

export const {
    resetAttendanceState,
    setPage,
    setLimit,
    setSelectedYear,
    setSelectedMonth,
    clearUserAttendancesFilters,
    clearUserAttendancesError,
    resetUserAttendances,
    // Statistics actions
    setMonthTuition,
    clearLessonStatistics,
    clearClassStatistics,
    setClassStatisticsFilters,
    clearOverallStatistics,
    setOverallStatisticsFilters,
    clearAllStatistics,
    clearStudentNotJoined
} = attendanceSlice.actions;

export default attendanceSlice.reducer;
