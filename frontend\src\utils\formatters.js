/**
 * Format a date string to a localized format
 * @param {string} dateString - The date string to format
 * @returns {string} - The formatted date string
 */
export const formatDate = (dateString) => {
  if (!dateString) return '';

  return new Date(dateString).toLocaleDateString("vi-VN", {
    year: "numeric",
    month: "long",
    day: "numeric"
  });
};
export const formatTimeDate = (dateString) => {
  if (!dateString) return '';

  return new Date(dateString).toLocaleDateString("vi-VN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
};

/**
 * Format a number as currency (VND)
 * @param {number} amount - The amount to format
 * @returns {string} - Formatted currency string
 */
export const formatCurrency = (amount) => {
  if (amount === null || amount === undefined) return "0 ₫";

  return new Intl.NumberFormat("vi-VN", {
    style: "currency",
    currency: "VND",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

/**
 * Format a number as currency without the currency symbol
 * @param {number} amount - The amount to format
 * @returns {string} - Formatted number string with dots as thousand separators
 */
export const formatNumberWithDots = (amount) => {
  if (amount === null || amount === undefined) return "";

  return new Intl.NumberFormat("vi-VN", {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

/**
 * Parse a formatted currency string (with dots) to a number
 * @param {string} formattedAmount - The formatted amount string (e.g., "123.000.000")
 * @returns {number} - The parsed number
 */
export const parseCurrencyInput = (formattedAmount) => {
  if (!formattedAmount) return 0;

  // Remove all dots and convert to number
  const cleanedValue = formattedAmount.toString().replace(/\./g, "");
  return parseInt(cleanedValue, 10) || 0;
};

/**
 * Format a date and time to a localized string
 * @param {string|Date} date - The date to format
 * @param {string} locale - The locale to use (default: 'vi-VN')
 * @returns {string} - Formatted date and time string
 */
export const formatDateTime = (date, locale = "vi-VN") => {
  if (!date) return "";

  return new Date(date).toLocaleString(locale);
};

/**
 * Remove diacritics (accents) from Vietnamese text
 * @param {string} str - The string to process
 * @returns {string} - String without diacritics
 */
export const removeDiacritics = (str) => {
  if (!str) return "";

  return str
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[đĐ]/g, d => d === 'đ' ? 'd' : 'D');
};

/**
 * Generate a username from a person's name and phone number
 * @param {string} firstName - The person's first name
 * @param {string} phone - The person's phone number
 * @returns {string} - Generated username
 */
export const generateUsername = (firstName, phone) => {
  if (!firstName || !phone) return "";

  // Remove diacritics, convert to lowercase, and remove spaces
  const processedFirstName = removeDiacritics(firstName)
    .toLowerCase()
    .replace(/\s+/g, '');

  return processedFirstName + phone;
};

export const formatTime = (seconds) => {
  const min = String(Math.floor(seconds / 60)).padStart(2, '0');
  const sec = String(seconds % 60).padStart(2, '0');
  return `${min}:${sec}`;
};

export function calculateDurationText(start, end) {
  if (!start || !end) return 'Không xác định';

  const startTime = new Date(start);
  const endTime = new Date(end);

  const diffInSeconds = Math.floor((endTime - startTime) / 1000);
  if (isNaN(diffInSeconds) || diffInSeconds < 0) return 'Không hợp lệ';

  const minutes = Math.floor(diffInSeconds / 60);
  const seconds = diffInSeconds % 60;

  return `${minutes} phút ${seconds} giây`;
}