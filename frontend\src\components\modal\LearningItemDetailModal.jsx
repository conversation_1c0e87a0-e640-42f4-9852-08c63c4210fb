import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Clock, Calendar, CheckCircle, AlertCircle, BookOpen, ExternalLink, X, FileText, Play, PenTool } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const LearningItemDetailModal = ({ isOpen, onClose, learningItemData }) => {
    const navigate = useNavigate();

    if (!learningItemData) return null;

    const handleNavigateToItem = () => {
        if (learningItemData.learningItem.lesson.class && learningItemData.learningItem) {
            navigate(`/class/${learningItemData.learningItem.lesson.class.class_code}/learning/lesson/${learningItemData.learningItem.lessonId}/learning-item/${learningItemData.learningItem.id}`);
            onClose();
        }
    };

    const getItemIcon = (type) => {
        switch (type) {
            case 'VID': return <Play size={16} className="text-red-500" />;
            case 'DOC': return <FileText size={16} className="text-blue-500" />;
            case 'BTVN': return <PenTool size={16} className="text-green-500" />;
            default: return <BookOpen size={16} className="text-gray-500" />;
        }
    };

    const getStatusInfo = () => {
        const now = new Date();
        const deadline = new Date(learningItemData.learningItem.deadline);
        deadline.setHours(23, 59, 59, 999);

        const isOverdue = !learningItemData.isDone && deadline < now;

        if (learningItemData.isDone) {
            return {
                status: 'completed',
                text: 'Đã hoàn thành',
                textColor: 'text-green-600',
                bgColor: 'bg-green-50',
                borderColor: 'border-green-300',
                icon: <CheckCircle size={16} className="text-green-600" />
            };
        } else if (isOverdue) {
            return {
                status: 'overdue',
                text: 'Quá hạn',
                textColor: 'text-red-600',
                bgColor: 'bg-red-50',
                borderColor: 'border-red-300',
                icon: <AlertCircle size={16} className="text-red-600" />
            };
        } else {
            return {
                status: 'pending',
                text: 'Chưa hoàn thành',
                textColor: 'text-yellow-600',
                bgColor: 'bg-yellow-50',
                borderColor: 'border-yellow-300',
                icon: <Clock size={16} className="text-yellow-600" />
            };
        }
    };

    const statusInfo = getStatusInfo();

    const formatDate = (dateStr) => {
        const date = new Date(dateStr);
        return date.toLocaleDateString('vi-VN', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatTime = (dateStr) => {
        const date = new Date(dateStr);
        return date.toLocaleTimeString('vi-VN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getTypeText = (type) => {
        switch (type) {
            case 'VID': return 'Video bài giảng';
            case 'DOC': return 'Tài liệu';
            case 'BTVN': return 'Bài tập về nhà';
            default: return 'Học liệu';
        }
    };

    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 z-[60] bg-black bg-opacity-40 flex items-start lg:items-center justify-center overflow-y-auto pt-8 pb-8 px-2 sm:px-4"
                    onClick={onClose}
                >
                    <motion.div
                        initial={{ scale: 0.95 }}
                        animate={{ scale: 1 }}
                        exit={{ scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                        onClick={(e) => e.stopPropagation()}
                        className="bg-white w-full max-w-lg p-6 rounded-md shadow-lg border border-gray-200"
                    >
                        {/* Header */}
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-gray-100 rounded-md flex items-center justify-center">
                                    {getItemIcon(learningItemData.learningItem.typeOfLearningItem)}
                                </div>
                                <div>
                                    <h2 className="text-lg font-semibold text-gray-900">
                                        {learningItemData.learningItem.name}
                                    </h2>
                                    <p className="text-sm text-gray-500">
                                        {getTypeText(learningItemData.learningItem.typeOfLearningItem)}
                                    </p>
                                </div>
                            </div>
                            <button
                                onClick={onClose}
                                className="p-1.5 rounded-md hover:bg-gray-100 transition-colors"
                            >
                                <X size={18} className="text-gray-500" />
                            </button>
                        </div>

                        <hr className="mb-4 border-gray-200" />

                        {/* Content */}
                        <div className="space-y-4">
                            {/* Trạng thái */}
                            <div className={`flex items-center gap-3 p-3 ${statusInfo.bgColor} rounded-md border ${statusInfo.borderColor}`}>
                                {statusInfo.icon}
                                <div>
                                    <p className={`text-sm font-medium ${statusInfo.textColor}`}>
                                        {statusInfo.text}
                                    </p>
                                    {learningItemData.isDone && learningItemData.studyTime && (
                                        <p className="text-xs text-gray-500">
                                            Hoàn thành lúc: {formatTime(learningItemData.studyTime)} - {formatDate(learningItemData.studyTime)}
                                        </p>
                                    )}
                                </div>
                            </div>

                            {/* Thời hạn */}
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-md">
                                <Calendar size={16} className="text-gray-600" />
                                <div>
                                    <p className="text-sm font-medium text-gray-900">Hạn nộp</p>
                                    <p className="text-xs text-gray-500">
                                        {formatDate(learningItemData.learningItem.deadline)}
                                    </p>
                                </div>
                            </div>

                            {/* Thông tin chi tiết */}
                            {learningItemData.learningItem.description && (
                                <div>
                                    <p className="text-xs text-gray-500 mb-1">Mô tả</p>
                                    <p className="text-sm text-gray-700">
                                        {learningItemData.learningItem.description}
                                    </p>
                                </div>
                            )}

                            {/* Lớp học liên quan */}
                            {learningItemData.learningItem.lesson.class && (
                                <div>
                                    <p className="text-xs text-gray-500 mb-1">Lớp học</p>
                                    <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-cyan-500 rounded-full"></div>
                                        <p className="text-sm font-medium text-gray-900">{learningItemData.learningItem.lesson.class.name}</p>
                                        <span className="text-xs text-gray-500">({learningItemData.learningItem.lesson.class.class_code})</span>
                                    </div>
                                </div>
                            )}
                        </div>

                        <hr className="my-4 border-gray-200" />

                        {/* Actions */}
                        <div className="flex justify-end gap-3">
                            <button
                                onClick={onClose}
                                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-md transition-colors duration-200"
                            >
                                Đóng
                            </button>

                            {!learningItemData.isDone ? (
                                <button
                                    onClick={handleNavigateToItem}
                                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors duration-200 flex items-center gap-2"
                                >
                                    <CheckCircle size={16} />
                                    Bắt đầu học
                                </button>
                            ) : (
                                <button
                                    onClick={handleNavigateToItem}
                                    className="px-4 py-2 bg-cyan-600 hover:bg-cyan-700 text-white text-sm font-medium rounded-md transition-colors duration-200 flex items-center gap-2"
                                >
                                    <ExternalLink size={16} />
                                    Xem chi tiết
                                </button>
                            )}
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};

export default LearningItemDetailModal;
