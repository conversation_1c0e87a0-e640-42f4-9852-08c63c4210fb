import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setSearch, resetActivities } from "../../features/adminActivityLog/adminActivityLogSlice";
import { Search, X, Calendar, Activity } from "lucide-react";

const AdminActivitySearch = () => {
    const dispatch = useDispatch();
    const { search, totalLoaded, startDate, endDate } = useSelector((state) => state.adminActivityLog);
    const [localSearch, setLocalSearch] = useState(search);

    // Debounce search
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (localSearch !== search) {
                dispatch(resetActivities());
                dispatch(setSearch(localSearch));
            }
        }, 500);

        return () => clearTimeout(timeoutId);
    }, [localSearch, search, dispatch]);

    const handleClearSearch = () => {
        setLocalSearch('');
        dispatch(resetActivities());
        dispatch(setSearch(''));
    };

    // Format date range display
    const formatDateRange = () => {
        if (!startDate && !endDate) return 'Tất cả thời gian';
        
        const formatDate = (dateStr) => {
            return new Date(dateStr).toLocaleDateString('vi-VN');
        };
        
        if (startDate && endDate) {
            return `${formatDate(startDate)} - ${formatDate(endDate)}`;
        } else if (startDate) {
            return `Từ ${formatDate(startDate)}`;
        } else {
            return `Đến ${formatDate(endDate)}`;
        }
    };

    return (
        <div className="space-y-4">
            {/* Search Input */}
            <div className="relative">
                <div className="relative">
                    <input
                        type="text"
                        placeholder="Tìm kiếm theo hành động, mô tả, tên admin..."
                        value={localSearch}
                        onChange={(e) => setLocalSearch(e.target.value)}
                        className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    {localSearch && (
                        <button
                            onClick={handleClearSearch}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                            <X size={20} />
                        </button>
                    )}
                </div>
                {localSearch && (
                    <div className="mt-2 text-sm text-gray-600">
                        Đang tìm kiếm: "<span className="font-medium">{localSearch}</span>"
                    </div>
                )}
            </div>

            {/* Stats */}
            <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                    <Activity className="w-4 h-4" />
                    <span>Đã tải: <strong>{totalLoaded || 0}</strong> hoạt động</span>
                </div>
                <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDateRange()}</span>
                </div>
            </div>
        </div>
    );
};

export default AdminActivitySearch;
