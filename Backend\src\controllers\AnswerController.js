import db from "../models/index.js"
// AnswerController.js
import { reExaminationManyService } from "../services/exam.service.js";
import { calculateScoreQuestion } from "../services/exam.service.js"
import { updateAverageScore } from "../services/user.service.js";
import { reExaminationService } from "../services/answer.service.js";

export const reExamination = async (req, res) => {
    const { attemptId } = req.params;

    if (!attemptId) {
        return res.status(400).json({ message: "attemptId không hợp lệ!" });
    }

    try {
        const result = await reExaminationService(attemptId);
        
        return res.status(200).json({
            message: "Chấm lại thành công!",
            updated: result.countUpdated,
            total: result.totalAnswers,
            score: result.newScore
        });

    } catch (error) {
        console.error("❌ Lỗi khi chấm lại:", error);
        return res.status(500).json({ 
            message: error.message || "Đ<PERSON> xảy ra lỗi khi chấm lại." 
        });
    }
};




export const getAnswerByAttempt = async (req, res) => {
    const { attemptId } = req.params;

    if (!attemptId) {
        return res.status(400).json({ message: "attemptId không hợp lệ!" });
    }

    const answers = await db.Answer.findAll({
        where: { attemptId },
        attributes: ["id", "questionId", "answerContent"],
        include: [
            {
                model: db.Question,
                attributes: ["typeOfQuestion"],
            }
        ]
    });

    const formatted = answers.map(answer => ({
        questionId: answer.questionId,
        answerContent: answer.answerContent,
        typeOfQuestion: answer.Question?.typeOfQuestion || null
    }));

    return res.status(200).json({
        message: "Lấy danh sách đáp án thành công!",
        data: formatted
    });
};

export const getQuestionsAndAnswersByAttempt = async (req, res) => {
    const { attemptId } = req.params;

    if (!attemptId) {
        return res.status(400).json({ message: "attemptId không hợp lệ!" });
    }

    // Dùng include để lấy cả exam => giảm 1 lần query
    const attempt = await db.StudentExamAttempt.findByPk(attemptId, {
        include: [
            {
                model: db.Exam,
                as: "exam", // phải đúng với alias trong associate
            }
        ]
    });

    if (!attempt) {
        return res.status(404).json({ message: "Không tìm thấy lượt làm bài!" });
    }

    const answers = await db.Answer.findAll({
        where: { attemptId },
        attributes: ["id", "questionId", "answerContent", "result"],
        include: [
            {
                model: db.Question,
                attributes: { exclude: ["createdAt", "updatedAt"] },
                include: [
                    {
                        model: db.Statement,
                        as: "statements",
                        attributes: ["id", "content", "imageUrl", "isCorrect"],
                    },
                ],
            }
        ]
    });

    const start = new Date(attempt.startTime);
    const end = new Date(attempt.endTime);
    const durationMs = Math.min(end - start, attempt.exam.testDuration * 60 * 1000);

    const questions = [];
    const questionMap = new Set(); // Tránh bị trùng câu hỏi nếu cùng questionId

    const formattedAnswers = answers.map(answer => {
        if (answer.Question && !questionMap.has(answer.Question.id)) {
            questions.push(answer.Question);
            questionMap.add(answer.Question.id);
        }

        return {
            id: answer.id,
            typeOfQuestion: answer.Question?.typeOfQuestion,
            questionId: answer.questionId,
            answerContent: answer.answerContent,
            result: answer.result,
        };
    });

    return res.status(200).json({
        message: "Lấy danh sách câu hỏi và đáp án thành công!",
        data: {
            questions,
            answers: formattedAnswers,
            exam: {
                name: attempt.exam?.name || "Không rõ",
                id: attempt.exam?.id,
                solutionPdfUrl: attempt.exam?.solutionPdfUrl || null,
                seeCorrectAnswer: attempt.exam?.seeCorrectAnswer || false,
            },
            score: attempt.score,
            durationMs,
            duration: `${Math.floor(durationMs / 1000 / 60)} phút ${Math.floor((durationMs / 1000) % 60)} giây`,
            durationInSeconds: Math.floor(durationMs / 1000)
        }
    });
};

export const reExaminations = async (req, res) => {
    const { examId } = req.params;

    if (!examId) {
        return res.status(400).json({ message: "examId không hợp lệ!" });
    }

    try {
        const attempts = await db.StudentExamAttempt.findAll({
            where: { examId },
            attributes: ["id"],
        });

        if (attempts.length === 0) {
            return res.status(404).json({ message: "Không tìm thấy lượt làm bài nào!" });
        }

        const results = [];
        let successCount = 0;
        let errorCount = 0;

        // Chấm lại từng attempt
        for (const attempt of attempts) {
            try {
                const result = await reExaminationService(attempt.id);
                results.push({
                    attemptId: attempt.id,
                    success: true,
                    ...result
                });
                successCount++;
            } catch (error) {
                console.error(`❌ Lỗi khi chấm lại attemptId ${attempt.id}:`, error);
                results.push({
                    attemptId: attempt.id,
                    success: false,
                    error: error.message
                });
                errorCount++;
            }
        }

        return res.status(200).json({
            message: "Chấm lại hàng loạt hoàn thành!",
            summary: {
                total: attempts.length,
                success: successCount,
                error: errorCount
            },
            results
        });

    } catch (error) {
        console.error("❌ Lỗi khi chấm lại hàng loạt:", error);
        return res.status(500).json({ 
            message: error.message || "Đã xảy ra lỗi khi chấm lại hàng loạt." 
        });
    }
};

export const postAnswer = async (req, res) => {
}


export const putAnswer = async (req, res) => {
}


export const deleteAnswer = async (req, res) => {
}
