import db from "../models/index.js"
import ResponseDataPagination from "../dtos/responses/pagination/PaginationResponse.js"
const { Op, literal, QueryTypes } = db.Sequelize;
import * as attemptService from "../services/attempt.service.js"
import UserType from "../constants/UserType.js";


const getDuration = (attempt, testDuration = null) => {
    const start = new Date(attempt.startTime);
    const end = new Date(attempt.endTime);

    let durationMs = end - start
    if (testDuration) {
        const maxDurationMs = testDuration * 60 * 1000;
        durationMs = Math.min(durationMs, maxDurationMs);
    }

    return {
        durationMs,
        duration: `${Math.floor(durationMs / 1000 / 60)} phút ${Math.floor((durationMs / 1000) % 60)} giây`,
    };
};


export const getAttemptsByUser = async (req, res) => {
    const { id } = req.user;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = 10;

    const { data, total } = await attemptService.getAttemptsByUserId(id, { page, limit });

    // console.log("total", total);
    return res.status(200).json({
        message: "Lấy danh sách lượt làm bài thành công!",
        ...new ResponseDataPagination(data, {
            page,
            pageSize: limit,
            total,
            totalPages: Math.ceil(total / limit),
        })
    });
};

export const getAttemptsByUserId = async (req, res) => {
    const { userId } = req.params;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 20;
    const sortOrder = req.query.sortOrder || 'DESC';

    // console.log("userId", userId, "page", page, "limit", limit, "sortOrder", sortOrder);
    const { data, total } = await attemptService.getAttemptsByUserId(userId, { page, limit, sortOrder });

    return res.status(200).json({
        message: "Lấy danh sách lượt làm bài theo mã sinh viên thành công!",
        ... new ResponseDataPagination(data, {
            page,
            pageSize: limit,
            total,
            totalPages: Math.ceil(total / limit),
            sortOrder,
        })
    });
}


export const getAttemptById = async (req, res) => {
    const { id } = req.params
    const { id: userId } = req.user;
    const attempt = await db.StudentExamAttempt.findByPk(id)
    if (!attempt) {
        return res.status(404).json({ message: 'Không tìm thấy lượt làm bài.' })
    }
    if (attempt.studentId !== userId) {
        return res.status(403).json({ message: 'Bạn không có quyền xem lượt làm bài này.' })
    }
    return res.status(200).json({ message: 'Lấy chi tiết lượt làm bài thành công!', data: attempt })
}


export const getAttemptByExamId = async (req, res) => {
    const { examId } = req.params;
    const { id } = req.user;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;
    const { userType } = req.user;
    // Kiểm tra đề thi
    const exam = await db.Exam.findByPk(examId);
    if (!exam) {
        return res.status(404).json({ message: 'Không tìm thấy đề thi!' });
    }

    if (!exam.public && userType === UserType.STUDENT) {
        return res.status(403).json({ message: '🚫 Đề thi này hiện không được công khai!' });
    }

    if (!exam.seeCorrectAnswer && userType === UserType.STUDENT) {
        return res.status(403).json({ message: '🚫 Đề thi này hiện không cho phép xem đáp án!' });
    }

    // Lấy danh sách attempts theo điểm cao ↓ (sẽ lọc + sắp lại theo thời gian sau)
    const { rows, count } = await db.StudentExamAttempt.findAndCountAll({
        where: {
            examId,
            endTime: { [db.Sequelize.Op.ne]: null },
            score: { [db.Sequelize.Op.ne]: null },
        },
        include: [
            {
                model: db.User,
                as: 'student',
                attributes: ['firstName', 'lastName', 'avatarUrl', 'id', 'highSchool', 'class'],
            }
        ],
        order: [['score', 'DESC']],
        limit: 1000, // lấy nhiều hơn rồi tự phân trang sau
    });

    // Tính duration + sort theo duration tăng dần nếu score bằng nhau
    const attemptsWithDuration = rows
        .map(attempt => {
            const { durationMs, duration } = getDuration(attempt, exam.testDuration);

            return {
                ...attempt.toJSON(),
                durationMs,
                duration,
            };
        })
        .sort((a, b) => {
            // Sắp theo score giảm dần, nếu bằng thì so sánh thời gian làm bài tăng dần
            if (b.score !== a.score) return b.score - a.score;
            return a.durationMs - b.durationMs;
        });

    // Tìm rank của người dùng hiện tại
    let userRank = null;
    let userBestAttempt = null;

    // Tìm lượt làm bài tốt nhất của người dùng hiện tại
    const userAttempts = attemptsWithDuration.filter(attempt => attempt.studentId === id);
    const userAttemptCount = userAttempts.length;
    if (userAttempts.length > 0) {
        // Lấy lượt làm bài có điểm cao nhất của người dùng
        userBestAttempt = userAttempts[0]; // Đã được sắp xếp theo điểm cao nhất rồi

        // Tìm rank của người dùng trong danh sách
        userRank = attemptsWithDuration.findIndex(attempt =>
            attempt.id === userBestAttempt.id
        ) + 1; // +1 vì index bắt đầu từ 0
    }

    // Phân trang sau khi sort
    const paginated = attemptsWithDuration.slice(offset, offset + limit);

    return res.status(200).json({
        message: 'Lấy danh sách lượt làm bài theo mã đề thành công!',
        data: {
            attempts: paginated,
            currentPage: page,
            totalPages: Math.ceil(attemptsWithDuration.length / limit),
            totalItems: attemptsWithDuration.length,
            exam: {
                name: exam.name,
                attemptLimit: exam.attemptLimit,
                seeCorrectAnswer: exam.seeCorrectAnswer,
            },
            limit,
            userAttemptCount,
            userRank, // Thêm rank của người dùng vào response
            userBestAttempt, // Thêm thông tin lượt làm bài tốt nhất của người dùng

        },
    });
};

export const getAttemptsForAdminByExamId = async (req, res) => {
    const { examId } = req.params;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 20;
    const offset = (page - 1) * limit;
    const search = req.query.search?.toLowerCase() || "";

    const exam = await db.Exam.findByPk(examId);
    if (!exam) {
        return res.status(404).json({ message: "Không tìm thấy đề thi!" });
    }

    const whereClause = {
        examId,
    };

    const userWhereClause = search
        ? {
            [Op.or]: [
                db.Sequelize.where(
                    db.Sequelize.fn(
                        'LOWER',
                        db.Sequelize.fn('CONCAT', db.Sequelize.col('student.lastName'), ' ', db.Sequelize.col('student.firstName'))
                    ),
                    {
                        [Op.like]: `%${search}%`,
                    }
                ),
                db.Sequelize.where(
                    db.Sequelize.fn('LOWER', db.Sequelize.col('student.class')),
                    {
                        [Op.like]: `%${search}%`,
                    }
                ),
            ],
        }
        : {};

    const { rows, count } = await db.StudentExamAttempt.findAndCountAll({
        where: whereClause,
        include: [
            {
                model: db.User,
                as: "student",
                where: userWhereClause,
                attributes: ["firstName", "lastName", "avatarUrl", "id", "highSchool", "class"],
                required: true,
            },
        ],
        order: [["score", "DESC"]],
        offset,
        limit,
    });

    const attemptsWithDuration = rows.map((attempt) => {
        const start = new Date(attempt.startTime);
        const end = new Date(attempt.endTime);
        if (!end || new Date(end).getTime() === 0) {
            return {
                ...attempt.toJSON(),
                durationMs: null,
                duration: null,
                durationInSeconds: null,
            };
        }

        const durationMs = end - start;

        return {
            ...attempt.toJSON(),
            duration: `${Math.floor(durationMs / 1000 / 60)} phút ${Math.floor((durationMs / 1000) % 60)} giây`,
        };
    });

    return res.status(200).json({
        message: "Lấy danh sách lượt làm bài (admin) thành công!",
        data: {
            data: attemptsWithDuration,
            currentPage: page,
            totalPages: Math.ceil(count / limit),
            totalItems: count,
            exam: {
                name: exam.name,
            },
            limit,
        },
    });
};



export const getAttemptByStudentId = async (req, res) => {
    const studentId = req.user.id;
    const examId = req.params.examId;
    const { userType } = req.user;
    // 📌 Kiểm tra đề thi
    const exam = await db.Exam.findByPk(examId);
    if (!exam) {
        return res.status(404).json({ message: 'Không tìm thấy đề thi!' });
    }
    if (!exam.public && userType === UserType.STUDENT) {
        return res.status(403).json({ message: '🚫 Đề thi này hiện không được công khai!' });
    }
    if (exam.seeCorrectAnswer === false && userType === UserType.STUDENT) {
        return res.status(403).json({ message: '🚫 Đề thi này hiện không cho phép xem đáp án!' });
    }

    // 📌 Lấy danh sách lượt làm bài
    const attempts = await db.StudentExamAttempt.findAll({
        where: { studentId, examId },
        order: [['startTime', 'DESC']],
    });

    // 📌 Tính duration cho mỗi attempt
    const formattedAttempts = attempts.map(attempt => {
        const { durationMs, duration } = getDuration(attempt, exam.testDuration);

        return {
            ...attempt.toJSON(),
            durationMs,
            duration,
        };
    });

    return res.status(200).json({
        message: 'Lấy danh sách lượt làm bài theo mã sinh viên thành công!',
        data: formattedAttempts,
        exam: {
            name: exam.name,
            seeCorrectAnswer: exam.seeCorrectAnswer,
        },
    });
};


export const postAttempt = async (req, res) => {
    const studentId = req.user.id
    const { examId } = req.body
    const newAttempt = await db.StudentExamAttempt.create({
        studentId,
        examId,
        startTime: new Date(),
        endTime: null,
        score: null,
    })
    return res.status(201).json({ message: 'Thêm lượt làm bài thành công!', data: newAttempt })
}

export const putAttempt = async (req, res) => {
    return res.status(200).json({ message: '🔧 Chức năng đang phát triển!' })
}

export const deleteAttempt = async (req, res) => {
    const { id } = req.params
    const attempt = await db.StudentExamAttempt.findByPk(id)

    if (!attempt) {
        return res.status(404).json({ message: 'Không tìm thấy lượt làm bài để xóa.' })
    }

    const studyStatus = await db.StudentStudyStatus.findOne({
        where: {
            studentId: attempt.studentId,
            learningItemId: attempt.examId,
        },
    });

    if (studyStatus) {
        studyStatus.isDone = false;
        await studyStatus.save();
    }

    await attempt.destroy()

    return res.status(200).json({ message: 'Xóa lượt làm bài thành công!' })
}

// Thống kê lượt làm bài và điểm số của người dùng
export const getUserAttemptStatistics = async (req, res) => {
    try {
        const { id: userId } = req.user;
        const { 
            period = 'week', // 'week', 'month', 'year'
            type = 'count', // 'count' (số lượt làm), 'score' (điểm số)
            date = null // ngày cụ thể để làm mốc (format: YYYY-MM-DD)
        } = req.query;

        let baseDate = date ? new Date(date) : new Date();
        let startDate, endDate, groupFormat, labels;

        // Xác định khoảng thời gian và format group by
        switch (period) {
            case 'week':
                // Lấy thứ 2 đầu tuần (tuần hiện tại)
                const dayOfWeek = baseDate.getDay(); // 0 = Chủ nhật, 1 = Thứ 2, ..., 6 = Thứ 7
                const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Chủ nhật = 6 ngày từ thứ 2
                startDate = new Date(baseDate);
                startDate.setDate(baseDate.getDate() - daysFromMonday);
                startDate.setHours(0, 0, 0, 0);
                endDate = new Date(startDate);
                endDate.setDate(startDate.getDate() + 6);
                endDate.setHours(23, 59, 59, 999);
                groupFormat = "DATE_FORMAT(endTime, '%Y-%m-%d')";
                labels = ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'];
                break;

            case 'month':
                // Lấy các tuần trong tháng theo calendar thực tế
                startDate = new Date(baseDate.getFullYear(), baseDate.getMonth(), 1);
                endDate = new Date(baseDate.getFullYear(), baseDate.getMonth() + 1, 0);
                endDate.setHours(23, 59, 59, 999);
                
                // Sử dụng logic tính tuần trong tháng khác
                groupFormat = `CASE 
                    WHEN DAY(endTime) <= 7 THEN 'Tuần 1'
                    WHEN DAY(endTime) <= 14 THEN 'Tuần 2' 
                    WHEN DAY(endTime) <= 21 THEN 'Tuần 3'
                    WHEN DAY(endTime) <= 28 THEN 'Tuần 4'
                    ELSE 'Tuần 5'
                END`;
                
                // Tính số tuần thực tế trong tháng
                const lastDayOfMonth = endDate.getDate();
                const weeksCount = Math.ceil(lastDayOfMonth / 7);
                labels = [];
                for (let i = 1; i <= weeksCount; i++) {
                    labels.push(`Tuần ${i}`);
                }
                break;

            case 'year':
                // Lấy các tháng trong năm
                startDate = new Date(baseDate.getFullYear(), 0, 1);
                endDate = new Date(baseDate.getFullYear(), 11, 31);
                endDate.setHours(23, 59, 59, 999);
                groupFormat = "DATE_FORMAT(endTime, '%m')";
                labels = ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10', 'T11', 'T12'];
                break;

            default:
                return res.status(400).json({ 
                    message: 'Period không hợp lệ. Chỉ chấp nhận: week, month, year' 
                });
        }

        // Xây dựng query dựa trên type
        let selectClause, queryType;
        if (type === 'count') {
            selectClause = 'COUNT(*) as value';
            queryType = 'Số lượt làm bài';
        } else if (type === 'score') {
            selectClause = 'ROUND(AVG(CAST(score AS DECIMAL(5,2))), 2) as value';
            queryType = 'Điểm trung bình';
        } else {
            return res.status(400).json({ 
                message: 'Type không hợp lệ. Chỉ chấp nhận: count, score' 
            });
        }

        // Query thống kê
        const query = `
            SELECT 
                ${groupFormat} as period,
                ${selectClause}
            FROM studentExamAttempt 
            WHERE studentId = :userId 
                AND endTime IS NOT NULL
                AND endTime >= :startDate 
                AND endTime <= :endDate
            GROUP BY ${groupFormat}
            ORDER BY period
        `;

        const rawResults = await db.sequelize.query(query, {
            replacements: { 
                userId, 
                startDate: startDate.toISOString(), 
                endDate: endDate.toISOString() 
            },
            type: QueryTypes.SELECT
        });

        // Xử lý kết quả theo period
        let processedData = [];
        
        if (period === 'week') {
            // Tạo array 7 ngày - sử dụng local dates để tránh timezone issues
            for (let i = 0; i < 7; i++) {
                const currentDate = new Date(startDate);
                currentDate.setDate(startDate.getDate() + i);
                
                // Format date theo local timezone
                const year = currentDate.getFullYear();
                const month = String(currentDate.getMonth() + 1).padStart(2, '0');
                const day = String(currentDate.getDate()).padStart(2, '0');
                const dateStr = `${year}-${month}-${day}`;
                
                const found = rawResults.find(item => item.period === dateStr);
                processedData.push({
                    label: labels[i],
                    date: dateStr,
                    value: found ? Number(found.value) : 0
                });
            }
        } else if (period === 'month') {
            // Tạo array các tuần trong tháng với date range thực tế
            const lastDayOfMonth = endDate.getDate();
            const weeksCount = Math.ceil(lastDayOfMonth / 7);
            
            for (let i = 1; i <= weeksCount; i++) {
                const weekLabel = `Tuần ${i}`;
                const weekStart = (i - 1) * 7 + 1;
                const weekEnd = Math.min(i * 7, lastDayOfMonth);
                
                const found = rawResults.find(item => item.period === weekLabel);
                processedData.push({
                    label: weekLabel,
                    week: i,
                    weekStart,
                    weekEnd,
                    value: found ? Number(found.value) : 0
                });
            }
        } else if (period === 'year') {
            // Tạo array 12 tháng
            for (let i = 1; i <= 12; i++) {
                const monthStr = i.toString().padStart(2, '0');
                const found = rawResults.find(item => item.period === monthStr);
                processedData.push({
                    label: labels[i - 1],
                    month: i,
                    value: found ? Number(found.value) : 0
                });
            }
        }

        // Tính tổng và trung bình
        const totalValue = processedData.reduce((sum, item) => sum + item.value, 0);
        const averageValue = processedData.length > 0 ? totalValue / processedData.length : 0;

        return res.status(200).json({
            message: 'Lấy thống kê thành công!',
            data: {
                period,
                type,
                queryType,
                dateRange: {
                    startDate: startDate.getFullYear() + '-' + String(startDate.getMonth() + 1).padStart(2, '0') + '-' + String(startDate.getDate()).padStart(2, '0'),
                    endDate: endDate.getFullYear() + '-' + String(endDate.getMonth() + 1).padStart(2, '0') + '-' + String(endDate.getDate()).padStart(2, '0')
                },
                statistics: processedData,
                summary: {
                    total: type === 'count' ? totalValue : Math.round(averageValue * 100) / 100,
                    average: Math.round(averageValue * 100) / 100,
                    totalRecords: processedData.filter(item => item.value > 0).length
                }
            }
        });

    } catch (error) {
        console.error('Error in getUserAttemptStatistics:', error);
        return res.status(500).json({ 
            message: 'Lỗi server khi lấy thống kê',
            error: error.message 
        });
    }
};

// Thống kê tổng quan của người dùng
export const getUserOverallStatistics = async (req, res) => {
    try {
        const { id: userId } = req.user;

        // Query thống kê tổng quan
        const overallQuery = `
            SELECT 
                COUNT(*) as totalAttempts,
                COUNT(CASE WHEN endTime IS NOT NULL THEN 1 END) as completedAttempts,
                ROUND(AVG(CASE WHEN endTime IS NOT NULL AND score IS NOT NULL THEN CAST(score AS DECIMAL(5,2)) END), 2) as averageScore,
                MAX(CASE WHEN endTime IS NOT NULL AND score IS NOT NULL THEN CAST(score AS DECIMAL(5,2)) END) as highestScore,
                MIN(CASE WHEN endTime IS NOT NULL AND score IS NOT NULL THEN CAST(score AS DECIMAL(5,2)) END) as lowestScore
            FROM studentExamAttempt 
            WHERE studentId = :userId
        `;

        const [overallStats] = await db.sequelize.query(overallQuery, {
            replacements: { userId },
            type: QueryTypes.SELECT
        });

        // Thống kê theo tháng gần nhất (6 tháng)
        const monthlyQuery = `
            SELECT 
                DATE_FORMAT(endTime, '%Y-%m') as month,
                COUNT(*) as attempts,
                ROUND(AVG(CASE WHEN score IS NOT NULL THEN CAST(score AS DECIMAL(5,2)) END), 2) as avgScore
            FROM studentExamAttempt 
            WHERE studentId = :userId 
                AND endTime IS NOT NULL
                AND endTime >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            GROUP BY DATE_FORMAT(endTime, '%Y-%m')
            ORDER BY month DESC
        `;

        const monthlyStats = await db.sequelize.query(monthlyQuery, {
            replacements: { userId },
            type: QueryTypes.SELECT
        });

        return res.status(200).json({
            message: 'Lấy thống kê tổng quan thành công!',
            data: {
                overall: {
                    totalAttempts: Number(overallStats.totalAttempts) || 0,
                    completedAttempts: Number(overallStats.completedAttempts) || 0,
                    averageScore: Number(overallStats.averageScore) || 0,
                    highestScore: Number(overallStats.highestScore) || 0,
                    lowestScore: Number(overallStats.lowestScore) || 0,
                    completionRate: overallStats.totalAttempts > 0 
                        ? Math.round((overallStats.completedAttempts / overallStats.totalAttempts) * 100 * 100) / 100
                        : 0
                },
                monthlyTrend: monthlyStats.map(item => ({
                    month: item.month,
                    attempts: Number(item.attempts),
                    averageScore: Number(item.avgScore) || 0
                }))
            }
        });

    } catch (error) {
        console.error('Error in getUserOverallStatistics:', error);
        return res.status(500).json({ 
            message: 'Lỗi server khi lấy thống kê tổng quan',
            error: error.message 
        });
    }
};
