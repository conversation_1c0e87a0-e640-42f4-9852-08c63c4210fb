import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchClassById, putClass } from "../../features/class/classSlice";
import LoadingSpinner from "../loading/LoadingSpinner";
import { useNavigate } from "react-router-dom";
import { setClass } from "../../features/class/classSlice";
import DropMenuBarAdmin from "../dropMenu/OptionBarAdmin";
import { fetchCodesByType } from "../../features/code/codeSlice";
import { setSuccessMessage } from "../../features/state/stateApiSlice";
import DetailTr from "./DetailTr";
import PutMultipleImages from "../image/PutMultipleImages";
import { putSlideImagesForClass } from "../../features/class/classSlice";
import AdminSidebar from "../sidebar/AdminSidebar";
import { Home } from 'lucide-react';
import ClassAdminLayout from "src/layouts/ClassAdminLayout";

const ClassDetail = ({ classId }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { classDetail } = useSelector((state) => state.classes);
    const { codes } = useSelector((state) => state.codes);
    const { loading } = useSelector((state) => state.states);
    const { closeSidebar } = useSelector((state) => state.sidebar);
    const [validationError, setValidationError] = useState("");
    const [classData, setClassData] = useState(null);
    const initialImages = classDetail?.slide?.slideImages?.map(img => ({
        id: img.id,
        url: img.imageUrl,
    })) || [];
    const handlePutImageFuction = (images, keepImageIds, classId) => {
        dispatch(putSlideImagesForClass({
            classId,
            images,
            keepImageIds,
            slideId: classDetail?.slideId,
        })).unwrap()
            .then(() => {
                if (classId) dispatch(fetchClassById(classId));
            })
    };

    const handlePutClass = () => {
        // Validate time ranges for both sessions
        if (classData.startTime1 && classData.endTime1) {
            if (classData.startTime1 >= classData.endTime1) {
                setValidationError("Giờ bắt đầu buổi 1 phải nhỏ hơn giờ kết thúc buổi 1");
                alert("Giờ bắt đầu buổi 1 phải nhỏ hơn giờ kết thúc buổi 1");
                return;
            }
        }

        if (classData.startTime2 && classData.endTime2) {
            if (classData.startTime2 >= classData.endTime2) {
                setValidationError("Giờ bắt đầu buổi 2 phải nhỏ hơn giờ kết thúc buổi 2");
                alert("Giờ bắt đầu buổi 2 phải nhỏ hơn giờ kết thúc buổi 2");
                return;
            }
        }



        // Clear any previous validation errors
        setValidationError("");

        const data = {
            name: classData.name,
            grade: classData.grade || null,
            description: classData.description,
            dayOfWeek1: classData.dayOfWeek1 || null,
            dayOfWeek2: classData.dayOfWeek2 || null,
            startTime1: classData.startTime1 || null,
            endTime1: classData.endTime1 || null,
            startTime2: classData.startTime2 || null,
            endTime2: classData.endTime2 || null,
            academicYear: classData.academicYear,
            status: classData.status,
            public: classData.public,
            teacher: classData.teacher || null,
        }

        if (classId) {
            dispatch(putClass({ data, id: classId }))
                .unwrap()
                .then(() => {
                    dispatch(setSuccessMessage("Cập nhật lớp học thành công"));
                })
                .catch((error) => {
                    console.error("Error updating class:", error);
                });
        }
    };

    useEffect(() => {
        dispatch(fetchCodesByType(["class status", "year", "dow", "grade"]));
    }, [dispatch]);

    // useEffect(() => {
    //     if (classId) dispatch(fetchClassById(classId));
    // }, [dispatch, classId]);

    useEffect(() => {
        if (classDetail) {
            setClassData({ ...classDetail });
        }
    }, [classDetail]);

    return (
        <ClassAdminLayout>
            {/* Content */}
            {loading ? (
                <LoadingSpinner
                    size="4rem"
                    showText={true}
                    text="Đang tải thông tin lớp học..."
                />
            ) : classDetail ? (
                <div className="flex-1 p-6 pb-20">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col">
                        {/* Image Section */}
                        <div className="p-6 border-b border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Hình ảnh lớp học</h3>
                            <PutMultipleImages
                                initialImages={initialImages}
                                putImageFunction={handlePutImageFuction}
                                classId={classId}
                            />
                        </div>

                        {/* Details Table */}
                        <div className="p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Thông tin chi tiết</h3>
                            <div className="overflow-x-auto">
                                <table className="w-full border-collapse border border-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="p-4 text-left text-sm font-semibold text-gray-900 border border-gray-200 w-1/3">Thuộc tính</th>
                                            <th className="p-4 text-left text-sm font-semibold text-gray-900 border border-gray-200">Chi tiết</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <DetailTr
                                            title="ID"
                                            value={classData?.id}
                                            type={0}
                                        />
                                        <DetailTr
                                            title="Mã lớp"
                                            value={classData?.class_code}
                                            type={0}
                                        />
                                        <DetailTr
                                            title="Tên lớp"
                                            placeholder={"Nhập tên lớp"}
                                            value={classData?.name}
                                            onChange={(e) => setClassData({ ...classData, name: e.target.value })}
                                        />
                                        <DetailTr
                                            title="Giáo viên"
                                            placeholder={"Nhập tên giáo viên"}
                                            value={classData?.teacher}
                                            onChange={(e) => setClassData({ ...classData, teacher: e.target.value })}
                                        />
                                        <DetailTr
                                            title="Mô tả"
                                            placeholder={"Nhập mô tả"}
                                            value={classData?.description}
                                            onChange={(e) => setClassData({ ...classData, description: e.target.value })}
                                            type={2}
                                        />
                                        <DetailTr
                                            title="Thứ buổi 1"
                                            value={classData?.dayOfWeek1}
                                            valueText={Array.isArray(codes["dow"]) ? codes["dow"].find((item) => item.code === classData?.dayOfWeek1)?.description : ""}
                                            onChange={(option) => setClassData({ ...classData, dayOfWeek1: option })}
                                            type={3}
                                            options={Array.isArray(codes["dow"]) ? [{ code: null, description: "Không" }, ...codes["dow"]] : []}
                                        />
                                        <DetailTr
                                            title="Thứ buổi 2"
                                            value={classData?.dayOfWeek2}
                                            valueText={Array.isArray(codes["dow"]) ? codes["dow"].find((item) => item.code === classData?.dayOfWeek2)?.description : ""}
                                            onChange={(option) => setClassData({ ...classData, dayOfWeek2: option })}
                                            type={3}
                                            options={Array.isArray(codes["dow"]) ? [{ code: null, description: "Không" }, ...codes["dow"]] : []}
                                        />
                                        <DetailTr
                                            title="Khối lớp"
                                            value={classData?.grade}
                                            onChange={(option) => setClassData({ ...classData, grade: option })}
                                            type={3}
                                            options={[
                                                { code: "10", description: "Lớp 10" },
                                                { code: "11", description: "Lớp 11" },
                                                { code: "12", description: "Lớp 12" }
                                            ]}
                                        />
                                        <DetailTr
                                            title="Giờ bắt đầu buổi 1"
                                            placeholder={"Nhập giờ bắt đầu buổi 1 (HH:MM)"}
                                            value={classData?.startTime1}
                                            onChange={(e) => {
                                                setClassData({ ...classData, startTime1: e.target.value });
                                                setValidationError("");
                                            }}
                                            type={1}
                                            inputType="time"
                                        />
                                        <DetailTr
                                            title="Giờ kết thúc buổi 1"
                                            placeholder={"Nhập giờ kết thúc buổi 1 (HH:MM)"}
                                            value={classData?.endTime1}
                                            onChange={(e) => {
                                                setClassData({ ...classData, endTime1: e.target.value });
                                                setValidationError("");
                                            }}
                                            type={1}
                                            inputType="time"
                                        />
                                        <DetailTr
                                            title="Giờ bắt đầu buổi 2"
                                            placeholder={"Nhập giờ bắt đầu buổi 2 (HH:MM)"}
                                            value={classData?.startTime2}
                                            onChange={(e) => {
                                                setClassData({ ...classData, startTime2: e.target.value });
                                                setValidationError("");
                                            }}
                                            type={1}
                                            inputType="time"
                                        />
                                        <DetailTr
                                            title="Giờ kết thúc buổi 2"
                                            placeholder={"Nhập giờ kết thúc buổi 2 (HH:MM)"}
                                            value={classData?.endTime2}
                                            onChange={(e) => {
                                                setClassData({ ...classData, endTime2: e.target.value });
                                                setValidationError("");
                                            }}
                                            type={1}
                                            inputType="time"
                                        />
                                        {validationError && (
                                            <tr className="border border-[#E7E7ED]">
                                                <td colSpan="2" className="p-3 text-red-500 text-sm">
                                                    ⚠️ {validationError}
                                                </td>
                                            </tr>
                                        )}
                                        <DetailTr
                                            title="Năm học"
                                            value={classData?.academicYear}
                                            onChange={(option) => setClassData({ ...classData, academicYear: option })}
                                            type={3}
                                            options={Array.isArray(codes["year"]) ? codes["year"] : []}
                                        />
                                        <DetailTr
                                            title="Công khai"
                                            value={classData?.public}
                                            onChange={(option) => setClassData({ ...classData, public: option })}
                                            type={3}
                                            valueText={classData?.public ? "Công khai" : "Không công khai"}
                                            options={[
                                                { code: true, description: "Công khai" },
                                                { code: false, description: "Không công khai" },
                                            ]}
                                        />
                                        <DetailTr
                                            title="Trạng thái lớp học"
                                            value={classData?.status}
                                            valueText={Array.isArray(codes["class status"]) ? codes["class status"].find((item) => item.code === classData?.status)?.description : ""}
                                            onChange={(option) => setClassData({ ...classData, status: option })}
                                            type={3}
                                            options={Array.isArray(codes["class status"]) ? codes["class status"] : []}
                                        />
                                        <DetailTr
                                            title="Ngày tạo"
                                            value={new Date(classData?.createdAt).toLocaleDateString()}
                                            type={0}
                                        />
                                        <DetailTr
                                            title="Ngày cập nhật"
                                            value={new Date(classData?.updatedAt).toLocaleDateString()}
                                            type={0}
                                        />
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        {/* Save Button */}
                        <div className="p-6 border-t border-gray-200">
                            <div className="flex justify-end">
                                <button
                                    type="button"
                                    onClick={handlePutClass}
                                    className="px-6 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 transition-colors font-medium"
                                >
                                    Lưu thay đổi
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="flex items-center justify-center h-full">
                    <p className="text-gray-500">Không tìm thấy lớp học với ID {classId}.</p>
                    <button
                        onClick={() => navigate("/admin/class-management")}
                        className="ml-4 px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-lg text-gray-700"
                    >
                        ← Quay lại danh sách
                    </button>
                </div>
            )}
        </ClassAdminLayout>
    )
}

export default ClassDetail;