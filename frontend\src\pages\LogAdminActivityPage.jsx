import React from 'react';
import { AdminActivitySearch } from '../components/search/AdminActivitySearch';
import { AdminActivityTable } from '../components/table/AdminActivityTable';
import { DateRangeFilter } from '../components/filter/DateRangeFilter';

export const LogAdminActivityPage = () => {
    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Header */}
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    Nhật ký hoạt động Admin
                </h1>
                <p className="text-gray-600">
                    Theo dõi và quản lý các hoạt động của admin trong hệ thống
                </p>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
                <div className="flex flex-col sm:flex-row gap-4">
                    {/* Search */}
                    <div className="flex-1">
                        <AdminActivitySearch />
                    </div>
                    
                    {/* Date Range Filter */}
                    <div className="sm:w-64">
                        <DateRangeFilter />
                    </div>
                </div>
            </div>

            {/* Table */}
            <div className="bg-white rounded-lg shadow-sm border">
                <AdminActivityTable />
            </div>
        </div>
    );
};

export default LogAdminActivityPage;
