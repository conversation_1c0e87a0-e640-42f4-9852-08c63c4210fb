import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Plus, X, Loader2, Clock } from 'lucide-react';
import useDebouncedEffect from 'src/hooks/useDebouncedEffect';

const CustomTextareaDropdown = ({
    value,
    onChange,
    onBlur,
    placeholder = "Nhập nhận xét...",
    className = "",
    suggestions = [],
    rows = 3,
    allowCustomSuggestions = true,
    showSavingIndicator = false,
    isLoading = false,
    isPending = false,
    defaultSuggestions = [
        "Con hoàn thành tốt bài tập trên lớp",
        "Con hoàn thành bài tập trên lớp, nhưng cần cải thiện kỹ năng giải bài toán",
        "Con chưa hoàn thành bài tập trên lớp, cần cố gắng hơn",
    ],
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [customSuggestions, setCustomSuggestions] = useState([]);
    const [recentEdits, setRecentEdits] = useState([]);
    const [newSuggestion, setNewSuggestion] = useState('');
    const [showAddForm, setShowAddForm] = useState(false);

    const dropdownRef = useRef(null);
    const textareaRef = useRef(null);
    const [valueTextarea, setValueTextarea] = useState(value || '');
    const isFirstRender = useRef(true);

    // Combine all suggestions
    const allSuggestions = [...defaultSuggestions, ...suggestions, ...customSuggestions];    // Sync internal state with external value prop
    useEffect(() => {
        if (value !== valueTextarea) {
            setValueTextarea(value || '');
        }
    }, [value]); useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
                setShowAddForm(false);
            }
        };

        const handleEscapeKey = (event) => {
            if (event.key === 'Escape') {
                setIsOpen(false);
                setShowAddForm(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleEscapeKey);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleEscapeKey);
        };
    }, []);    // Load custom suggestions from localStorage
    useEffect(() => {
        const saved = localStorage.getItem('customAssignmentFeedback');
        if (saved) {
            try {
                setCustomSuggestions(JSON.parse(saved));
            } catch (error) {
                console.error('Error loading custom suggestions:', error);
            }
        }

        // Load recent edits from localStorage
        const savedEdits = localStorage.getItem('recentEdits');
        if (savedEdits) {
            try {
                setRecentEdits(JSON.parse(savedEdits));
            } catch (error) {
                console.error('Error loading recent edits:', error);
            }
        }
    }, []);// Save custom suggestions to localStorage
    const saveCustomSuggestions = (suggestions) => {
        localStorage.setItem('customAssignmentFeedback', JSON.stringify(suggestions));
        setCustomSuggestions(suggestions);
    };    // Save recent edits to localStorage (keep only 5 most recent)
    const saveRecentEdit = (text) => {
        if (!text.trim()) return;

        // Remove if already exists
        const filteredEdits = recentEdits.filter(edit => edit !== text.trim());

        // Add to beginning
        const newRecentEdits = [text.trim(), ...filteredEdits].slice(0, 5);

        // Update state and localStorage
        setRecentEdits(newRecentEdits);
        localStorage.setItem('recentEdits', JSON.stringify(newRecentEdits));
    };

    const handleSuggestionClick = (suggestion) => {
        setValueTextarea(suggestion);
        // Immediately call onChange for suggestion clicks (no debounce needed)
        onChange({ target: { value: suggestion } });
        setIsOpen(false);
    };

    const handleAddSuggestion = () => {
        if (newSuggestion.trim() && !allSuggestions.includes(newSuggestion.trim())) {
            const updatedSuggestions = [...customSuggestions, newSuggestion.trim()];
            saveCustomSuggestions(updatedSuggestions);
            setNewSuggestion('');
            setShowAddForm(false);
            handleSuggestionClick(newSuggestion.trim());
        }
    };

    const handleRemoveCustomSuggestion = (suggestionToRemove) => {
        const updatedSuggestions = customSuggestions.filter(s => s !== suggestionToRemove);
        saveCustomSuggestions(updatedSuggestions);
    }; const handleTextareaBlur = (e) => {
        // Only call onBlur if it's provided
        if (onBlur) {
            // Delay to allow dropdown clicks
            setTimeout(() => {
                onBlur(e);
            }, 150);
        }
    }; const handleTextareaFocus = () => {
        setIsOpen(true);
        // Pre-fill the edit form with current value when opening
        if (valueTextarea) {
            setNewSuggestion(valueTextarea);
        }
    };
    return (
        <div
            ref={dropdownRef} className={`relative ${className}`}>
            <div className="relative ">
                <textarea
                    ref={textareaRef}
                    rows={rows}
                    value={valueTextarea}
                    readOnly={true}
                    onChange={(e) => setValueTextarea(e.target.value)}
                    onFocus={handleTextareaFocus}
                    onBlur={handleTextareaBlur}
                    placeholder={placeholder}
                    className="read-only:cursor-pointer w-full px-3 py-2 pr-8 border border-gray-300 rounded text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                /><div className="absolute right-2 top-2 flex items-center gap-1">
                    {/* Status indicator */}
                    {showSavingIndicator && (
                        <div className="flex items-center">
                            {isLoading ? (
                                <Loader2 size={14} className="text-blue-500 animate-spin" />
                            ) : isPending ? (
                                <Clock size={14} className="text-yellow-500" />
                            ) : null}
                        </div>
                    )}
                    {/* Dropdown button */}
                    <button
                        type="button"
                        onClick={() => setIsOpen(!isOpen)}
                        className="text-gray-400 hover:text-gray-600"
                    >
                        <ChevronDown
                            size={16}
                            className={`transform transition-transform ${isOpen ? 'rotate-180' : ''}`}
                        />
                    </button>
                </div>
            </div>
            {isOpen && (
                <div className="absolute z-[100] w-[20rem] mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-64 overflow-y-auto">
                    {/* Custom suggestions */}
                    {customSuggestions.length > 0 && (
                        <div className="p-2 border-b border-gray-200">
                            <div className="text-xs font-medium text-gray-500 mb-2">Nhận xét tùy chỉnh:</div>
                            {customSuggestions.map((suggestion, index) => (
                                <div key={`custom-${index}`} className="flex items-center group">
                                    <button
                                        type="button"
                                        onClick={() => handleSuggestionClick(suggestion)}
                                        className="flex-1 text-left px-3 py-2 text-sm hover:bg-gray-100 rounded"
                                    >
                                        {suggestion}
                                    </button>
                                    {allowCustomSuggestions && (
                                        <button
                                            type="button"
                                            onClick={() => handleRemoveCustomSuggestion(suggestion)}
                                            className="opacity-0 group-hover:opacity-100 p-1 text-red-500 hover:text-red-700 transition-opacity"
                                        >
                                            <X size={14} />
                                        </button>
                                    )}
                                </div>
                            ))}                        </div>
                    )}

                    {/* Recent edits */}
                    {recentEdits.length > 0 && (
                        <div className="p-2 border-b border-gray-200">
                            <div className="text-xs font-medium text-gray-500 mb-2">Chỉnh sửa gần đây:</div>
                            {recentEdits.map((edit, index) => (
                                <button
                                    key={`recent-${index}`}
                                    type="button"
                                    onClick={() => handleSuggestionClick(edit)}
                                    className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded"
                                >
                                    {edit}
                                </button>
                            ))}
                        </div>
                    )}

                    {/* Add custom suggestion */}
                    {allowCustomSuggestions && (
                        <div className="p-2">
                            <div className="space-y-2">
                                <textarea
                                    rows={3}
                                    value={newSuggestion}
                                    onChange={(e) => setNewSuggestion(e.target.value)}
                                    placeholder="Nhập hoặc chỉnh sửa nhận xét..."
                                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                                />
                                <div className="flex gap-2">                                        <button
                                    type="button"
                                    onClick={() => {
                                        setValueTextarea(newSuggestion);
                                        onChange({ target: { value: newSuggestion } });
                                        // Save to recent edits when applying
                                        saveRecentEdit(newSuggestion);
                                        setIsOpen(false);
                                        setShowAddForm(false);
                                        setNewSuggestion('');
                                    }}
                                    className="flex-1 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Áp dụng
                                </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowAddForm(false);
                                            setNewSuggestion('');
                                        }}
                                        className="flex-1 px-3 py-1 text-sm border border-gray-300 text-gray-700 rounded hover:bg-gray-50"
                                    >
                                        Hủy
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Default suggestions */}
                    <div className="p-2 border-b border-gray-200">
                        <div className="text-xs font-medium text-gray-500 mb-2">Nhận xét mẫu:</div>
                        {defaultSuggestions.map((suggestion, index) => (
                            <button
                                key={`default-${index}`}
                                type="button"
                                onClick={() => handleSuggestionClick(suggestion)}
                                className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 rounded"
                            >
                                {suggestion}
                            </button>
                        ))}
                    </div>


                </div>
            )}
        </div>
    );
};

export default CustomTextareaDropdown;
