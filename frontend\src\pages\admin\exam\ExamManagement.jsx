import { useEffect, useState } from "react";
import AdminLayout from "../../../layouts/AdminLayout";
import FunctionBarAdmin from "../../../components/bar/FunctionBarAdmin";
import ExamTable from "../../../components/table/ExamTable";
import { useSelector, useDispatch } from "react-redux";
// import { setIsAddView, setIsFilterView } from "../../../features/filter/filterSlice";
// import AddExamModal from "../../../components/modal/AddExamModal";
// import AdminModal from "../../../components/modal/AdminModal";
import { fetchExams } from "../../../features/exam/examSlice";
import { setCurrentPage, setLimit, setSearch } from "src/features/exam/examSlice";
import { useNavigate } from "react-router-dom";
import useDebouncedEffect from "src/hooks/useDebouncedEffect";
import ButtonFunctionBarAdmin from "src/components/button/ButtonFunctionBarAdmin";
import { Plus, Bot } from "lucide-react";
import Pagination from "src/components/Pagination";

const ExamManagement = () => {
    const dispatch = useDispatch();
    const { isAddView, isFilterVIew } = useSelector(state => state.filter);
    const { exams, pagination, search } = useSelector(state => state.exams);
    const navigate = useNavigate();
    const { page: currentPage, pageSize: limit, sortOrder } = pagination;
    const [inputValue, setInputValue] = useState("");
    const options = [5, 10, 15, 20, 30];

    useEffect(() => {
        dispatch(fetchExams({ search, currentPage, limit, sortOrder }));
    }, [dispatch, search, currentPage, limit, sortOrder]);

    useDebouncedEffect(() => {
        dispatch(setSearch(inputValue));
    }, [inputValue], 1000);

    const handleAddExam = () => {
        navigate(`/admin/exam-management/add`);
    }

    const handleAutoGenerateExam = () => {
        navigate(`/admin/exam-management/auto-generate`);
    }

    return (
        <AdminLayout>
            {/* <AdminModal isOpen={isAddView} headerText={'Tạo câu hỏi mới'} onClose={() => dispatch(setIsAddView(false))} >
                <AddExamModal onClose={() => dispatch(setIsAddView(false))} fetchExams={fetchExams} />
            </AdminModal> */}
            <div className="text-[#090a0a] text-[32px] font-bold font-bevietnam leading-9">
                Danh sách đề thi
            </div>

            <div className="w-full space-y-3 pb-4">
                {/* Main Action Bar */}
                <div className="flex flex-col lg:flex-row lg:items-center gap-3">
                    {/* Left Side: Search */}
                    <div className="flex items-center gap-3 min-w-0">
                        <div className="relative flex-1 max-w-sm">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 16 16"
                                fill="none"
                                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                            >
                                <path
                                    d="M13.3333 13.3333L10.7555 10.7556M12.1481 7.40741C12.1481 10.0256 10.0256 12.1481 7.40736 12.1481C4.78913 12.1481 2.66663 10.0256 2.66663 7.40741C2.66663 4.78917 4.78913 2.66667 7.40736 2.66667C10.0256 2.66667 12.1481 4.78917 12.1481 7.40741Z"
                                    stroke="currentColor"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                            </svg>
                            <input
                                type="text"
                                placeholder="Tìm kiếm học sinh..."
                                value={inputValue}
                                onChange={(e) => setInputValue(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                            />
                        </div>
                    </div>

                    {/* Right Side: Action Buttons */}
                    <div className="flex items-center gap-2 flex-shrink-0">

                        <ButtonFunctionBarAdmin
                            icon={<Plus size={16} />}
                            text={'Thêm đề mới'}
                            onClick={handleAddExam}
                        />
                        <ButtonFunctionBarAdmin
                            icon={<Bot size={16} />}
                            text={'Tạo đề tự động'}
                            onClick={handleAutoGenerateExam}
                        />
                    </div>
                </div>

                {/* Pagination Controls */}
                {pagination && (
                    <div className="flex flex-col justify-end sm:flex-row sm:items-center gap-3 pt-2 border-t border-gray-100">
                        {/* Left: Items per page */}
                        <div className="flex items-center gap-2 text-sm text-gray-700">
                            <label htmlFor="limitSelect" className="font-medium whitespace-nowrap">
                                Hiển thị:
                            </label>
                            <div className="relative">
                                <select
                                    id="limitSelect"
                                    value={limit}
                                    onChange={(e) => dispatch(setLimit(Number(e.target.value)))}
                                    className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-1.5 pr-8 text-sm focus:outline-none focus:ring-1 focus:ring-sky-500 focus:border-sky-500"
                                >
                                    {options.map((option) => (
                                        <option key={option} value={option}>
                                            {option}
                                        </option>
                                    ))}
                                </select>
                                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                    <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>
                            <span className="text-gray-500 whitespace-nowrap">/ trang</span>
                        </div>

                        {/* Right: Pagination */}
                        {/* {totalItems > limit && ( */}
                        <div className="flex items-center justify-end">
                            <Pagination
                                currentPage={currentPage}
                                totalItems={pagination.total}
                                limit={limit}
                                onPageChange={(page) => dispatch(setCurrentPage(page))}
                            />
                        </div>
                        {/* )} */}
                    </div>
                )}
            </div>
            <ExamTable exams={exams} fetchExams={fetchExams} />
        </AdminLayout >
    );
}

export default ExamManagement;