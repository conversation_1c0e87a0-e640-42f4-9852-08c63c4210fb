import db from "../models/index.js"

export const joinExam = async (req, res) => {
    const { id } = req.user;
    const { examId } = req.params;

    const t = await db.sequelize.transaction();

    try {
        const exam = await db.Exam.findByPk(examId, {
            include: [{ model: db.Question, as: "questions", attributes: ['id'] }],
            transaction: t
        });

        if (!exam) {
            await t.rollback();
            return res.status(404).json({ message: "Đề thi không tồn tại." });
        }

        const allAttempts = await db.StudentExamAttempt.findAll({
            where: { studentId: id, examId },
            transaction: t
        });
        const unfinishedAttempt = allAttempts.find(attempt => attempt.endTime === null);
        const hasReachedLimit =
            exam.attemptLimit !== null &&
            allAttempts.length >= exam.attemptLimit &&
            !unfinishedAttempt;

        if (hasReachedLimit) {
            await t.rollback();
            return res.status(403).json({
                message: "<PERSON>ạn đã đạt giới hạn số lần làm bài cho phép.",
            });
        }

        let currentAttempt = unfinishedAttempt;

        if (!currentAttempt) {
            // Tạo mới lượt làm bài
            currentAttempt = await db.StudentExamAttempt.create({
                studentId: id,
                examId,
                startTime: new Date(),
                endTime: null,
                score: null
            }, { transaction: t });

            // Tạo các bản ghi Answer rỗng ban đầu
            const answers = exam.questions.map(q => ({
                attemptId: currentAttempt.id,
                questionId: q.id,
                answerContent: "",
                result: null,
                createdAt: new Date(),
                updatedAt: new Date()
            }));

            await db.Answer.bulkCreate(answers, { transaction: t });
        }

        await t.commit();

        return res.status(200).json({
            attemptId: currentAttempt.id,
            description: exam.description,
            testDuration: exam.testDuration,
            startTime: currentAttempt.startTime,
        });
    } catch (err) {
        console.error("Lỗi khi tham gia bài thi:", err);
        await t.rollback();
        return res.status(500).json({ message: "Lỗi khi tham gia bài thi." });
    }
};


// controllers/answerController.js
import { ANSWER_TYPES, PREFIX_TN } from '../socket/constants.js'; // giả sử bạn có constants định nghĩa trước

export const submitAnswerHandler = async (req, res) => {
    const { id: studentId } = req.user;

    const {
        attemptId,
        questionId,
        answerContent,
        type,
        examId,
        name // để dùng cho thông báo admin sau này nếu cần
    } = req.body;

    const t = await db.sequelize.transaction();

    try {
        const existing = await db.Answer.findOne({
            where: { attemptId, questionId },
            transaction: t
        });

        let answer = '';
        let isCorrect = false;

        console.log("Answer content:", answerContent);

        if (type === ANSWER_TYPES.MULTIPLE_CHOICE) {
            const statement = await db.Statement.findByPk(answerContent, { transaction: t });
            answer = PREFIX_TN[statement?.order - 1];
            isCorrect = statement?.isCorrect || false;
        }

        else if (type === ANSWER_TYPES.TRUE_FALSE) {
            if (!Array.isArray(answerContent)) {
                await t.rollback();
                return res.status(400).json({ message: "Answer content phải là mảng các statement" });
            }

            const statementIds = answerContent.map(item => item.statementId);
            const statements = await db.Statement.findAll({
                where: { id: statementIds },
                attributes: ['id', 'order', 'isCorrect'],
                transaction: t
            });

            const enrichedAnswerContent = answerContent.map(item => {
                const stmt = statements.find(s => s.id === item.statementId);
                return {
                    ...item,
                    order: stmt?.order || 0,
                    isCorrect: stmt?.isCorrect
                };
            }).sort((a, b) => a.order - b.order);

            answer = '';
            let allCorrect = true;

            for (const item of enrichedAnswerContent) {
                answer += item.answer ? 'Đ ' : 'S ';
                if (item.isCorrect !== item.answer) {
                    allCorrect = false;
                }
            }

            isCorrect = allCorrect;
        }

        else if (type === ANSWER_TYPES.SHORT_ANSWER) {
            const question = await db.Question.findByPk(questionId, { transaction: t });
            const formattedAnswer = answerContent.trim().replace(',', '.');
            isCorrect = question?.correctAnswer.trim().replace(',', '.') === formattedAnswer;
            answer = formattedAnswer;
        }

        const formattedAnswerContent =
            type === ANSWER_TYPES.TRUE_FALSE
                ? JSON.stringify(answerContent)
                : type === ANSWER_TYPES.SHORT_ANSWER
                    ? answerContent.trim().replace(',', '.')
                    : answerContent;

        if (existing) {
            await db.Answer.update(
                {
                    answerContent: formattedAnswerContent,
                    result: isCorrect,
                },
                { where: { id: existing.id }, transaction: t }
            );
        } else {
            await db.Answer.create(
                {
                    attemptId,
                    questionId,
                    answerContent: formattedAnswerContent,
                    studentId,
                    result: isCorrect,
                },
                { transaction: t }
            );
        }

        await t.commit();

        const isDSFullyAnswered =
            type === ANSWER_TYPES.TRUE_FALSE &&
            Array.isArray(answerContent) &&
            answerContent.length >= 4;

        return res.status(200).json({
            message: "Lưu đáp án thành công",
            questionId,
            attemptId,
            answerContent: answer,
            isCorrect,
            shouldNotifyAdmin:
                type !== ANSWER_TYPES.TRUE_FALSE || isDSFullyAnswered,
        });

    } catch (err) {
        console.error("Lỗi khi ghi đáp án:", err);
        await t.rollback();
        return res.status(500).json({
            message: "Không thể lưu đáp án",
            questionId,
        });
    }
};


export const calculateScoreHandler = async (req, res) => {
    const { attemptId } = req.params;
    const { answers, examId, student } = req.body;

    const t = await db.sequelize.transaction();

    try {
        const attempt = await db.StudentExamAttempt.findByPk(attemptId, { transaction: t });
        if (!attempt) {
            await t.rollback();
            return res.status(404).json({ message: "Không tìm thấy lượt làm bài." });
        }

        if (attempt.endTime) {
            await t.rollback();
            return res.status(400).json({ message: "Bài thi đã kết thúc." });
        }

        const questionIds = [];
        const tnStatementIds = [];
        const dsStatementIdsMap = {};

        for (const answer of answers) {
            const { questionId, typeOfQuestion, answerContent } = answer;
            questionIds.push(questionId);

            if (typeOfQuestion === ANSWER_TYPES.MULTIPLE_CHOICE && answerContent) {
                tnStatementIds.push(answerContent);
            } else if (typeOfQuestion === ANSWER_TYPES.TRUE_FALSE && answerContent) {
                const answersDS = typeof answerContent === 'string'
                    ? JSON.parse(answerContent)
                    : answerContent;

                if (Array.isArray(answersDS)) {
                    dsStatementIdsMap[questionId] = answersDS.map(a => a.statementId);
                }
            }
        }

        const questions = await db.Question.findAll({
            where: { id: questionIds },
            transaction: t
        });

        const questionMap = {};
        questions.forEach(q => {
            questionMap[q.id] = q;
        });

        const allStatementIds = [...tnStatementIds, ...Object.values(dsStatementIdsMap).flat()];
        const statements = await db.Statement.findAll({
            where: { id: allStatementIds },
            transaction: t
        });

        const statementMap = {};
        statements.forEach(s => {
            statementMap[s.id] = s;
        });

        let totalScore = 0;

        for (const answer of answers) {
            const { questionId, typeOfQuestion, answerContent } = answer;
            const question = questionMap[questionId];
            if (!question) continue;

            if (typeOfQuestion === ANSWER_TYPES.MULTIPLE_CHOICE) {
                const statement = statementMap[answerContent];
                if (statement?.isCorrect) {
                    totalScore += 0.25;
                }
            }

            else if (typeOfQuestion === ANSWER_TYPES.SHORT_ANSWER) {
                const formattedAnswer = answerContent.trim().replace(',', '.');
                if (question.correctAnswer.trim().replace(',', '.') === formattedAnswer) {
                    totalScore += 0.5;
                }
            }

            else if (typeOfQuestion === ANSWER_TYPES.TRUE_FALSE) {
                const answersDS = typeof answerContent === 'string'
                    ? JSON.parse(answerContent)
                    : answerContent;

                let count = 0;
                for (const ans of answersDS || []) {
                    const stmt = statementMap[ans.statementId];
                    if (stmt && stmt.isCorrect === ans.answer) {
                        count++;
                    }
                }

                if (count === 1) totalScore += 0.1;
                else if (count === 2) totalScore += 0.25;
                else if (count === 3) totalScore += 0.5;
                else if (count >= 4) totalScore += 1.0;
            }
        }

        attempt.score = parseFloat(totalScore.toFixed(2));
        await attempt.save({ transaction: t });

        await t.commit();

        // OPTIONAL: nếu muốn emit cho admin bằng Socket.IO
        // io.to(`${ROOMS.ADMIN_EXAM}${examId}`).emit(EVENTS.ADMIN_SCORE_CALCULATED, { attempt, student });

        return res.status(200).json({
            message: 'Tính điểm thành công',
            score: attempt.score,
            attempt
        });

    } catch (err) {
        await t.rollback();
        console.error("Lỗi khi tính điểm:", err);
        return res.status(500).json({ message: "Không thể tính điểm", error: err.message });
    }
};

// API để lấy thời gian còn lại của bài thi
export const getRemainingTimeHandler = async (req, res) => {
    try {
        const { examId, attemptId } = req.params;
        const userId = req.user.id;

        // Tìm attempt
        const attempt = await db.StudentExamAttempt.findOne({
            where: {
                id: attemptId,
                studentId: userId,
                examId: examId
            },
            include: [{
                model: db.Exam,
                as: 'exam',
                attributes: ['testDuration']
            }],
            transaction: null
        });

        if (!attempt) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài thi'
            });
        }

        if (!attempt.exam.testDuration) {
            return res.status(200).json({
                success: true,
                data: {
                    remainingTime: null,
                    isTimeUp: false
                }
            });
        }

        // Tính thời gian còn lại
        const startTime = new Date(attempt.startTime);
        const currentTime = new Date();
        const elapsedTime = Math.floor((currentTime - startTime) / 1000); // seconds
        const totalTime = attempt.exam.testDuration * 60; // convert minutes to seconds
        // console.log('totalTime', totalTime, 'elapsedTime', elapsedTime);
        const remainingTime = Math.max(0, totalTime - elapsedTime);

        // Nếu hết thời gian, tự động submit
        if (remainingTime <= 0 && !attempt.endTime) {
            await attempt.update({
                endTime: new Date()
            });
        }

        res.json({
            success: true,
            data: {
                remainingTime,
                isTimeUp: remainingTime <= 0
            }
        });

    } catch (error) {
        console.error('Error getting remaining time:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lấy thời gian'
        });
    }
};

// API để log hoạt động của user
export const logUserActivityHandler = async (req, res) => {
    try {
        const { examId, attemptId, activityType, details } = req.body;
        const userId = req.user.id;

        // Verify attempt belongs to user
        const attempt = await db.StudentExamAttempt.findOne({
            where: {
                id: attemptId,
                studentId: userId,
                examId: examId
            }
        });

        if (!attempt) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài thi'
            });
        }

        // Log activity (you can save to database if needed)
        console.log(`User ${userId} activity in exam ${examId}:`, {
            activityType,
            details,
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Đã ghi log hoạt động'
        });

    } catch (error) {
        console.error('Error logging user activity:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi ghi log'
        });
    }
};

// API để submit answer với attemptId (thay thế socket)
export const submitAnswerWithAttemptHandler = async (req, res) => {
    try {
        const { questionId, answerContent, type, attemptId } = req.body;
        const userId = req.user.id;

        // Verify attempt belongs to user
        const attempt = await db.StudentExamAttempt.findOne({
            where: {
                id: attemptId,
                studentId: userId,
                endTime: null // Chỉ cho phép submit khi bài thi chưa kết thúc
            }
        });

        if (!attempt) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài thi hoặc bài thi đã kết thúc'
            });
        }

        // Sử dụng lại logic từ submitAnswerHandler
        const t = await db.sequelize.transaction();

        try {
            const existing = await db.Answer.findOne({
                where: { attemptId, questionId },
                transaction: t
            });
            let answer = '';
            let isCorrect = false;

            if (type === ANSWER_TYPES.MULTIPLE_CHOICE) {
                const statement = await db.Statement.findByPk(answerContent, { transaction: t });
                answer = PREFIX_TN[statement?.order - 1];
                isCorrect = statement?.isCorrect || false;
            }
            else if (type === ANSWER_TYPES.TRUE_FALSE) {
                if (!Array.isArray(answerContent)) {
                    await t.rollback();
                    return res.status(400).json({
                        success: false,
                        message: "Answer content phải là mảng các statement"
                    });
                }

                const statementIds = answerContent.map(item => item.statementId);
                const statements = await db.Statement.findAll({
                    where: { id: statementIds },
                    attributes: ['id', 'order', 'isCorrect'],
                    transaction: t
                });

                const enrichedAnswerContent = answerContent.map(item => {
                    const stmt = statements.find(s => s.id === item.statementId);
                    return {
                        ...item,
                        order: stmt?.order || 0,
                        isCorrect: stmt?.isCorrect
                    };
                }).sort((a, b) => a.order - b.order);

                answer = '';
                if (enrichedAnswerContent.length < 4) {
                    isCorrect = false;
                } else {
                    let allCorrect = true;

                    for (const item of enrichedAnswerContent) {
                        answer += item.answer ? 'Đ ' : 'S ';
                        if (item.isCorrect !== item.answer) {
                            allCorrect = false;
                        }
                    }

                    isCorrect = allCorrect;
                }
            }
            else if (type === ANSWER_TYPES.SHORT_ANSWER) {
                const question = await db.Question.findByPk(questionId, { transaction: t });
                const formattedAnswer = answerContent.trim().replace(',', '.');
                isCorrect = question?.correctAnswer.trim().replace(',', '.') === formattedAnswer;
                answer = formattedAnswer;
            }

            const formattedAnswerContent =
                type === ANSWER_TYPES.TRUE_FALSE
                    ? JSON.stringify(answerContent)
                    : type === ANSWER_TYPES.SHORT_ANSWER
                        ? answerContent.trim().replace(',', '.')
                        : answerContent;

            if (existing) {
                await db.Answer.update(
                    {
                        answerContent: formattedAnswerContent,
                        result: isCorrect,
                    },
                    { where: { id: existing.id }, transaction: t }
                );
            } else {
                await db.Answer.create(
                    {
                        attemptId,
                        questionId,
                        answerContent: formattedAnswerContent,
                        studentId: userId,
                        result: isCorrect,
                    },
                    { transaction: t }
                );
            }

            await t.commit();

            res.json({
                success: true,
                message: 'Đã lưu câu trả lời',
                data: {
                    questionId,
                    answerContent: answer
                }
            });

        } catch (err) {
            console.error("Lỗi khi ghi đáp án:", err);
            await t.rollback();
            res.status(500).json({
                success: false,
                message: "Không thể lưu đáp án"
            });
        }

    } catch (error) {
        console.error('Error submitting answer:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server khi lưu câu trả lời'
        });
    }
};

// API để leave exam
export const leaveExamHandler = async (req, res) => {
    try {
        const { examId, attemptId } = req.body;
        const userId = req.user.id;

        // Verify attempt belongs to user
        const attempt = await db.StudentExamAttempt.findOne({
            where: {
                id: attemptId,
                studentId: userId,
                examId: examId
            }
        });

        if (!attempt) {
            return res.status(404).json({
                success: false,
                message: 'Không tìm thấy bài thi'
            });
        }

        // Log leave activity
        console.log(`User ${userId} left exam ${examId} at ${new Date()}`);

        res.json({
            success: true,
            message: 'Đã ghi nhận rời khỏi bài thi'
        });

    } catch (error) {
        console.error('Error leaving exam:', error);
        res.status(500).json({
            success: false,
            message: 'Lỗi server'
        });
    }
};
