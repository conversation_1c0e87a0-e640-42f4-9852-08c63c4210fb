import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import * as adminActivityLogApi from "../../services/adminActivityLogApi";
import { apiHandler } from "../../utils/apiHandler";

// Async thunk để fetch admin activities
export const fetchAdminActivities = createAsyncThunk(
    "adminActivityLog/fetchAdminActivities",
    async ({ search, limit, cursor, sortOrder, startDate, endDate, loadMore = false }, { dispatch, getState }) => {
        const params = { search, limit, cursor, sortOrder, startDate, endDate };
        return await apiHandler(
            dispatch, 
            adminActivityLogApi.getAdminActivitiesAPI, 
            params, 
            (data) => {
                // Success callback - không cần xử lý gì thêm
            }, 
            true, 
            false
        );
    }
);

// Helper function để tính toán tuần hiện tại
const getCurrentWeekRange = () => {
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, ...
    const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Tính offset để về thứ 2
    
    const monday = new Date(now);
    monday.setDate(now.getDate() + mondayOffset);
    monday.setHours(0, 0, 0, 0);
    
    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);
    sunday.setHours(23, 59, 59, 999);
    
    return {
        startDate: monday.toISOString(),
        endDate: sunday.toISOString()
    };
};

const weekRange = getCurrentWeekRange();

const initialState = {
    activities: [],
    loading: false,
    loadingMore: false,
    error: null,
    hasNextPage: false,
    nextCursor: null,
    search: '',
    limit: 20,
    sortOrder: 'DESC',
    totalLoaded: 0,
    startDate: weekRange.startDate,
    endDate: weekRange.endDate
};

const adminActivityLogSlice = createSlice({
    name: "adminActivityLog",
    initialState,
    reducers: {
        setSearch: (state, action) => {
            state.search = action.payload;
        },
        setSortOrder: (state) => {
            state.sortOrder = state.sortOrder === 'DESC' ? 'ASC' : 'DESC';
            // Reset data khi thay đổi sort order
            state.activities = [];
            state.nextCursor = null;
            state.hasNextPage = false;
            state.totalLoaded = 0;
        },
        setLimit: (state, action) => {
            state.limit = action.payload;
        },
        setDateRange: (state, action) => {
            state.startDate = action.payload.startDate;
            state.endDate = action.payload.endDate;
            // Reset data khi thay đổi date range
            state.activities = [];
            state.nextCursor = null;
            state.hasNextPage = false;
            state.totalLoaded = 0;
        },
        resetActivities: (state) => {
            state.activities = [];
            state.nextCursor = null;
            state.hasNextPage = false;
            state.totalLoaded = 0;
        },
        clearError: (state) => {
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchAdminActivities.pending, (state, action) => {
                const { cursor } = action.meta.arg;
                if (cursor) {
                    // Load more
                    state.loadingMore = true;
                } else {
                    // First load or refresh
                    state.loading = true;
                    state.activities = [];
                    state.totalLoaded = 0;
                }
                state.error = null;
            })
            .addCase(fetchAdminActivities.fulfilled, (state, action) => {
                if (action.payload && action.payload.data) {
                    const { activities, hasNextPage, nextCursor } = action.payload.data;
                    const { cursor } = action.meta.arg;
                    
                    if (cursor) {
                        // Load more - append to existing activities
                        state.activities = [...state.activities, ...activities];
                    } else {
                        // First load or refresh - replace activities
                        state.activities = activities;
                    }
                    
                    state.hasNextPage = hasNextPage;
                    state.nextCursor = nextCursor;
                    state.totalLoaded = state.activities.length;
                }
                state.loading = false;
                state.loadingMore = false;
            })
            .addCase(fetchAdminActivities.rejected, (state, action) => {
                state.loading = false;
                state.loadingMore = false;
                state.error = action.error.message;
            });
    }
});

export const {
    setSearch,
    setSortOrder,
    setLimit,
    setDateRange,
    resetActivities,
    clearError
} = adminActivityLogSlice.actions;

export default adminActivityLogSlice.reducer;
