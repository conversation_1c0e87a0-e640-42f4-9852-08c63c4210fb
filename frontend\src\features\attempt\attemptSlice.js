
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { apiHandler } from "../../utils/apiHandler";
import { 
    getAttemptsByExamIdApi, 
    getAttemptByUserIdApi, 
    getAttemptByStudentIdApi, 
    getAttemptByUser, 
    getAttemptByExamIdAdminApi,
    getUserAttemptStatisticsApi,
    getUserOverallStatisticsApi
} from "../../services/attemptApi";
import { setExam } from "../exam/examSlice";
import { setCurrentPage, setLimit, setTotalItems } from "../filter/filterSlice";

export const fetchAttemptsByExamId = createAsyncThunk(
    "attempts/fetchAttemptsByExamId",
    async ({ examId, currentPage }, { dispatch }) => {
        return await apiHandler(dispatch, getAttemptsByExamIdApi, { examId, currentPage }, (data) => {
            dispatch(setCurrentPage(data.currentPage));
            dispatch(setTotalItems(data.totalItems));
            dispatch(setLimit(data.limit));
            dispatch(setExam(data.exam));
        }, false, false);
    }
);

export const fetchAttemptsByUser = createAsyncThunk(
    "attempts/fetchAttemptsByUser",
    async (data, { dispatch }) => {
        return await apiHandler(dispatch, getAttemptByUser, data, (data) => {
            // Không cập nhật filter slice nữa, sẽ sử dụng state riêng
        }, false, false);
    }
);

export const fetchAttemptsByUserId = createAsyncThunk(
    "attempts/fetchAttemptsByUserId",
    async ({ userId, params }, { dispatch }) => {
        return await apiHandler(dispatch, getAttemptByUserIdApi, { userId, params }, (data) => {
            // console.log("data", data);
            dispatch(setCurrentPage(data.pagination.page));
            dispatch(setTotalItems(data.pagination.total));
            dispatch(setLimit(data.pagination.pageSize));

        }, false, false, false, true);
    }
);

export const fetchAttemptByStudentId = createAsyncThunk(
    "attempts/fetchAttemptByStudentId",
    async ({ examId }, { dispatch }) => {
        return await apiHandler(dispatch, getAttemptByStudentIdApi, { examId }, (data) => {
            dispatch(setExam(data.exam));
            dispatch(setCurrentPage(1));
            dispatch(setLimit(10));
        }, false, false);
    }
);

export const fetchAttemptByExamIdAdmin = createAsyncThunk(
    "attempts/fetchAttemptByExamIdAdmin",
    async ({ examId, search = "", currentPage = 1, limit = 10 }, { dispatch }) => {
        return await apiHandler(dispatch, getAttemptByExamIdAdminApi, { examId, search, currentPage, limit }, (data) => {
            dispatch(setCurrentPage(data.data.currentPage));
            dispatch(setTotalItems(data.data.totalItems));
            dispatch(setLimit(data.data.limit));
            // console.log("data", data);
        }, true, false);
    }
);

// Thunk cho thống kê biểu đồ
export const fetchUserAttemptStatistics = createAsyncThunk(
    "attempts/fetchUserAttemptStatistics",
    async ({ period = 'week', type = 'count', date = null }, { dispatch }) => {
        return await apiHandler(dispatch, getUserAttemptStatisticsApi, { period, type, date }, null, false, false);
    }
);

// Thunk cho thống kê tổng quan
export const fetchUserOverallStatistics = createAsyncThunk(
    "attempts/fetchUserOverallStatistics",
    async (_, { dispatch }) => {
        return await apiHandler(dispatch, getUserOverallStatisticsApi, {}, null, false, false);
    }
);

const attemptSlice = createSlice({
    name: "attempts",
    initialState: {
        attempts: [],
        bestAttempt: null,
        userRank: null,
        userAttemptCount: null,
        loadingAttempt: false,
        userAttempts: [],
        loading: false,
        // Pagination riêng cho fetchAttemptsByUser
        userAttemptsPagination: {
            currentPage: 1,
            totalItems: 0,
            limit: 10,
            totalPages: 0
        },
        // Statistics state
        statistics: {
            chartData: null,
            overviewData: null,
            loadingChart: false,
            loadingOverview: false,
            chartError: null,
            overviewError: null
        }
    },
    reducers: {
        setAttempts: (state, action) => {
            state.attempts = action.payload;
        },
        // Actions để quản lý pagination riêng cho fetchAttemptsByUser
        setUserAttemptsPage: (state, action) => {
            state.userAttemptsPagination.currentPage = action.payload;
        },
        setUserAttemptsLimit: (state, action) => {
            state.userAttemptsPagination.limit = action.payload;
        },
        resetUserAttemptsPagination: (state) => {
            state.userAttemptsPagination = {
                currentPage: 1,
                totalItems: 0,
                limit: 10,
                totalPages: 0
            };
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchAttemptsByExamId.pending, (state) => {
                state.attempts = [];
                state.bestAttempt = null;
                state.userRank = null;
                state.userAttemptCount = null;
            })
            .addCase(fetchAttemptsByExamId.fulfilled, (state, action) => {
                if (action.payload) {
                    state.attempts = action.payload.attempts;
                    state.bestAttempt = action.payload.userBestAttempt;
                    state.userRank = action.payload.userRank;
                    state.userAttemptCount = action.payload.userAttemptCount;
                }
            })
            .addCase(fetchAttemptsByUser.pending, (state) => {
                state.attempts = [];
                state.loadingAttempt = true;
            })
            .addCase(fetchAttemptsByUser.fulfilled, (state, action) => {
                if (action.payload) {
                    state.attempts = action.payload.data;
                    // Cập nhật pagination riêng
                    const { pagination } = action.payload;

                    state.userAttemptsPagination.currentPage = pagination.page || 1;
                    state.userAttemptsPagination.totalItems = pagination.total || 0;
                    state.userAttemptsPagination.limit = pagination.pageSize || 10;
                    state.userAttemptsPagination.totalPages = pagination.totalPages || 0;
                }
                state.loadingAttempt = false;
            })
            .addCase(fetchAttemptsByUser.rejected, (state) => {
                state.loadingAttempt = false;
            })
            .addCase(fetchAttemptByStudentId.pending, (state) => {
                state.attempts = [];
            })
            .addCase(fetchAttemptByStudentId.fulfilled, (state, action) => {
                if (action.payload) {
                    state.attempts = action.payload.data;
                }
            })
            .addCase(fetchAttemptByExamIdAdmin.pending, (state) => {
                state.attempts = [];
            })
            .addCase(fetchAttemptByExamIdAdmin.fulfilled, (state, action) => {
                if (action.payload) {
                    state.attempts = action.payload.data.data;
                }
            })
            .addCase(fetchAttemptsByUserId.pending, (state) => {
                state.userAttempts = [];
                state.loading = true;
            })
            .addCase(fetchAttemptsByUserId.fulfilled, (state, action) => {
                // console.log("action.payload", action.payload);
                if (action.payload) {
                    state.userAttempts = action.payload.data;
                }
                state.loading = false;
            })
            .addCase(fetchAttemptsByUserId.rejected, (state) => {
                state.userAttempts = [];
                state.loading = false;
            })
            // Statistics reducers
            .addCase(fetchUserAttemptStatistics.pending, (state) => {
                state.statistics.loadingChart = true;
                state.statistics.chartError = null;
            })
            .addCase(fetchUserAttemptStatistics.fulfilled, (state, action) => {
                state.statistics.loadingChart = false;
                if (action.payload) {
                    state.statistics.chartData = action.payload.data;
                }
            })
            .addCase(fetchUserAttemptStatistics.rejected, (state, action) => {
                state.statistics.loadingChart = false;
                state.statistics.chartError = action.error.message;
            })
            .addCase(fetchUserOverallStatistics.pending, (state) => {
                state.statistics.loadingOverview = true;
                state.statistics.overviewError = null;
            })
            .addCase(fetchUserOverallStatistics.fulfilled, (state, action) => {
                state.statistics.loadingOverview = false;
                if (action.payload) {
                    state.statistics.overviewData = action.payload.data;
                }
            })
            .addCase(fetchUserOverallStatistics.rejected, (state, action) => {
                state.statistics.loadingOverview = false;
                state.statistics.overviewError = action.error.message;
            });
    },
});

export const { setAttempts, setUserAttemptsPage, setUserAttemptsLimit, resetUserAttemptsPagination } = attemptSlice.actions;
export default attemptSlice.reducer;
