import UserLayout from "../../../layouts/UserLayout"
import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { fetchClassesOverview } from "../../../features/class/classSlice"
import { getLearningItemWeekend } from "../../../features/learningItem/learningItemSlice"
import { useNavigate } from "react-router-dom"
import ModernSchedule from "../../../components/ModernSchedule"
import { formatDate } from "../../../utils/formatters"
import { FileText, User, Phone, Calendar, GraduationCap, BadgeInfo, BookOpenCheck, Play, PenTool } from "lucide-react"
import LoadingData from "src/components/loading/LoadingData"
import { fetchRecentLessonsByUser } from "src/features/lesson"
import { setOpenStudentCardModal } from "src/features/auth/authSlice"
import { resetClassDetail } from "../../../features/class/classSlice"
import AttemptStatisticsChart from "../../../components/chart/AttemptStatisticsChart"

const AvatarUser = () => {
    const { user } = useSelector(state => state.auth);

    return (
        <div className="aspect-square md:w-full w-24 rounded-full overflow-hidden border border-gray-300 flex items-center justify-center">
            {user?.avatarUrl ? (
                <img
                    src={user.avatarUrl}
                    alt="avatar"
                    className="w-full h-full object-cover"
                />
            ) : (
                <svg className="w-full h-full text-gray-400" viewBox="0 0 40 40" fill="none">
                    <path
                        d="M20 2.5C10.335 2.5 2.5 10.335 2.5 20C2.5 29.665 10.335 37.5 20 37.5C29.665 37.5 37.5 29.665 37.5 20C37.5 10.335 29.665 2.5 20 2.5ZM20 22.5C16.6983 22.5 14.1667 19.88 14.1667 16.6667C14.1667 13.4533 16.6983 10.8333 20 10.8333C23.3017 10.8333 25.8333 13.4533 25.8333 16.6667C25.8333 19.88 23.3017 22.5 20 22.5ZM10 32.3C10.1 30.38 10.8 29.03 11.73 28.07C12.72 27.05 14 26.41 15.2 26.03C15.41 25.96 15.73 26.01 16.09 26.26C16.88 26.8 18.25 27.5 20 27.5C21.75 27.5 23.12 26.8 23.91 26.26C24.27 26.01 24.59 25.96 24.8 26.03C26 26.41 27.28 27.05 28.27 28.07C29.2 29.03 29.9 30.39 30 32.29C27.18 34.59 23.65 35.84 20 35.83C16.35 35.84 12.82 34.58 10 32.3Z"
                        fill="#94A3B8"
                    />
                </svg>
            )}
        </div>
    );
};

const InfoRow = ({ icon, label, value }) => (
    <div className="flex items-center gap-2 text-xs">
        <span className="text-gray-600">{icon}</span>
        <span className="w-28 font-medium text-gray-900">{label}:</span>
        <span className="text-gray-700">{value}</span>
    </div>
);
const InformationUser = () => {
    const { user } = useSelector(state => state.auth);
    const dispatch = useDispatch();
    if (!user) return null;

    return (
        <div className="flex flex-col items-start gap-3 p-4 sticky top-[6rem]">
            <div className="flex flex-row md:flex-col items-center gap-3">
                <AvatarUser />
                <div className="text-center flex md:justify-between flex-row w-full md:mt-3 gap-2">
                    <div className="flex flex-col gap-1">
                        <p className="text-lg font-semibold text-gray-900 whitespace-nowrap overflow-hidden text-ellipsis">
                            {user.lastName} {user.firstName}
                        </p>
                        <p className="text-sm text-gray-500 hidden sm:block">@{user.username}</p>
                    </div>
                    <p className="text-sm sm:flex text-gray-500 hidden items-center justify-center gap-1">
                        <BadgeInfo size={16} className="text-gray-500 " /> {user.id}
                    </p>
                </div>
            </div>
            <hr className="w-full border-gray-200 my-3" />

            <div className="w-full flex flex-col gap-3 text-sm text-gray-700">
                <div className="sm:hidden flex">
                    <InfoRow icon={<BadgeInfo size={16} />} label="ID" value={user.id} />
                </div>
                <InfoRow icon={<User size={16} />} label="Giới tính" value={user.gender ? "Nam" : "Nữ"} />
                <InfoRow icon={<Calendar size={16} />} label="Ngày sinh" value={formatDate(user.birthDate)} />
                <InfoRow icon={<GraduationCap size={16} />} label="Lớp" value={user.class || "Chưa cập nhật"} />
                <InfoRow icon={<Phone size={16} />} label="Số điện thoại" value={user.phone || "Chưa cập nhật"} />
            </div>
            <hr className="w-full border-gray-200 my-3" />
            <button
                onClick={() => dispatch(setOpenStudentCardModal(true))}
                className="w-full text-sm p-1.5 rounded-md bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 transition-colors duration-200 flex items-center justify-center"
            >
                Chỉnh sửa thông tin cá nhân
            </button>
        </div>
    );
};

const ClassCard = ({ cls, onClick }) => {
    return (
        <div
            onClick={onClick}
            className="flex flex-col md:flex-row justify-center items-start md:items-center p-3 lg:p-4 gap-3 lg:gap-4 bg-white border border-gray-200 rounded-md hover:shadow-md hover:border-cyan-300 cursor-pointer transition-all duration-200 group"
        >
            {/* Class Content */}
            <div className="flex-1 flex flex-col w-full md:w-auto">
                {/* Class Name and Code */}
                <div className="flex flex-row w-full justify-between">
                    <h3 className="text-zinc-900 font-semibold font-bevietnam text-sm truncate group-hover:text-cyan-700 transition-colors">
                        {cls.class.name}
                    </h3>
                    <div className={`h-fit flex-shrink-0 p-1 border border-gray-200 rounded-full text-[0.6rem] text-gray-500 ${cls.class.status === 'LHD' ? 'border-green-300 text-green-600' : 'border-red-300 text-red-600'}`}>
                        {cls.class.status === 'LHD' ? 'Hoạt động' : 'Chưa hoạt động'}
                    </div>
                </div>
                <div className="flex flex-row gap-2 mt-4 flex-wrap">
                    <p className="text-xs text-gray-500 ">Mã lớp: {cls.class.class_code}</p>
                    <p className="text-xs text-gray-500 ">Khối: {cls.class.grade}</p>
                    <p className="text-xs text-gray-500 ">Năm học: {cls.class.academicYear}</p>
                </div>
            </div>
        </div>
    )
}

const ClassContainer = () => {
    const { classesOverview: classes, loadingClass } = useSelector(state => state.classes);
    const navigate = useNavigate()

    return (
        <div className="flex flex-col gap-3">
            <p className=" text-gray-900 font-semibold">Lớp học của bạn</p>
            <LoadingData
                loading={loadingClass}
                isNoData={classes.length > 0 ? false : true}
                loadText="Đang tải danh sách lớp học"
                noDataText="Không có lớp học nào."
                IconNoData={GraduationCap}
            >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {classes?.map((cls, index) => (
                        <ClassCard
                            key={cls._id}
                            cls={cls}
                            onClick={() => navigate(`/class/${cls.class.class_code}`)}
                            index={index}
                            startIndex={0}
                        />
                    ))}
                </div>
            </LoadingData>

        </div>
    )
}

const CalendarContainer = () => {
    const { classesOverview: classes, loadingClass } = useSelector(state => state.classes);
    const { learningItemsWeekend } = useSelector(state => state.learningItems);
    return (
        <div className="flex flex-col gap-3">
            <p className="text-gray-900 font-semibold">Lịch học của bạn</p>
            <LoadingData
                loading={loadingClass}
                isNoData={classes.length > 0 ? false : true}
                loadText="Đang tải lịch học"
                noDataText="Không có lịch học nào"
                IconNoData={Calendar}
            >
                <ModernSchedule classes={classes?.map((cls) => cls.class)} learningItems={learningItemsWeekend} />
            </LoadingData>
        </div>
    )
}

const getIconLearningItem = (type) => {
    switch (type) {
        case 'VID': return <Play size={14} className="text-red-500" />;
        case 'DOC': return <FileText size={14} className="text-blue-500" />;
        case 'BTVN': return <PenTool size={14} className="text-green-500" />;
        default: return <FileText size={14} className="text-gray-500" />;
    }
};

const LessonCard = ({ lessons, day }) => {
    const navigate = useNavigate()
    return (
        <div
            className="flex flex-col text-sm gap-2"
        >
            <div className="flex flex-row gap-3 items-center">
                <p className="text-gray-500 flex-shrink-0">{formatDate(day)}</p>
                <div className="w-full bg-gray-200 h-[1px] " />
            </div>
            <div className="flex flex-col">
                {lessons.map((lesson) => {
                    return (
                        <div className="relative w-full">
                            {/* Icon nằm trên border */}
                            <div className="absolute -left-[-10px] top-4 bg-white py-1">
                                <BookOpenCheck size={16} className="text-cyan-600" />
                            </div>

                            {/* Nội dung bài tập */}
                            <div className="flex flex-col gap-3 w-full items-start ml-4 border-l-2 border-gray-200 p-4">
                                <p className="text-gray-900 flex-shrink-0 hover:underline cursor-pointer hover:text-cyan-600" onClick={() => navigate(`/class/${lesson.class.class_code}/learning/lesson/${lesson.id}`)}>{lesson.name}</p>
                                <div className="flex flex-col gap-2 w-full ml-4">
                                    {lesson.learningItems?.map((item) => {
                                        return (
                                            <div
                                                key={item.id}
                                                className="flex md:flex-row flex-col w-full md:items-center cursor-pointer md:justify-between gap-2 px-2 py-1 rounded hover:bg-gray-50 transition"
                                                onClick={() => navigate(`/class/${lesson.class.class_code}/learning/lesson/${lesson.id}/learning-item/${item.id}`)}
                                            >
                                                {/* Bên trái: icon + tên */}
                                                <div className="flex items-center gap-2">
                                                    {getIconLearningItem(item.typeOfLearningItem)}
                                                    <span className="text-sm text-gray-800">{item.name}</span>
                                                </div>

                                                {/* Bên phải: trạng thái */}
                                                <div className="flex items-center gap-2 text-xs md:text-sm">
                                                    {item.studyStatuses?.[0]?.isDone ? (
                                                        <>
                                                            <span className="text-green-600 font-medium">Hoàn thành</span>
                                                            <span className="text-gray-500">{formatDate(item.studyStatuses?.[0]?.studyTime)}</span>
                                                        </>
                                                    ) : (
                                                        <span className="text-gray-400 italic">Chưa hoàn thành</span>
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                    )
                })
                }
            </div>
        </div>
    );
};

const ExerciseContainer = () => {
    const { recentLessons } = useSelector(state => state.lessons);
    const { list, loading } = recentLessons;

    const [visibleDayCount, setVisibleDayCount] = useState(2);

    // Group lessons by day
    const groupLessonsByDay = (lessons) => {
        return lessons.reduce((acc, lesson) => {
            const day = lesson.day;
            if (!acc[day]) acc[day] = [];
            acc[day].push(lesson);
            return acc;
        }, {});
    };

    const groupedLessons = groupLessonsByDay(list);
    const groupedEntries = Object.entries(groupedLessons); // [[day, [lesson]], ...]

    // Giới hạn số ngày hiển thị
    const visibleEntries = groupedEntries.slice(0, visibleDayCount);

    const handleShowMore = () => {
        setVisibleDayCount(prev => prev + 2);
    };

    return (
        <div className="flex flex-col gap-3">
            <p className="text-gray-900 font-semibold">Bài tập mới nhất</p>
            <LoadingData
                loading={loading}
                isNoData={list.length === 0}
                loadText="Đang tải danh sách bài tập"
                noDataText="Không có bài tập nào."
                IconNoData={BookOpenCheck}
            >
                <div className="flex flex-col gap-6 p-2">
                    {visibleEntries.map(([day, lessons]) => (
                        <LessonCard
                            key={day}
                            lessons={lessons}
                            day={day}
                        />
                    ))}
                </div>

                {groupedEntries.length > visibleDayCount && (
                    <button
                        onClick={handleShowMore}
                        className="mt-3 self-center text-sm text-cyan-600 hover:underline p-1.5 border border-gray-200 hover:bg-gray-50 rounded-md transition-colors w-full"
                    >
                        Xem thêm
                    </button>
                )}
            </LoadingData>
        </div>
    );
};

const OverViewPage = () => {
    const dispatch = useDispatch()
    const { classDetail } = useSelector(state => state.classes)
    useEffect(() => {
        if (classDetail) dispatch(resetClassDetail())
        dispatch(fetchClassesOverview())
        dispatch(getLearningItemWeekend({ startOfWeek: null, endOfWeek: null }))
        dispatch(fetchRecentLessonsByUser());
    }, [dispatch])
    return (
        <UserLayout>
            <div className="container flex md:flex-row flex-col">
                <div className="md:w-1/4 w-full flex flex-col justify-start ">
                    <InformationUser />
                </div>
                <div className="flex-1 flex flex-col justify-start p-4 gap-3">
                    <ClassContainer />
                    <hr className="w-full border-gray-200 my-3" />
                    <AttemptStatisticsChart />
                    <hr className="w-full border-gray-200 my-3" />
                    <CalendarContainer />
                    <hr className="w-full border-gray-200 my-3" />
                    <ExerciseContainer />
                </div>
            </div>
        </UserLayout>
    )
}

export default OverViewPage