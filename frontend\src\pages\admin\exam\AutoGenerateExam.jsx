import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import AdminSidebar from "src/components/sidebar/AdminSidebar";
import { fetchCodesByType } from "src/features/code/codeSlice";
import LoadingSpinner from "src/components/loading/LoadingSpinner";
import LatexRenderer from "src/components/latex/RenderLatex";
import NoTranslate from "src/components/utils/NoTranslate";
import { getDescription } from "src/utils/codeUtils";
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragOverlay,
    DragEndEvent,
    DragOverEvent,
    useDraggable,
    useDroppable,
} from '@dnd-kit/core';
import {
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
    arrayMove,
    useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
    ArrowLeft,
    Save,
    Eye,
    ChevronRight,
    ChevronLeft,
    Clock,
    Users,
    BookOpen,
    RefreshCw,
    CheckCircle,
    Zap,
    GripVertical,
    X,
    Search,
    Filter,
    FileText,
    Lightbulb,
    ExternalLink,
    Download
} from "lucide-react";
import { setErrorMessage } from "src/features/state/stateApiSlice";
import { downloadQuestionsDocxByIds } from "src/features/exam/examSlice";
// API service for auto generation
import { autoGenerateQuestions, getQuestionSuggestions, getRelatedQuestions } from "src/services/questionApi";
import { createExam } from "src/features/exam/examSlice";
import OutsideClickWrapper from "src/components/common/OutsideClickWrapper";
// Related Questions Modal Component
const RelatedQuestionsModal = ({ isOpen, onClose, question, codes, onReplaceQuestion }) => {
    const [relatedQuestions, setRelatedQuestions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.'];
    const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)'];
    // Fetch related questions when modal opens
    useEffect(() => {
        if (isOpen && question) {
            fetchRelatedQuestions();
        }
    }, [isOpen, question]);

    const fetchRelatedQuestions = async () => {
        setLoading(true);
        setError(null);

        try {
            const response = await getRelatedQuestions(question.id, 10);
            if (response.success) {
                setRelatedQuestions(response.data.data.relatedQuestions || []);
            } else {
                setError(response.message || 'Không thể tải câu hỏi liên quan');
            }
        } catch (err) {
            setError('Lỗi khi tải câu hỏi liên quan');
            console.error('Error fetching related questions:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleQuestionSelect = (selectedQuestion) => {
        if (onReplaceQuestion) {
            onReplaceQuestion(question, selectedQuestion);
            onClose();
        }
    };

    const handleBackdropClick = (e) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    if (!isOpen) return null;

    return (
        <div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={handleBackdropClick}
        >
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                {/* Header */}
                <div className="p-4 border-b border-gray-200 bg-gray-50">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Lightbulb className="w-5 h-5 text-blue-600" />
                            <h3 className="text-lg font-semibold text-gray-900">
                                Câu hỏi liên quan
                            </h3>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-1 hover:bg-gray-200 rounded transition-colors"
                        >
                            <X className="w-5 h-5 text-gray-500" />
                        </button>
                    </div>

                    {/* Original question info */}
                    {question && (
                        <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                            <p className="text-sm text-gray-600 mb-1">Câu hỏi gốc:</p>
                            <div className="flex items-center gap-2 mb-2">
                                <span className="bg-gray-800 text-white px-2 py-1 rounded text-xs">
                                    ID: {question.id}
                                </span>
                                <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                    {question.typeOfQuestion}
                                </span>
                                <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs">
                                    {getDescription('chapter', question.chapter, codes)}
                                </span>
                            </div>
                            <LatexRenderer
                                text={question.content}
                                className="text-sm text-gray-800 truncate"
                            />
                        </div>
                    )}
                </div>

                {/* Content */}
                <div className="p-4 overflow-y-auto max-h-[70vh]">
                    {loading ? (
                        <div className="flex items-center justify-center py-8">
                            <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
                            <span className="ml-2 text-gray-600">Đang tải câu hỏi liên quan...</span>
                        </div>
                    ) : error ? (
                        <div className="text-center py-8">
                            <div className="text-red-500 mb-2">{error}</div>
                            <button
                                onClick={fetchRelatedQuestions}
                                className="text-blue-600 hover:text-blue-800 text-sm"
                            >
                                Thử lại
                            </button>
                        </div>
                    ) : relatedQuestions.length === 0 ? (
                        <div className="text-center py-8">
                            <Search className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-gray-500">Không tìm thấy câu hỏi liên quan</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="text-sm text-gray-600 mb-3">
                                Tìm thấy {relatedQuestions.length} câu hỏi liên quan. Click để thay thế.
                            </div>

                            {relatedQuestions.map((relatedQ, index) => (
                                <div
                                    key={relatedQ.id}
                                    onClick={() => handleQuestionSelect(relatedQ)}
                                    className="border rounded-lg p-3 hover:shadow-md transition-shadow cursor-pointer hover:border-blue-300"
                                >
                                    <div className="flex items-start gap-3">
                                        {/* Question metadata */}
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-2">
                                                <span className="bg-gray-800 text-white px-2 py-1 rounded text-xs">
                                                    ID: {relatedQ.id}
                                                </span>
                                                <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                                    {relatedQ.typeOfQuestion}
                                                </span>
                                                <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs">
                                                    {getDescription('chapter', relatedQ.chapter, codes)}
                                                </span>
                                                <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded text-xs">
                                                    {getDescription('difficulty', relatedQ.difficulty, codes)}
                                                </span>

                                                {/* Relevance score */}
                                                {relatedQ.relevanceScore && (
                                                    <span className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">
                                                        Điểm: {relatedQ.relevanceScore}
                                                    </span>
                                                )}
                                            </div>

                                            {/* Relevance factors */}
                                            {relatedQ.relevanceFactors && relatedQ.relevanceFactors.length > 0 && (
                                                <div className="flex flex-wrap gap-1 mb-2">
                                                    {relatedQ.relevanceFactors.map((factor, factorIndex) => (
                                                        <span
                                                            key={factorIndex}
                                                            className="bg-yellow-100 text-yellow-700 px-1 py-0.5 rounded text-xs"
                                                        >
                                                            {factor}
                                                        </span>
                                                    ))}
                                                </div>
                                            )}

                                            {/* Question content */}
                                            <div className="mb-3">
                                                <LatexRenderer
                                                    text={relatedQ.content}
                                                    className="text-sm text-gray-800"
                                                />
                                            </div>

                                            {/* Question Image */}
                                            {relatedQ.imageUrl && (
                                                <div className="mb-3">
                                                    <img
                                                        src={relatedQ.imageUrl}
                                                        alt="question"
                                                        className="max-w-full h-auto max-h-24 object-contain border rounded"
                                                    />
                                                </div>
                                            )}

                                            {/* Quick preview of answers */}
                                            {relatedQ.statements && relatedQ.statements.length > 0 && relatedQ.statements.map(
                                                (statement, stmtIndex) => (
                                                    <div key={statement.id || stmtIndex} className="flex items-start gap-2 mb-1">
                                                        <span className="font-medium text-xs">
                                                            {relatedQ.typeOfQuestion === 'TN' ? prefixStatementTN[stmtIndex] : prefixStatementDS[stmtIndex]}
                                                        </span>
                                                        <LatexRenderer
                                                            text={statement.content}
                                                            className="text-xs"
                                                        />
                                                    </div>
                                                )
                                            )}

                                            {relatedQ.typeOfQuestion === 'TLN' && relatedQ.correctAnswer && (
                                                <div className="text-xs text-green-600">
                                                    <span className="font-medium">Đáp án: </span>
                                                    <LatexRenderer text={relatedQ.correctAnswer} className="text-xs" />
                                                </div>
                                            )}
                                        </div>

                                        {/* Replace button indicator */}
                                        <div className="flex-shrink-0">
                                            <ExternalLink className="w-4 h-4 text-blue-500" />
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

// Sortable Question Item Component
const SortableQuestionItem = ({ question, index, type, codes, isSelected, onSelect, isOver, onShowRelated }) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({
        id: question.id,
        data: {
            type: 'question',
            question: question,
            questionType: type
        }
    });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    };

    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.'];
    const prefixStatementDS = ['a)', 'b)', 'c)', 'd)', 'e)', 'f)'];

    return (
        <div
            ref={setNodeRef}
            style={style}
            className={`border rounded-lg p-4 bg-white cursor-pointer transition-all ${isDragging ? 'shadow-lg' : ''
                } ${isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300'
                } ${isOver ? 'ring-2 ring-green-300 border-green-400 bg-green-50' : ''
                }`}
            onClick={() => onSelect && onSelect(question)}
        >
            <div className="flex items-start gap-3">
                {/* Drag Handle */}
                <div
                    {...attributes}
                    {...listeners}
                    className="cursor-grab active:cursor-grabbing p-1 text-gray-400 hover:text-gray-600"
                >
                    <GripVertical className="w-4 h-4" />
                </div>

                {/* Question Number */}
                <div className="bg-blue-100 text-blue-800 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                    {index + 1}
                </div>

                {/* Question Content */}
                <div className="flex-1 relative">
                    {/* Related Questions Button */}
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            onShowRelated && onShowRelated(question);
                        }}
                        className="absolute top-0 right-0 p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors z-10"
                        title="Xem câu hỏi liên quan"
                    >
                        <Lightbulb className="w-4 h-4" />
                    </button>

                    <div className="flex items-center gap-2 mb-2">
                        {/* Question ID */}
                        <span className="bg-gray-800 text-white px-2 py-1 rounded text-xs font-mono">
                            ID: {question.id}
                        </span>
                        <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs font-medium">
                            {question.typeOfQuestion}
                        </span>
                        <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs">
                            {getDescription('chapter', question.chapter, codes)}
                        </span>
                        <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded text-xs">
                            {getDescription('difficulty', question.difficulty, codes)}
                        </span>
                    </div>

                    {/* Question Content */}
                    <div className="mb-3">
                        <LatexRenderer
                            text={question.content}
                            className="text-sm text-gray-800"
                        />
                    </div>

                    {/* Question Image */}
                    {question.imageUrl && (
                        <div className="mb-3">
                            <img
                                src={question.imageUrl}
                                alt="question"
                                className="max-w-full h-auto max-h-32 object-contain border rounded"
                            />
                        </div>
                    )}

                    {/* Statements with Correct Answer Highlighting */}
                    {question.statements && question.statements.length > 0 && (
                        <div className="mt-3">
                            {question.typeOfQuestion === 'TN' && (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    {question.statements.map((statement, stmtIndex) => {
                                        const isCorrect = statement.isCorrect;
                                        return (
                                            <div
                                                key={statement.id || stmtIndex}
                                                className={`flex items-start gap-2 p-2 rounded ${isCorrect ? 'bg-green-50 border border-green-200' : ''
                                                    }`}
                                            >
                                                <span className={`text-sm font-medium flex-shrink-0 ${isCorrect ? 'text-green-800 font-bold' : 'text-gray-700'
                                                    }`}>
                                                    <NoTranslate>{prefixStatementTN[stmtIndex]}</NoTranslate>
                                                </span>
                                                <div className="flex-1">
                                                    <LatexRenderer
                                                        text={statement.content}
                                                        className={`text-sm ${isCorrect ? 'text-green-800 font-medium' : 'text-gray-700'
                                                            }`}
                                                    />
                                                    {statement.imageUrl && (
                                                        <img
                                                            src={statement.imageUrl}
                                                            alt="statement"
                                                            className="mt-1 max-w-full h-auto max-h-16 object-contain border rounded"
                                                        />
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            )}

                            {question.typeOfQuestion === 'DS' && (
                                <div className="space-y-2">
                                    {question.statements.map((statement, stmtIndex) => {
                                        const isCorrect = statement.isCorrect;
                                        return (
                                            <div
                                                key={statement.id || stmtIndex}
                                                className={`flex items-start gap-2 p-2 rounded ${isCorrect === true ? 'bg-green-50 border border-green-200' :
                                                    isCorrect === false ? 'bg-red-50 border border-red-200' : ''
                                                    }`}
                                            >
                                                <span className={`text-sm font-medium flex-shrink-0 ${isCorrect === true ? 'text-green-800 font-bold' :
                                                    isCorrect === false ? 'text-red-800 font-bold' : 'text-gray-700'
                                                    }`}>
                                                    <NoTranslate>{prefixStatementDS[stmtIndex]}</NoTranslate>
                                                </span>
                                                <div className="flex-1">
                                                    <LatexRenderer
                                                        text={statement.content}
                                                        className={`text-sm ${isCorrect === true ? 'text-green-800 font-medium' :
                                                            isCorrect === false ? 'text-red-800 font-medium' : 'text-gray-700'
                                                            }`}
                                                    />
                                                    {statement.imageUrl && (
                                                        <img
                                                            src={statement.imageUrl}
                                                            alt="statement"
                                                            className="mt-1 max-w-full h-auto max-h-16 object-contain border rounded"
                                                        />
                                                    )}
                                                    {/* Show correct/incorrect label for DS */}
                                                    {isCorrect !== undefined && (
                                                        <span className={`inline-block mt-1 px-2 py-0.5 rounded text-xs font-medium ${isCorrect === true ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                                            }`}>
                                                            {isCorrect === true ? 'Đúng' : 'Sai'}
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            )}
                        </div>
                    )}

                    {/* Correct Answer for TLN */}
                    {question.typeOfQuestion === 'TLN' && question.correctAnswer && (
                        <div className="mt-3 p-2 bg-green-50 rounded">
                            <span className="text-sm font-medium text-green-800">Đáp án: </span>
                            <LatexRenderer
                                text={question.correctAnswer}
                                className="text-sm text-green-700"
                            />
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

// Question Section Header Component
const QuestionSectionHeader = ({ title, count, type }) => {
    const typeColors = {
        TN: 'text-blue-600',
        DS: 'text-green-600',
        TLN: 'text-orange-600'
    };

    return (
        <div className="flex items-center gap-2 mb-4">
            <FileText className={`w-4 h-4 ${typeColors[type]}`} />
            <h4 className="text-sm font-semibold text-gray-900">
                {title} ({count} câu)
            </h4>
        </div>
    );
};

// TN Questions Component
const TNQuestionsSection = ({ questions, onReorder, codes, selectedQuestion, onQuestionSelect, overQuestion, onShowRelated }) => {
    if (!questions || questions.length === 0) {
        return null;
    }

    return (
        <div className="space-y-3">
            <SortableContext
                items={questions?.map(q => q.id) || []}
                strategy={verticalListSortingStrategy}
            >
                {questions?.map((question, index) => (
                    <SortableQuestionItem
                        key={question.id}
                        question={question}
                        index={index}
                        type="TN"
                        codes={codes}
                        isSelected={selectedQuestion?.id === question.id}
                        onSelect={onQuestionSelect}
                        isOver={overQuestion?.id === question.id}
                        onShowRelated={onShowRelated}
                    />
                ))}
            </SortableContext>
        </div>
    );
};

// DS Questions Component
const DSQuestionsSection = ({ questions, onReorder, codes, selectedQuestion, onQuestionSelect, overQuestion, onShowRelated }) => {
    if (!questions || questions.length === 0) {
        return null;
    }

    return (
        <div className="space-y-3">
            <SortableContext
                items={questions?.map(q => q.id) || []}
                strategy={verticalListSortingStrategy}
            >
                {questions?.map((question, index) => (
                    <SortableQuestionItem
                        key={question.id}
                        question={question}
                        index={index}
                        type="DS"
                        codes={codes}
                        isSelected={selectedQuestion?.id === question.id}
                        onSelect={onQuestionSelect}
                        isOver={overQuestion?.id === question.id}
                        onShowRelated={onShowRelated}
                    />
                ))}
            </SortableContext>
        </div>
    );
};

// TLN Questions Component
const TLNQuestionsSection = ({ questions, onReorder, codes, selectedQuestion, onQuestionSelect, overQuestion, onShowRelated }) => {
    if (!questions || questions.length === 0) {
        return null;
    }

    return (
        <div className="space-y-3">
            <SortableContext
                items={questions?.map(q => q.id) || []}
                strategy={verticalListSortingStrategy}
            >
                {questions?.map((question, index) => (
                    <SortableQuestionItem
                        key={question.id}
                        question={question}
                        index={index}
                        type="TLN"
                        codes={codes}
                        isSelected={selectedQuestion?.id === question.id}
                        onSelect={onQuestionSelect}
                        isOver={overQuestion?.id === question.id}
                        onShowRelated={onShowRelated}
                    />
                ))}
            </SortableContext>
        </div>
    );
};

// Question Suggestion Panel Component
const QuestionSuggestionPanel = ({ selectedQuestion, generatedQuestions, codes, onReplaceQuestion, examClass }) => {
    const [suggestions, setSuggestions] = useState([]); // Remove mock data
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [hasMore, setHasMore] = useState(false);
    const [totalCount, setTotalCount] = useState(0);
    const [searchInput, setSearchInput] = useState(''); // Separate state for input
    const [filters, setFilters] = useState({
        chapter: '', // Set default filter to show suggestions
        classLevel: '',
        difficulty: '',
        typeOfQuestion: '',
        search: '',
        page: 1,
        limit: 10
    });

    // Debounce search input
    const debouncedSearch = useDebounce(searchInput, 500); // 500ms delay

    // Get current question IDs to exclude from suggestions
    const currentQuestionIds = useMemo(() => {
        return generatedQuestions.map(q => q.id);
    }, [generatedQuestions]);

    // Filter chapters based on exam class
    const filteredChaptersForSuggestion = useMemo(() => {
        if (!Array.isArray(codes?.chapter)) return [];
        if (examClass && examClass.trim() !== "") {
            return codes.chapter.filter(chapter => chapter.code.startsWith(examClass));
        } else {
            return codes.chapter;
        }
    }, [codes?.chapter, examClass]);

    // Update search filter when debounced search changes
    useEffect(() => {
        setFilters(prev => ({
            ...prev,
            search: debouncedSearch,
            page: 1
        }));
    }, [debouncedSearch]);

    // Fetch suggestions when filters change (not when selectedQuestion changes)
    useEffect(() => {
        if (selectedQuestion && (filters.chapter || filters.classLevel || filters.difficulty || filters.typeOfQuestion || filters.search)) {
            fetchSuggestions();
        }
    }, [filters.chapter, filters.classLevel, filters.difficulty, filters.typeOfQuestion, filters.search]);

    // Reset chapter filter when exam class changes
    useEffect(() => {
        if (filters.chapter) {
            // Check if current selected chapter is still valid for the new class
            const isChapterValid = filteredChaptersForSuggestion.some(ch => ch.code === filters.chapter);
            if (!isChapterValid) {
                setFilters(prev => ({
                    ...prev,
                    chapter: ''
                }));
            }
        }
    }, [examClass, filteredChaptersForSuggestion, filters.chapter]);

    const fetchSuggestions = async (loadMore = false) => {
        if (!selectedQuestion) return;

        if (loadMore) {
            setLoadingMore(true);
        } else {
            setLoading(true);
        }

        try {
            const requestData = {
                questionIds: currentQuestionIds,
                chapter: filters.chapter,
                classLevel: filters.classLevel,
                difficulty: filters.difficulty,
                typeOfQuestion: filters.typeOfQuestion,
                search: filters.search,
                page: loadMore ? filters.page : 1,
                limit: filters.limit
            };

            const response = await getQuestionSuggestions(requestData);
            if (response.success) {
                const newSuggestions = response.data.data.suggestions || [];
                const total = response.data.data.pagination.totalItems || 0;
                const hasNextPage = response.data.data.pagination.hasNextPage || false;

                if (loadMore) {
                    setSuggestions(prev => [...prev, ...newSuggestions]);
                } else {
                    setSuggestions(newSuggestions);
                }

                setTotalCount(total);
                setHasMore(hasNextPage);
            } else {
                console.error('Error fetching suggestions:', response.message);
                if (!loadMore) setSuggestions([]);
            }
        } catch (error) {
            console.error('Error fetching suggestions:', error);
            if (!loadMore) setSuggestions([]);
        } finally {
            if (loadMore) {
                setLoadingMore(false);
            } else {
                setLoading(false);
            }
        }
    };

    const fetchSuggestionsWithPage = async (page, loadMore = false) => {
        if (!selectedQuestion) return;

        if (loadMore) {
            setLoadingMore(true);
        } else {
            setLoading(true);
        }

        try {
            const requestData = {
                questionIds: currentQuestionIds,
                chapter: filters.chapter,
                classLevel: filters.classLevel,
                difficulty: filters.difficulty,
                typeOfQuestion: filters.typeOfQuestion,
                search: filters.search,
                page: page,
                limit: filters.limit
            };

            const response = await getQuestionSuggestions(requestData);
            if (response.success) {
                const newSuggestions = response.data.data.suggestions || [];
                const total = response.data.data.pagination.totalItems || 0;
                const hasNextPage = response.data.data.pagination.hasNextPage || false;

                if (loadMore) {
                    setSuggestions(prev => [...prev, ...newSuggestions]);
                } else {
                    setSuggestions(newSuggestions);
                }

                setTotalCount(total);
                setHasMore(hasNextPage);
            } else {
                console.error('Error fetching suggestions:', response.message);
                if (!loadMore) setSuggestions([]);
            }
        } catch (error) {
            console.error('Error fetching suggestions:', error);
            if (!loadMore) setSuggestions([]);
        } finally {
            if (loadMore) {
                setLoadingMore(false);
            } else {
                setLoading(false);
            }
        }
    };

    const handleFilterChange = (field, value) => {
        setFilters(prev => ({
            ...prev,
            [field]: value,
            page: 1 // Always reset page to 1 when filters change
        }));
    };

    const handleReplaceQuestion = (newQuestion) => {
        if (onReplaceQuestion) {
            onReplaceQuestion(selectedQuestion, newQuestion);
        }
    };

    const handleLoadMore = () => {
        if (!loadingMore && hasMore) {
            const nextPage = filters.page + 1;
            setFilters(prev => ({
                ...prev,
                page: nextPage
            }));

            // Call fetchSuggestions with loadMore = true and the next page
            fetchSuggestionsWithPage(nextPage, true);
        }
    };

    // Check if filters are set to show suggestions
    const hasActiveFilters = filters.chapter || filters.classLevel || filters.difficulty || filters.typeOfQuestion || filters.search || searchInput;

    return (
        <div className="h-full flex flex-col overflow-hidden">
            {/* Filters */}
            <div className=" p-4 border-b border-gray-200 bg-white space-y-3 flex-shrink-0">
                {/* Search */}
                <div>
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <input
                            type="text"
                            placeholder="Tìm kiếm câu hỏi..."
                            value={searchInput}
                            onChange={(e) => setSearchInput(e.target.value)}
                            className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        {/* Loading indicator when debouncing */}
                        {searchInput !== debouncedSearch && searchInput.length > 0 && (
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <RefreshCw className="w-4 h-4 animate-spin text-gray-400" />
                            </div>
                        )}
                    </div>
                </div>

                {/* Filter dropdowns */}
                <div className="grid grid-cols-2 gap-2">
                    {/* Chapter Filter */}
                    <select
                        value={filters.chapter}
                        onChange={(e) => handleFilterChange('chapter', e.target.value)}
                        className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                        <option value="">Chọn chương</option>
                        {filteredChaptersForSuggestion?.map((chapter) => (
                            <option key={chapter.code} value={chapter.code}>
                                {chapter.description}
                            </option>
                        ))}
                    </select>

                    {/* Difficulty Filter */}
                    <select
                        value={filters.difficulty}
                        onChange={(e) => handleFilterChange('difficulty', e.target.value)}
                        className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                        <option value="">Chọn độ khó</option>
                        {codes?.difficulty?.map((difficulty) => (
                            <option key={difficulty.code} value={difficulty.code}>
                                {difficulty.description}
                            </option>
                        ))}
                    </select>

                    {/* Class Level Filter */}
                    <select
                        value={filters.classLevel}
                        onChange={(e) => handleFilterChange('classLevel', e.target.value)}
                        className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                        <option value="">Chọn lớp</option>
                        {codes?.grade?.map((grade) => (
                            <option key={grade.code} value={grade.code}>
                                Lớp {grade.code}
                            </option>
                        ))}
                    </select>

                    {/* Type Filter */}
                    <select
                        value={filters.typeOfQuestion}
                        onChange={(e) => handleFilterChange('typeOfQuestion', e.target.value)}
                        className="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                        <option value="">Chọn loại câu hỏi</option>
                        <option value="TN">Trắc nghiệm</option>
                        <option value="DS">Đúng/Sai</option>
                        <option value="TLN">Trả lời ngắn</option>
                    </select>
                </div>

                {/* Reset filters button */}
                {hasActiveFilters && (
                    <button
                        onClick={() => {
                            setSearchInput('');
                            setFilters(prev => ({
                                ...prev,
                                chapter: '',
                                classLevel: '',
                                difficulty: '',
                                typeOfQuestion: '',
                                search: '',
                                page: 1
                            }));
                        }}
                        className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                    >
                        <X className="w-3 h-3" />
                        Xóa bộ lọc
                    </button>
                )}
            </div>

            {/* Suggestions List */}
            <div className="flex-1 overflow-y-auto">
                {!hasActiveFilters ? (
                    <div className="flex items-center justify-center p-8">
                        <div className="text-center">
                            <Filter className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-500 mb-1">Thiết lập bộ lọc để tìm câu hỏi</p>
                            <p className="text-xs text-gray-400">Chọn chương, độ khó, lớp hoặc loại câu hỏi</p>
                        </div>
                    </div>
                ) : loading ? (
                    <div className="flex items-center justify-center p-8">
                        <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
                        <span className="ml-2 text-sm text-gray-600">Đang tải...</span>
                    </div>
                ) : suggestions.length === 0 ? (
                    <div className="flex items-center justify-center p-8">
                        <div className="text-center">
                            <Search className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-500">Không tìm thấy câu hỏi phù hợp</p>
                        </div>
                    </div>
                ) : (
                    <>
                        <SuggestionDropZone suggestions={suggestions} codes={codes} />

                        {/* Load More Button */}
                        {hasMore && (
                            <div className="p-4 border-t border-gray-200 bg-white flex-shrink-0">
                                <button
                                    onClick={handleLoadMore}
                                    disabled={loadingMore}
                                    className="w-full py-2 px-4 text-sm bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-md border border-blue-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {loadingMore ? (
                                        <div className="flex items-center justify-center gap-2">
                                            <RefreshCw className="w-4 h-4 animate-spin" />
                                            Đang tải...
                                        </div>
                                    ) : (
                                        <div className="flex items-center justify-center gap-2">
                                            <span>Xem thêm</span>
                                            <span className="text-xs bg-blue-100 px-2 py-0.5 rounded">
                                                {suggestions.length}/{totalCount}
                                            </span>
                                        </div>
                                    )}
                                </button>
                                {/* Debug info */}
                                <div className="text-xs text-gray-400 mt-1">
                                    hasMore: {hasMore.toString()}, total: {totalCount}, current: {suggestions.length}
                                </div>
                            </div>
                        )}

                        {/* Show when no more data */}
                        {!hasMore && suggestions.length > 0 && (
                            <div className="p-2 text-center text-xs text-gray-500 border-t border-gray-200">
                                Đã hiển thị tất cả {totalCount} câu hỏi
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

// Draggable Suggestion Question Item Component
const DraggableSuggestionItem = ({ question, index, codes }) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        isDragging,
    } = useDraggable({
        id: `suggestion-${question.id}`,
        data: {
            type: 'suggestion',
            question: question
        }
    });

    const style = {
        transform: transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined,
        opacity: isDragging ? 0.5 : 1,
    };

    const prefixStatementTN = ['A.', 'B.', 'C.', 'D.', 'E.', 'F.'];

    return (
        <div
            ref={setNodeRef}
            style={style}
            {...attributes}
            {...listeners}
            className={`border rounded-lg p-3 bg-white hover:shadow-md transition-shadow cursor-grab active:cursor-grabbing ${isDragging ? 'shadow-lg z-50' : ''
                }`}
        >
            <div className="flex items-start gap-2">
                {/* Drag indicator */}
                <div className="text-gray-400 mt-1">
                    <GripVertical className="w-3 h-3" />
                </div>

                {/* Question metadata */}
                <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                        <span className="bg-gray-800 text-white px-2 py-0.5 rounded text-xs font-mono">
                            ID: {question.id}
                        </span>
                        <span className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded text-xs">
                            {question.typeOfQuestion}
                        </span>
                        <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded text-xs">
                            {getDescription('chapter', question.chapter, codes)}
                        </span>
                        <span className="bg-orange-100 text-orange-700 px-2 py-0.5 rounded text-xs">
                            {getDescription('difficulty', question.difficulty, codes)}
                        </span>
                    </div>

                    {/* Question content - full */}
                    <div className="mb-3">
                        <LatexRenderer
                            text={question.content}
                            className="text-xs text-gray-800"
                        />
                    </div>

                    {/* Question Image */}
                    {question.imageUrl && (
                        <div className="mb-3">
                            <img
                                src={question.imageUrl}
                                alt="question"
                                className="max-w-full h-auto max-h-20 object-contain border rounded"
                            />
                        </div>
                    )}

                    {/* Statements full for TN/DS */}
                    {question.statements && question.statements.length > 0 && (
                        <div className="text-xs mt-3">
                            {question.typeOfQuestion === 'TN' && (
                                <div className="space-y-1">
                                    {question.statements.map((statement, stmtIndex) => (
                                        <div key={statement.id || stmtIndex} className={`flex items-start gap-2 p-1 rounded ${statement.isCorrect ? 'bg-green-50 border border-green-200' : ''
                                            }`}>
                                            <span className={`font-medium flex-shrink-0 ${statement.isCorrect ? 'text-green-600 font-bold' : 'text-gray-600'
                                                }`}>
                                                {prefixStatementTN[stmtIndex]}
                                            </span>
                                            <div className="flex-1">
                                                <LatexRenderer
                                                    text={statement.content}
                                                    className={`text-xs ${statement.isCorrect ? 'text-green-600 font-medium' : 'text-gray-600'
                                                        }`}
                                                />
                                                {statement.imageUrl && (
                                                    <img
                                                        src={statement.imageUrl}
                                                        alt="statement"
                                                        className="mt-1 max-w-full h-auto max-h-12 object-contain border rounded"
                                                    />
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}

                            {question.typeOfQuestion === 'DS' && (
                                <div className="space-y-1">
                                    {question.statements.map((statement, stmtIndex) => (
                                        <div key={statement.id || stmtIndex} className={`flex items-start gap-2 p-1 rounded ${statement.isCorrect === true ? 'bg-green-50 border border-green-200' :
                                            statement.isCorrect === false ? 'bg-red-50 border border-red-200' : ''
                                            }`}>
                                            <span className={`font-medium flex-shrink-0 ${statement.isCorrect === true ? 'text-green-600 font-bold' :
                                                statement.isCorrect === false ? 'text-red-600 font-bold' : 'text-gray-600'
                                                }`}>
                                                {['a)', 'b)', 'c)', 'd)', 'e)', 'f)'][stmtIndex]}
                                            </span>
                                            <div className="flex-1">
                                                <LatexRenderer
                                                    text={statement.content}
                                                    className={`text-xs ${statement.isCorrect === true ? 'text-green-600 font-medium' :
                                                        statement.isCorrect === false ? 'text-red-600 font-medium' : 'text-gray-600'
                                                        }`}
                                                />
                                                {statement.imageUrl && (
                                                    <img
                                                        src={statement.imageUrl}
                                                        alt="statement"
                                                        className="mt-1 max-w-full h-auto max-h-12 object-contain border rounded"
                                                    />
                                                )}
                                                {/* Show correct/incorrect label for DS */}
                                                {statement.isCorrect !== undefined && (
                                                    <span className={`inline-block mt-1 px-1 py-0.5 rounded text-xs font-medium ${statement.isCorrect === true ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                                                        }`}>
                                                        {statement.isCorrect === true ? 'Đúng' : 'Sai'}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}

                    {/* Correct answer for TLN */}
                    {question.typeOfQuestion === 'TLN' && question.correctAnswer && (
                        <div className="text-xs text-green-600">
                            Đáp án: <LatexRenderer text={question.correctAnswer} className="text-xs" />
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

// Custom hook for debounce
const useDebounce = (value, delay) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);

    return debouncedValue;
};

// Suggestion Drop Zone Component - Safe area to drop suggestions back
const SuggestionDropZone = ({ suggestions, codes }) => {
    const {
        setNodeRef,
        isOver
    } = useDroppable({
        id: 'suggestion-panel',
        data: {
            type: 'suggestion-panel'
        }
    });

    return (
        <div
            ref={setNodeRef}
            className={`p-4 space-y-3 max-h-[55vh] transition-colors overflow-y-auto ${isOver ? 'bg-blue-50 border-blue-200' : ''
                }`}
        >
            {suggestions.map((suggestion, index) => (
                <DraggableSuggestionItem
                    key={suggestion.id}
                    question={suggestion}
                    index={index}
                    codes={codes}
                />
            ))}
        </div>
    );
};

const CustomCheckbox = ({ label, checked, onChange, required = false, disabled = false }) => {
    return (
        <div className="flex items-center space-x-2">
            <input
                type="checkbox"
                checked={checked}
                onChange={(e) => onChange(e.target.checked)}
                disabled={disabled}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                id={label.toLowerCase().replace(/\s+/g, '-') + '-checkbox'}
            />
            <label
                htmlFor={label.toLowerCase().replace(/\s+/g, '-') + '-checkbox'}
                className={`text-sm font-medium text-gray-700 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
                {label} {required && <span className="text-red-500">*</span>}
            </label>
        </div>
    );
};


const CustomInput = ({
    label,
    value,
    onChange,
    type = 'text',
    required = false,
    min,
    max,
    placeholder
}) => {

    const onChangeValue = (e) => {
        let newValue = e.target.value;

        if (type === 'number') {
            if (isNaN(newValue)) return;
            const numValue = Number(newValue);

            if (min !== undefined && numValue < min) {
                newValue = min;
            } else if (max !== undefined && numValue > max) {
                newValue = max;
            } else {
                newValue = numValue;
            }
        }
        onChange(newValue);
    };

    return (
        <div className="w-full">
            <label className="block text-sm font-medium text-gray-700 mb-2">
                {label} {required && <span className="text-red-500">*</span>}
            </label>
            <input
                type={'text'}
                value={value}
                onChange={onChangeValue}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                min={min}
                max={max}
                required={required}
                placeholder={placeholder}
            />
        </div>
    );
};


const CustomDropdown = ({ title, ignoreRef = '', options = [], value, handleSelect, required = false }) => {
    const [open, setOpen] = useState(false);

    const selected = options?.find(g => g.code === value);

    return (
        <div className={`relative w-full ${ignoreRef}`}>
            <label className="block text-sm font-medium text-gray-700 mb-2">
                {title} {required && <span className="text-red-500">*</span>}
            </label>

            {/* Nút hiển thị dropdown */}
            <button
                onClick={() => setOpen(!open)}
                className="truncate whitespace-nowrap overflow-hidden text-ellipsis
w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-left focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
                {selected?.description ? `${selected.description}` : `Chọn ${title}`}
            </button>

            {/* Danh sách lớp */}
            {open && (
                <OutsideClickWrapper
                    onClickOutside={() => setOpen(false)}
                    ignoreOutsideClick={ignoreRef}
                    className="absolute z-10 mt-2 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {options.map((option) => (
                        <p
                            key={option.code}
                            onClick={() => {
                                handleSelect(option.code)
                                setOpen(false);
                            }}
                            className="px-4 py-2 hover:bg-blue-100 cursor-pointer text-sm text-gray-700"
                        >
                            {option.description}
                        </p>
                    ))}
                </OutsideClickWrapper>
            )}
        </div>
    );
};

const ChapterDropdown = ({ codes, handleSelect, selectedChapters }) => {
    const [open, setOpen] = useState(false);
    const handleToggle = () => setOpen(!open);

    const handleCheckboxChange = (code) => {
        handleSelect(code);
    };

    return (
        <div className="relative w-full chapterDropdownRef">
            {/* Nút mở dropdown */}
            <label className="block text-sm font-medium text-gray-700 mb-2">
                Chọn chương (có thể chọn nhiều)
            </label>
            <button
                onClick={handleToggle}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-left"
            >
                {selectedChapters.length > 0
                    ? `Đã chọn ${selectedChapters.length} chương`
                    : "Chọn chương..."}
            </button>

            {/* Dropdown các checkbox chương */}
            {open && (

                <OutsideClickWrapper
                    onClickOutside={() => setOpen(false)}
                    ignoreOutsideClick="chapterDropdownRef"
                    className="absolute z-10 mt-2 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {codes["chapter"].map((ch) => (
                        <label key={ch.code} className="flex items-center px-3 py-2 hover:bg-gray-100 cursor-pointer">
                            <input
                                type="checkbox"
                                className="mr-2"
                                checked={selectedChapters.includes(ch.code)}
                                onChange={() => handleCheckboxChange(ch.code)}
                            />
                            {ch.code} - {ch.description}
                        </label>
                    ))}
                </OutsideClickWrapper>
            )}

            {/* Danh sách chương đã chọn */}
            <div className="mt-3">
                <h4 className="text-sm font-semibold text-gray-700 mb-1">Chương đã chọn:</h4>
                {selectedChapters.length === 0 ? (
                    <p className="text-sm text-gray-500">Chưa chọn chương nào.</p>
                ) : (
                    <ul className="list-disc list-inside text-sm text-gray-700">
                        {selectedChapters.map(code => {
                            const chapter = codes["chapter"].find(ch => ch.code === code);
                            return (
                                <li key={code}>
                                    {code} - {chapter?.description}
                                </li>
                            );
                        })}
                    </ul>
                )}
            </div>
        </div>
    );
};



const AutoGenerateExam = () => {
    const { closeSidebar } = useSelector((state) => state.sidebar);
    const { codes } = useSelector((state) => state.codes);
    const { downloadingQuestions } = useSelector((state) => state.exams);
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [selectedChapters, setSelectedChapters] = useState([])
    const { loading: loadingUploadExam } = useSelector((state) => state.exams);
    // State management
    const [step, setStep] = useState(1); // Reset to step 1
    const [loading, setLoading] = useState(false);
    const [activeTab, setActiveTab] = useState('TN'); // Added for tab functionality
    const [examData, setExamData] = useState({
        title: '',
        description: '',
        class: '12',
        testDuration: null,
        tnCount: 12,
        dsCount: 4,
        tlnCount: 6,
        examType: '', // Kiểu đề thi
        year: '', // Năm
        chapter: '', // Chương (hiện khi chọn kiểu OT)
        passRate: null,
        public: false,
        isClassroomExam: false,
    });
    const [generatedQuestions, setGeneratedQuestions] = useState([]); // Remove mock data
    const [analysisData, setAnalysisData] = useState(null);
    const [selectedQuestion, setSelectedQuestion] = useState(null); // Reset to null
    const [overQuestion, setOverQuestion] = useState(null);
    const [draggedQuestion, setDraggedQuestion] = useState(null);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [pendingReplacement, setPendingReplacement] = useState(null);

    // Related questions modal state
    const [showRelatedModal, setShowRelatedModal] = useState(false);
    const [relatedQuestion, setRelatedQuestion] = useState(null);

    // Sensors for main DndContext
    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 12, // Increased from 8 to reduce sensitivity
            },
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    // Separate questions by type with memoization
    const questionsByType = useMemo(() => {
        const TN = generatedQuestions.filter(q => q.typeOfQuestion === 'TN').sort((a, b) => a.order - b.order);
        const DS = generatedQuestions.filter(q => q.typeOfQuestion === 'DS').sort((a, b) => a.order - b.order);
        const TLN = generatedQuestions.filter(q => q.typeOfQuestion === 'TLN').sort((a, b) => a.order - b.order);

        return { TN, DS, TLN };
    }, [generatedQuestions]);

    // Function to update order when reordering within a type
    const updateQuestionsOrder = (questions, type, startOrder) => {
        return questions?.map((q, index) => ({
            ...q,
            order: startOrder + index
        }));
    };

    // Function to handle reordering within question types
    const handleReorder = (type, oldIndex, newIndex) => {
        setGeneratedQuestions(prevQuestions => {
            const updatedQuestions = [...prevQuestions];

            // Find questions of this type
            const typeQuestions = updatedQuestions.filter(q => q.typeOfQuestion === type);
            const otherQuestions = updatedQuestions.filter(q => q.typeOfQuestion !== type);

            // Reorder within type
            const reorderedTypeQuestions = arrayMove(typeQuestions, oldIndex, newIndex);

            // Update orders based on type
            let startOrder = 1;
            if (type === 'DS') startOrder = examData.tnCount + 1;
            if (type === 'TLN') startOrder = examData.tnCount + examData.dsCount + 1;

            const updatedTypeQuestions = updateQuestionsOrder(reorderedTypeQuestions, type, startOrder);

            // Combine all questions
            const allUpdatedQuestions = [...otherQuestions, ...updatedTypeQuestions];

            return allUpdatedQuestions.sort((a, b) => a.order - b.order);
        });
    };

    // Function to handle question selection
    const handleQuestionSelect = (question) => {
        setSelectedQuestion(question);
    };

    // Function to handle question replacement
    const handleReplaceQuestion = (oldQuestion, newQuestion) => {

        const hasQuestion = generatedQuestions.find(q => q.id === newQuestion.id);

        if (hasQuestion) {
            dispatch(setErrorMessage("Câu hỏi đã tồn tại trong đề thi!"));
            return;
        }

        setGeneratedQuestions(prevQuestions => {
            return prevQuestions.map(q => {
                if (q.id === oldQuestion.id) {
                    // Keep the same order but replace with new question
                    return {
                        ...newQuestion,
                        order: q.order,
                        questionType: q.questionType
                    };
                }
                return q;
            });
        });

        // Clear selection after replacement
        setSelectedQuestion(null);
    };

    // Function to handle showing related questions
    const handleShowRelated = (question) => {
        setRelatedQuestion(question);
        setShowRelatedModal(true);
    };

    // Function to handle closing related questions modal
    const handleCloseRelatedModal = () => {
        setShowRelatedModal(false);
        setRelatedQuestion(null);
    };

    // Handle drag start
    const handleDragStart = (event) => {
        const { active } = event;
        console.log('Drag start:', active);
        if (active.data.current?.type === 'suggestion') {
            setDraggedQuestion(active.data.current.question);
            console.log('Dragging suggestion:', active.data.current.question);
        }
    };

    // Handle drag over
    const handleDragOver = (event) => {
        const { active, over } = event;

        if (!over) {
            setOverQuestion(null);
            return;
        }

        const activeData = active.data.current;
        const overData = over.data.current;

        // Only highlight if dragging suggestion over question of same type
        // AND not over the suggestion panel area
        if (activeData?.type === 'suggestion' && overData?.type === 'question') {
            const suggestionType = activeData.question.typeOfQuestion;
            const questionType = overData.question.typeOfQuestion;

            if (suggestionType === questionType) {
                setOverQuestion(overData.question);
            } else {
                setOverQuestion(null);
            }
        } else {
            setOverQuestion(null);
        }
    };

    // Handle drag end
    const handleDragEnd = (event) => {
        const { active, over } = event;

        setDraggedQuestion(null);
        setOverQuestion(null);

        if (!over) {
            console.log('No drop target');
            return;
        }

        const activeData = active.data.current;
        const overData = over.data.current;

        // Handle suggestion dropped on question
        if (activeData?.type === 'suggestion' && overData?.type === 'question') {
            const suggestionQuestion = activeData.question;
            const targetQuestion = overData.question;

            // Additional check: make sure we're actually dropping on a question, not just passing over
            const dropRect = over.rect;
            if (dropRect) {

                // Only allow replacement if same question type
                if (suggestionQuestion.typeOfQuestion === targetQuestion.typeOfQuestion) {
                    // Store pending replacement for confirmation
                    setPendingReplacement({
                        oldQuestion: targetQuestion,
                        newQuestion: suggestionQuestion
                    });
                    setShowConfirmModal(true);
                } else {
                    console.log('Different types!');
                    alert('Chỉ có thể thay thế câu hỏi cùng loại!');
                }
            } else {
                console.log('Invalid drop position, cancelling replacement');
            }
        }
        // Handle suggestion dropped back on suggestion panel (cancel operation)
        else if (activeData?.type === 'suggestion' && overData?.type === 'suggestion-panel') {
            console.log('Suggestion dropped back on panel, no action needed');
            // No action needed - just return to original position
        }
        // Handle reordering within same question type
        else if (activeData?.type === 'question' && overData?.type === 'question') {
            const activeQuestion = activeData.question;
            const overQuestion = overData.question;

            // Only allow reordering within same type
            if (activeQuestion.typeOfQuestion === overQuestion.typeOfQuestion) {
                const questionType = activeQuestion.typeOfQuestion;
                const typeQuestions = questionsByType[questionType];

                const oldIndex = typeQuestions.findIndex(q => q.id === activeQuestion.id);
                const newIndex = typeQuestions.findIndex(q => q.id === overQuestion.id);

                if (oldIndex !== newIndex) {
                    handleReorder(questionType, oldIndex, newIndex);
                }
            }
        } else {
            console.log('No valid drop action');
        }
    };

    // Confirmation modal handlers
    const handleConfirmReplacement = () => {
        if (pendingReplacement) {
            // Perform the actual replacement
            handleReplaceQuestion(pendingReplacement.oldQuestion, pendingReplacement.newQuestion);
        }

        // Reset modal state
        setShowConfirmModal(false);
        setPendingReplacement(null);
    };

    const handleCancelReplacement = () => {
        setShowConfirmModal(false);
        setPendingReplacement(null);
    };

    useEffect(() => {
        dispatch(fetchCodesByType(["chapter", "difficulty", "grade", "exam type", "year"]));
    }, [dispatch]);

    // Get filtered data
    const gradeOptions = codes?.grade || [];
    const chapterOptions = codes?.chapter || [];
    const difficultyOptions = codes?.difficulty || [];

    // Filter chapters based on selected class
    const filteredChapterOptions = useMemo(() => {
        if (!Array.isArray(codes?.chapter)) return [];
        if (examData.class && examData.class.trim() !== "") {
            return codes.chapter.filter(chapter => chapter.code.startsWith(examData.class));
        } else {
            return codes.chapter;
        }
    }, [codes?.chapter, examData.class]);

    const handleInputChange = (field, value) => {
        setExamData(prev => {
            const newData = {
                ...prev,
                [field]: value
            };

            // Reset chương khi thay đổi kiểu đề thi và không phải OT
            if (field === 'examType' && value !== 'OT') {
                newData.chapter = '';
            }

            // Reset chương khi thay đổi lớp
            if (field === 'class') {
                newData.chapter = '';
            }

            return newData;
        });
    };

    const handleGenerateQuestions = async () => {
        setLoading(true);
        try {
            const requestData = {
                classLevel: examData.class || 12,
                tnCount: examData.tnCount || 0,
                dsCount: examData.dsCount || 0,
                tlnCount: examData.tlnCount || 0,
                chapters: selectedChapters
            };

            if (requestData.classLevel.trim() === "") {
                dispatch(setErrorMessage('Vui lòng chọn lớp!'));
                return;
            }

            if (requestData.tnCount + requestData.dsCount + requestData.tlnCount === 0) {
                dispatch(setErrorMessage('Vui lòng chọn số lượng câu hỏi!'));
                return;
            }

            const response = await autoGenerateQuestions(requestData);

            if (response.success) {
                setGeneratedQuestions(response.data.questions);
                setAnalysisData(response.data.analysis);
                setStep(2);
            } else {
                alert('Lỗi khi tạo câu hỏi: ' + response.message);
            }
        } catch (error) {
            console.error('Error generating questions:', error);
            alert('Có lỗi xảy ra khi tạo câu hỏi tự động');
        } finally {
            setLoading(false);
        }
    };

    const handleNext = () => {
        if (step === 1) {
            handleGenerateQuestions();
        }
    };

    const handlePrev = () => {
        if (step === 2) {
            setStep(1);
        }
    };

    const handleCreateExam = async () => {
        await dispatch(createExam({
            examData: {
                name: examData.title,
                class: examData.class,
                typeOfExam: examData.examType,
                chapter: examData.chapter || null,
                year: examData.year,
                public: examData.public,
                isClassroomExam: examData.isClassroomExam,
                description: examData.description || null,
                passRate: examData.passRate || null,
                testDuration: examData.testDuration || null,
            },
            questionIds: generatedQuestions.map(q => q.id)
        })).unwrap().then((data) => {
            navigate(`/admin/exam-management/${data.id}/preview`);
        });

    };

    const handleDownloadDocx = async () => {
        try {
            const questionIds = generatedQuestions.map(q => q.id);
            await dispatch(downloadQuestionsDocxByIds({
                questionIds,
                examTitle: examData.title || 'Generated-Questions'
            })).unwrap();
            console.log('Downloaded DOCX for generated questions');
        } catch (error) {
            console.error('Error downloading DOCX:', error);
            alert('Có lỗi xảy ra khi tải file. Vui lòng thử lại!');
        }
    };

    const totalQuestions = examData.tnCount + examData.dsCount + examData.tlnCount;

    const handleChapterSelect = (selected) => {
        if (selectedChapters.includes(selected)) {
            setSelectedChapters(selectedChapters.filter(ch => ch !== selected));
            return;
        }
        setSelectedChapters([...selectedChapters, selected]);
    };

    return (
        <div className="bg-gray-50 flex flex-col">
            <AdminSidebar />
            <div className={`bg-gray-50 flex flex-col ${closeSidebar ? "ml-[104px]" : "ml-64"}`}>
                {/* Header */}
                <div className="fixed top-0 z-10 w-full bg-white border-b border-gray-200 flex justify-between items-center px-3 py-2">
                    <div className="flex items-center gap-2">
                        <button
                            onClick={() => navigate('/admin/exam-management')}
                            className="p-1 hover:bg-gray-100 rounded transition-colors"
                        >
                            <ArrowLeft className="w-4 h-4 text-gray-600" />
                        </button>
                        <div>
                            <h1 className="text-sm font-bold text-gray-900">Tạo đề thi tự động</h1>
                            <p className="text-xs text-gray-600">AI sẽ chọn câu hỏi đa dạng nhất cho bạn</p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="text-xs text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full">
                            Bước {step}/2
                        </div>
                    </div>
                </div>

                {/* Main Content */}
                <div className="pt-16 p-6">
                    {/* Progress Bar */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between mb-2">
                            <span className={`text-sm font-medium ${step >= 1 ? 'text-blue-600' : 'text-gray-500'}`}>
                                Thông tin đề thi
                            </span>
                            <span className={`text-sm font-medium ${step >= 2 ? 'text-blue-600' : 'text-gray-500'}`}>
                                Xem câu hỏi
                            </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${(step / 2) * 100}%` }}
                            ></div>
                        </div>

                        {/* Step 1: Exam Information */}
                        {step === 1 && (
                            <div className="bg-white rounded-lg shadow-sm border p-6">
                                <div className="flex items-center gap-2 mb-6">
                                    <Zap className="w-5 h-5 text-blue-600" />
                                    <h2 className="text-xl font-semibold text-gray-900">Thông tin đề thi</h2>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Basic Information */}
                                    <div className="space-y-4">
                                        <CustomInput
                                            label="Tiêu đề đề thi"
                                            value={examData.title}
                                            onChange={(value) => handleInputChange('title', value)}
                                            required={true}
                                            placeholder="Nhập tiêu đề đề thi"
                                        />

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Mô tả
                                            </label>
                                            <textarea
                                                value={examData.description}
                                                onChange={(e) => handleInputChange('description', e.target.value)}
                                                rows={3}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                placeholder="Mô tả về đề thi..."
                                            />
                                        </div>
                                        <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                                            <CustomDropdown
                                                title="Lớp"
                                                options={codes?.grade}
                                                value={examData.class}
                                                handleSelect={(code) => handleInputChange('class', code)}
                                                required={true}
                                                ignoreRef="gradeRef"
                                            />

                                            <CustomDropdown
                                                handleSelect={(value) => handleInputChange('testDuration', parseInt(value))}
                                                title={'Thời gian (phút)'}
                                                value={examData.testDuration}
                                                options={[
                                                    { code: null, description: 'Không giới hạn' },
                                                    { code: 30, description: '30 phút' },
                                                    { code: 45, description: '45 phút' },
                                                    { code: 60, description: '60 phút' },
                                                    { code: 90, description: '90 phút' },
                                                    { code: 120, description: '120 phút' },
                                                    { code: 180, description: '180 phút' }
                                                ]}
                                                ignoreRef="timeRef"
                                            />

                                            <CustomInput
                                                label="Tỷ lệ đạt (%)"
                                                value={examData.passRate}
                                                onChange={(value) => handleInputChange('passRate', parseInt(value))}
                                                type="number"
                                                min={0}
                                                max={100}
                                                placeholder={'%'}
                                            />
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <CustomCheckbox
                                                label="Công khai"
                                                checked={examData.public}
                                                onChange={(checked) => handleInputChange('public', checked)}
                                            />
                                            <CustomCheckbox
                                                label="Đề thi lớp"
                                                checked={examData.isClassroomExam}
                                                onChange={(checked) => handleInputChange('isClassroomExam', checked)}
                                            />
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <CustomDropdown
                                                title="Kiểu đề thi"
                                                options={codes?.['exam type']}
                                                value={examData.examType}
                                                handleSelect={(code) => handleInputChange('examType', code)}
                                                required={true}
                                                ignoreRef="examTypeRef"
                                            />

                                            <CustomDropdown
                                                title="Năm"
                                                options={codes?.year}
                                                value={examData.year}
                                                handleSelect={(code) => handleInputChange('year', code)}
                                                required={true}
                                                ignoreRef="yearRef"
                                            />
                                        </div>

                                        {/* Hiển thị chọn chương khi kiểu đề là OT */}
                                        {examData.examType === 'OT' && (
                                            <CustomDropdown
                                                title="Chương"
                                                options={codes?.chapter}
                                                value={examData.chapter}
                                                handleSelect={(code) => handleInputChange('chapter', code)}
                                                required={true}
                                                ignoreRef="chapterRef"
                                            />

                                        )}
                                    </div>

                                    {/* Question Distribution */}
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-medium text-gray-900 border-b pb-2">
                                            Phân bố câu hỏi
                                        </h3>

                                        <CustomInput
                                            label="Câu trắc nghiệm (TN)"
                                            value={examData.tnCount}
                                            onChange={(value) => handleInputChange('tnCount', parseInt(value) || 0)}
                                            type="number"
                                            min={0}
                                            max={50}
                                        />

                                        <CustomInput
                                            label="Câu đúng/sai (DS)"
                                            value={examData.dsCount}
                                            onChange={(value) => handleInputChange('dsCount', parseInt(value) || 0)}
                                            type="number"
                                            min={0}
                                            max={20}
                                        />

                                        <CustomInput
                                            label="Câu trả lời ngắn (TLN)"
                                            value={examData.tlnCount}
                                            onChange={(value) => handleInputChange('tlnCount', parseInt(value) || 0)}
                                            type="number"
                                            min={0}
                                            max={20}
                                        />
                                        <ChapterDropdown codes={codes} handleSelect={handleChapterSelect} selectedChapters={selectedChapters} />


                                        <div className="bg-blue-50 p-3 rounded-md">
                                            <p className="text-sm text-blue-800">
                                                <strong>Tổng số câu hỏi: {totalQuestions}</strong>
                                            </p>
                                            <p className="text-xs text-blue-600 mt-1">
                                                AI sẽ chọn câu hỏi đa dạng nhất về độ khó và chương
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex justify-between mt-8">
                                    <button
                                        onClick={() => navigate('/admin/exam-management')}
                                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                                    >
                                        Hủy
                                    </button>
                                    <button
                                        onClick={handleNext}
                                        disabled={!examData.title.trim() || !examData.examType || !examData.year || (examData.examType === 'OT' && !examData.chapter) || loading}
                                        className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {loading ? (
                                            <>
                                                <RefreshCw className="w-4 h-4 animate-spin" />
                                                Đang tạo...
                                            </>
                                        ) : (
                                            <>
                                                Tạo câu hỏi tự động
                                                <ChevronRight className="w-4 h-4" />
                                            </>
                                        )}
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* Step 2: Generated Questions */}
                        {step === 2 && (
                            <DndContext
                                sensors={sensors}
                                onDragStart={handleDragStart}
                                onDragOver={handleDragOver}
                                onDragEnd={handleDragEnd}
                                collisionDetection={closestCenter}
                            >
                                <SortableContext
                                    items={generatedQuestions.map(q => q.id)}
                                    strategy={verticalListSortingStrategy}
                                >
                                    <div className="bg-white rounded-lg shadow-sm border">
                                        {/* Header */}
                                        <div className="p-6 border-b">
                                            <div className="flex items-center gap-2">
                                                <CheckCircle className="w-5 h-5 text-green-600" />
                                                <h2 className="text-xl font-semibold text-gray-900">Câu hỏi đã tạo tự động</h2>
                                            </div>
                                        </div>

                                        {/* Top Panel - Exam Info & Analysis */}
                                        <div className="border-b border-gray-200">
                                            <div className="flex">
                                                {/* Exam Information */}
                                                <div className="w-1/2 p-6 border-r border-gray-200">
                                                    <h3 className="font-semibold text-gray-900 mb-4">Thông tin đề thi</h3>
                                                    <div className="grid grid-cols-2 gap-4">
                                                        <div>
                                                            <label className="text-sm font-medium text-gray-700">Tiêu đề:</label>
                                                            <p className="text-sm text-gray-900">{examData.title}</p>
                                                        </div>
                                                        <div>
                                                            <label className="text-sm font-medium text-gray-700">Lớp:</label>
                                                            <p className="text-sm text-gray-900">Lớp {examData.class}</p>
                                                        </div>
                                                        <div>
                                                            <label className="text-sm font-medium text-gray-700">Thời gian:</label>
                                                            <p className="text-sm text-gray-900">{examData.timeLimit} phút</p>
                                                        </div>
                                                        <div>
                                                            <label className="text-sm font-medium text-gray-700">Tổng số câu:</label>
                                                            <p className="text-sm text-gray-900">{analysisData?.totalQuestions || 0} câu</p>
                                                        </div>
                                                        {examData.examType && (
                                                            <div>
                                                                <label className="text-sm font-medium text-gray-700">Kiểu đề thi:</label>
                                                                <p className="text-sm text-gray-900">
                                                                    {getDescription('exam type', examData.examType, codes)}
                                                                </p>
                                                            </div>
                                                        )}
                                                        {examData.year && (
                                                            <div>
                                                                <label className="text-sm font-medium text-gray-700">Năm:</label>
                                                                <p className="text-sm text-gray-900">
                                                                    {getDescription('year', examData.year, codes)}
                                                                </p>
                                                            </div>
                                                        )}
                                                        {examData.examType === 'OT' && examData.chapter && (
                                                            <div>
                                                                <label className="text-sm font-medium text-gray-700">Chương:</label>
                                                                <p className="text-sm text-gray-900">
                                                                    {getDescription('chapter', examData.chapter, codes)}
                                                                </p>
                                                            </div>
                                                        )}
                                                    </div>
                                                    {examData.description && (
                                                        <div className="mt-3">
                                                            <label className="text-sm font-medium text-gray-700">Mô tả:</label>
                                                            <p className="text-sm text-gray-900">{examData.description}</p>
                                                        </div>
                                                    )}
                                                </div>

                                                {/* Analysis Summary */}
                                                {analysisData && (
                                                    <div className="w-1/2 p-6">
                                                        <h3 className="font-semibold text-gray-900 mb-4">Phân tích đề thi</h3>

                                                        <div className="grid grid-cols-3 gap-4">
                                                            {/* Type Distribution */}
                                                            <div className="bg-blue-50 p-3 rounded-lg">
                                                                <h4 className="font-medium text-blue-800 mb-2 text-sm">Loại câu hỏi</h4>
                                                                <div className="text-xs text-blue-700 space-y-1">
                                                                    <p>TN: {analysisData.typeDistribution.TN}</p>
                                                                    <p>DS: {analysisData.typeDistribution.DS}</p>
                                                                    <p>TLN: {analysisData.typeDistribution.TLN}</p>
                                                                </div>
                                                            </div>

                                                            {/* Chapter Distribution */}
                                                            <div className="bg-green-50 p-3 rounded-lg">
                                                                <h4 className="font-medium text-green-800 mb-2 text-sm">Chương</h4>
                                                                <div className="text-xs text-green-700 space-y-1 max-h-16 overflow-y-auto">
                                                                    {Object.entries(analysisData.chapterDistribution).map(([chapter, count]) => (
                                                                        <p key={chapter}>{getDescription('chapter', chapter, codes)}: {count}</p>
                                                                    ))}
                                                                </div>
                                                            </div>

                                                            {/* Difficulty Distribution */}
                                                            <div className="bg-orange-50 p-3 rounded-lg">
                                                                <h4 className="font-medium text-orange-800 mb-2 text-sm">Độ khó</h4>
                                                                <div className="text-xs text-orange-700 space-y-1">
                                                                    {Object.entries(analysisData.difficultyDistribution).map(([difficulty, count]) => (
                                                                        <p key={difficulty}>{getDescription('difficulty', difficulty, codes)}: {count}</p>
                                                                    ))}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Bottom Two Column Layout */}
                                        <div className="flex" >
                                            {/* Left Column - Questions List */}
                                            <div className="md:w-2/3 2xl:w-3/4 border-r border-gray-200 flex flex-col">
                                                <div className="p-4 border-b border-gray-200">
                                                    <h3 className="font-semibold text-gray-900 mb-4">Danh sách câu hỏi</h3>

                                                    {/* Tab Navigation */}
                                                    <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                                                        <button
                                                            onClick={() => setActiveTab('TN')}
                                                            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'TN'
                                                                ? 'bg-blue-600 text-white shadow-sm'
                                                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                                                                }`}
                                                        >
                                                            Trắc nghiệm ({questionsByType.TN?.length || 0})
                                                        </button>
                                                        <button
                                                            onClick={() => setActiveTab('DS')}
                                                            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'DS'
                                                                ? 'bg-green-600 text-white shadow-sm'
                                                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                                                                }`}
                                                        >
                                                            Đúng/Sai ({questionsByType.DS?.length || 0})
                                                        </button>
                                                        <button
                                                            onClick={() => setActiveTab('TLN')}
                                                            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'TLN'
                                                                ? 'bg-orange-600 text-white shadow-sm'
                                                                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                                                                }`}
                                                        >
                                                            Trả lời ngắn ({questionsByType.TLN?.length || 0})
                                                        </button>
                                                    </div>
                                                </div>

                                                <div className=" p-4">
                                                    {/* Tab Content */}
                                                    {activeTab === 'TN' && (
                                                        <TNQuestionsSection
                                                            questions={questionsByType.TN}
                                                            onReorder={handleReorder}
                                                            codes={codes}
                                                            selectedQuestion={selectedQuestion}
                                                            onQuestionSelect={handleQuestionSelect}
                                                            overQuestion={overQuestion}
                                                            onShowRelated={handleShowRelated}
                                                        />
                                                    )}

                                                    {activeTab === 'DS' && (
                                                        <DSQuestionsSection
                                                            questions={questionsByType.DS}
                                                            onReorder={handleReorder}
                                                            codes={codes}
                                                            selectedQuestion={selectedQuestion}
                                                            onQuestionSelect={handleQuestionSelect}
                                                            overQuestion={overQuestion}
                                                            onShowRelated={handleShowRelated}
                                                        />
                                                    )}

                                                    {activeTab === 'TLN' && (
                                                        <TLNQuestionsSection
                                                            questions={questionsByType.TLN}
                                                            onReorder={handleReorder}
                                                            codes={codes}
                                                            selectedQuestion={selectedQuestion}
                                                            onQuestionSelect={handleQuestionSelect}
                                                            overQuestion={overQuestion}
                                                            onShowRelated={handleShowRelated}
                                                        />
                                                    )}
                                                </div>
                                            </div>

                                            {/* Right Panel - Question Suggestions */}
                                            <div className="md:w-1/3 2xl:w-1/4 bg-gray-50 sticky top-16 overflow-hidden" style={{ height: 'calc(100vh - 4rem)' }}>
                                                <div className="p-4 border-b border-gray-200 bg-white">
                                                    <h3 className="font-semibold text-gray-900">Gợi ý câu hỏi thay thế</h3>
                                                </div>

                                                <QuestionSuggestionPanel
                                                    selectedQuestion={selectedQuestion}
                                                    generatedQuestions={generatedQuestions}
                                                    codes={codes}
                                                    onReplaceQuestion={handleReplaceQuestion}
                                                    examClass={examData.class}
                                                />
                                            </div>
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="p-6 border-t bg-gray-50 flex justify-between">
                                            <button
                                                onClick={handlePrev}
                                                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors flex items-center gap-2"
                                            >
                                                <ChevronLeft className="w-4 h-4" />
                                                Quay lại
                                            </button>
                                            <div className="flex gap-3">
                                                <button
                                                    onClick={handleDownloadDocx}
                                                    disabled={downloadingQuestions || generatedQuestions.length === 0}
                                                    className={`
                                                        px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2
                                                        ${downloadingQuestions || generatedQuestions.length === 0
                                                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                                            : 'bg-blue-600 hover:bg-blue-700 text-white'
                                                        }
                                                    `}
                                                    title={downloadingQuestions ? "Đang tải..." : "Tải file Word câu hỏi"}
                                                >
                                                    {downloadingQuestions ? (
                                                        <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                                                    ) : (
                                                        <Download className="w-4 h-4" />
                                                    )}
                                                    Tải Word
                                                </button>
                                                <button
                                                    onClick={handleGenerateQuestions}
                                                    disabled={loading}
                                                    className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
                                                >
                                                    <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                                                    Tạo lại
                                                </button>
                                                <button
                                                    onClick={handleCreateExam}
                                                    disabled={loadingUploadExam}
                                                    className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
                                                >
                                                    <Save className="w-4 h-4" />
                                                    {loadingUploadExam ? (
                                                        <>
                                                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                                            Đang tạo...
                                                        </>
                                                    ) : (
                                                        <>Tạo đề thi</>
                                                    )}
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Drag Overlay */}
                                    <DragOverlay>
                                        {draggedQuestion ? (
                                            <div className="bg-white border-2 border-blue-500 rounded-lg p-3 shadow-lg opacity-90">
                                                <div className="flex items-center gap-2 mb-2">
                                                    <span className="bg-gray-800 text-white px-2 py-0.5 rounded text-xs font-mono">
                                                        ID: {draggedQuestion.id}
                                                    </span>
                                                    <span className="bg-gray-100 text-gray-700 px-2 py-0.5 rounded text-xs">
                                                        {draggedQuestion.typeOfQuestion}
                                                    </span>
                                                </div>
                                                <LatexRenderer
                                                    text={draggedQuestion.content.length > 60 ? draggedQuestion.content.substring(0, 60) + '...' : draggedQuestion.content}
                                                    className="text-sm text-gray-800"
                                                />
                                            </div>
                                        ) : null}
                                    </DragOverlay>
                                </SortableContext>
                            </DndContext>
                        )}
                    </div>
                </div>
            </div>

            {/* Confirmation Modal */}
            {showConfirmModal && pendingReplacement && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-2xl max-h-[80vh] overflow-y-auto">
                        <h3 className="text-lg font-semibold mb-4">Xác nhận thay đổi câu hỏi</h3>

                        <div className="mb-4">
                            <h4 className="font-medium text-red-600 mb-2">Câu hỏi cũ (sẽ bị thay thế):</h4>
                            <div className="p-3 border border-red-200 rounded bg-red-50">
                                <div className="text-sm text-gray-600 mb-1">
                                    ID: {pendingReplacement.oldQuestion.id} | Loại: {pendingReplacement.oldQuestion.typeOfQuestion}
                                </div>
                                <LatexRenderer text={pendingReplacement.oldQuestion.content} className="text-sm" />
                            </div>
                        </div>

                        <div className="mb-6">
                            <h4 className="font-medium text-green-600 mb-2">Câu hỏi mới (sẽ thay thế):</h4>
                            <div className="p-3 border border-green-200 rounded bg-green-50">
                                <div className="text-sm text-gray-600 mb-1">
                                    ID: {pendingReplacement.newQuestion.id} | Loại: {pendingReplacement.newQuestion.typeOfQuestion}
                                </div>
                                <LatexRenderer text={pendingReplacement.newQuestion.content} className="text-sm" />
                            </div>
                        </div>

                        <div className="flex justify-end space-x-3">
                            <button
                                onClick={handleCancelReplacement}
                                className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-50"
                            >
                                Hủy
                            </button>
                            <button
                                onClick={handleConfirmReplacement}
                                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                            >
                                Xác nhận thay đổi
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Related Questions Modal */}
            <RelatedQuestionsModal
                isOpen={showRelatedModal}
                onClose={handleCloseRelatedModal}
                question={relatedQuestion}
                codes={codes}
                onReplaceQuestion={handleReplaceQuestion}
            />
        </div>
    );
};

export default AutoGenerateExam;
