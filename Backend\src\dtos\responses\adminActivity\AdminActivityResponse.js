class AdminActivityResponse {
    constructor(activity) {
        this.id = activity.id;
        this.adminId = activity.adminId;
        this.action = activity.action;
        this.targetId = activity.targetId;
        this.description = activity.description;
        this.createdAt = activity.createdAt;
        this.updatedAt = activity.updatedAt;
        this.admin = activity.admin ? {
            id: activity.admin.id,
            firstName: activity.admin.firstName,
            lastName: activity.admin.lastName,
            fullName: `${activity.admin.lastName} ${activity.admin.firstName}`,
            username: activity.admin.username,
            avatarUrl: activity.admin.avatarUrl
        } : null;
    }
}

export default AdminActivityResponse;
