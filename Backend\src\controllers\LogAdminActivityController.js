import * as AdminActivityLogService from '../services/admin.activity.log.service.js';

export const getAllAdminActivities = async (req, res) => {
    try {
        const search = req.query.search || '';
        const limit = parseInt(req.query.limit, 10) || 20;
        const cursor = req.query.cursor || null; // Cursor là timestamp
        const sortOrder = req.query.sortOrder || 'DESC';
        const startDate = req.query.startDate || null;
        const endDate = req.query.endDate || null;
        const result = await AdminActivityLogService.getAllAdminActivities(
            search,
            limit,
            cursor,
            sortOrder,
            startDate,
            endDate
        );

        return res.status(200).json({
            message: 'L<PERSON>y danh sách hoạt động admin thành công',
            data: result
        });
    } catch (error) {
        console.error('Error getting admin activities:', error);
        return res.status(500).json({
            message: 'Lỗi server khi lấy danh sách hoạt động admin',
            error: error.message
        });
    }
};