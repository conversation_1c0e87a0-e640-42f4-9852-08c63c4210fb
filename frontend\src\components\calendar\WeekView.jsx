import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Calendar, Clock, ChevronLeft, ChevronRight, CheckCircle } from 'lucide-react';
import UserLayout from 'src/layouts/UserLayout';
import { fetchClassesOverview } from 'src/features/class/classSlice';
import { getLearningItemWeekend } from 'src/features/learningItem/learningItemSlice';
import CalendarMonth from 'src/components/calendar/CalenderMonth';
import { resetCalendar, setSelectedDay, setView } from 'src/features/calendar/calendarSlice';
import SidebarCalendar from 'src/components/calendar/SidebarCalender';
import NavigateTimeButton from './NavigateTimeButton';
import TimeSlots from './TimeSlots';

const WeekView = ({ weekDays, formatDate, getClassHeight, isMobile }) => {
    const dispatch = useDispatch();
    const { classesOverview: classes, loading } = useSelector((state) => state.classes);
    const { learningItemsWeekend } = useSelector((state) => state.learningItems);
    const [currentTime, setCurrentTime] = useState(new Date());
    const { startOfWeek, endOfWeek, selectedDay, timeSlots } = useSelector((state) => state.calendar);

    const handleDayClick = (date) => {
        dispatch(setSelectedDay(date.toISOString()));
        dispatch(setView('day'));
    };


    useEffect(() => {
        dispatch(getLearningItemWeekend({ startOfWeek, endOfWeek }));
    }, [dispatch, startOfWeek, endOfWeek]);

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date());
        }, 60000); // Cập nhật mỗi phút

        return () => clearInterval(interval);
    }, []);

    // useEffect(() => {
    //     console.log(learningItemsWeekend);
    // }, [learningItemsWeekend]);



    return (
        <div className="w-full">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                {/* Calendar header - Days of the week */}
                <div className="grid sm:grid-cols-[4rem_repeat(7,1fr)] grid-cols-[3.5rem_repeat(7,1fr)] sticky top-0 z-[35]">
                    <div className="border-r border-gray-200 left-0 z-[35] flex text-center items-center justify-center">
                        <NavigateTimeButton />
                    </div>
                    {weekDays.map((day, index) => {
                        const today = new Date();
                        const todayStr = `${String(today.getDate()).padStart(2, '0')}-${String(today.getMonth() + 1).padStart(2, '0')}`;
                        const isToday = day.date === todayStr;

                        const itemsToday = learningItemsWeekend?.filter(item => formatDate(item.learningItem.deadline) === day.date);

                        return (
                            <div key={index} className="relative flex flex-col h-full ">
                                <div className="absolute bottom-0 right-0 h-1/3 w-px bg-gray-200" />

                                {/* Header */}
                                <div className="p-2 text-center flex flex-col gap-1 items-center z-10">
                                    <div className="font-semibold text-emerald-800">{day.short}</div>
                                    <div className={`lg:text-md text-sm ${isToday ? 'text-red-600 underline font-semibold' : 'text-gray-500'}`}>
                                        {day.date}
                                    </div>
                                </div>

                                {/* Items tràn ra ngoài */}
                                <div className="absolute top-full left-1 w-[calc(100%-8px)] z-40 mt-[8px]">
                                    {itemsToday?.map((item, i) => {
                                        const Icon = item.isDone ? CheckCircle : Clock;
                                        const now = new Date();
                                        const deadline = new Date(item.learningItem.deadline);
                                        deadline.setHours(23, 59, 59, 999);
                                        const isOverdue = !item.isDone && deadline < now;

                                        const bgColor = item.isDone
                                            ? "bg-green-100"
                                            : isOverdue
                                                ? "bg-red-100"
                                                : "bg-yellow-100";

                                        const borderColor = item.isDone
                                            ? "border-green-500"
                                            : isOverdue
                                                ? "border-red-500"
                                                : "border-yellow-400";

                                        const textColor = item.isDone
                                            ? "text-green-700"
                                            : isOverdue
                                                ? "text-red-700"
                                                : "text-yellow-800";

                                        return (
                                            <div
                                                key={i}
                                                title={item.title}
                                                className={`w-full ${bgColor} border-l-4 ${borderColor} rounded-r-md px-2 py-[0.3rem] overflow-hidden hover:brightness-95 transition-colors duration-150 cursor-pointer text-[11px] text-left flex items-center gap-1 ${textColor} mt-[4px] first:mt-0`}
                                            >
                                                <Icon className="w-3 h-3" />
                                                <span className="truncate">
                                                    {item.learningItem.name.length > 15 ? item.learningItem.name.slice(0, 15) + '…' : item.learningItem.name}
                                                </span>
                                            </div>
                                        );
                                    })}
                                </div>
                            </div>

                        );
                    })}
                </div>

                {/* Calendar body */}
                <div className="grid sm:grid-cols-[4rem_repeat(7,1fr)] grid-cols-[3.5rem_repeat(7,1fr)]">
                    {/* Time slots (Y-axis) - Sticky on mobile */}
                    <TimeSlots />


                    {/* Calendar grid */}
                    {weekDays.map((day, dayIndex) => (
                        <div key={dayIndex} className="col-span-1 border-r border-gray-200 last:border-r-0 relative">
                            {timeSlots.map((_, timeIndex) => (
                                <div
                                    key={timeIndex}
                                    onClick={() => handleDayClick(new Date(day.fullDate))}
                                    className="h-12 border-b border-gray-200 first:border-t "
                                ></div>
                            ))}

                            {(() => {
                                const today = new Date();
                                const todayIndex = (today.getDay() + 6) % 7;

                                if (dayIndex === todayIndex && day.date === formatDate(today)) {
                                    const hour = today.getHours();
                                    const minute = today.getMinutes();

                                    const baseHour = parseInt(timeSlots[0]?.split(':')[0]);
                                    const top = (hour - baseHour + minute / 60) * 3;

                                    return (
                                        <div
                                            className="absolute z-30"
                                            style={{
                                                top: `${top}rem`,
                                                height: '4px',
                                                left: '-0.25rem',
                                                width: 'calc(100% + 0.25rem)',
                                            }}
                                        >
                                            <div className="relative h-full w-full">
                                                {/* Thanh đỏ với đầu bo tròn bên phải */}
                                                <div className="absolute inset-0 bg-red-500 rounded-r-full"></div>

                                                {/* Nút tròn đỏ nằm ở bên phải */}
                                                <div className="absolute top-1/2 -translate-y-1/2 left-0 w-2 h-2 bg-red-500 rounded-full z-10"></div>

                                                {/* Hiển thị giờ ở giữa thanh */}
                                                {/* <div className="absolute left-1/2 -translate-x-1/2 text-xs text-white px-1 z-20">
                                                                                {`${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`}
                                                                            </div> */}
                                            </div>
                                        </div>
                                    );
                                }
                                return null;
                            })()}



                            {/* Render class blocks */}
                            {(() => {
                                // Get classes for this day - check for either old format or new format
                                const dayClasses = classes.filter(cls =>
                                    cls.class.status === 'LHD' &&
                                    cls.class.public == true &&
                                    (
                                        // New format: check if this day matches dayOfWeek1 or dayOfWeek2
                                        (cls.class.dayOfWeek1 === day.short && cls.class.startTime1 && cls.class.endTime1) ||
                                        (cls.class.dayOfWeek2 === day.short && cls.class.startTime2 && cls.class.endTime2) ||
                                        // Old format: backward compatibility
                                        (cls.class.dayOfWeek === day.short && cls.class.startTime && cls.class.endTime)
                                    )
                                );

                                // console.log(`Classes for ${day.long}:`, classes);

                                // Find overlapping classes
                                const overlappingGroups = [];

                                // Process each class - create new objects with formatted times for both sessions
                                const processedClasses = [];

                                dayClasses.forEach((cls) => {
                                    // Handle new format - only show session that matches current day
                                    if (cls.class.dayOfWeek1 === day.short && cls.class.startTime1 && cls.class.endTime1) {
                                        // Session 1 for this day
                                        const startTime1 = cls.class.startTime1 || "08:00:00";
                                        const endTime1 = cls.class.endTime1 || "09:00:00";
                                        const formattedStartTime1 = startTime1.substring(0, 5);
                                        const formattedEndTime1 = endTime1.substring(0, 5);

                                        processedClasses.push({
                                            ...cls.class,
                                            formattedStartTime: formattedStartTime1,
                                            formattedEndTime: formattedEndTime1,
                                            sessionNumber: 1,
                                            sessionId: `${cls.id}-session1`
                                        });
                                    }

                                    if (cls.class.dayOfWeek2 === day.short && cls.class.startTime2 && cls.class.endTime2) {
                                        // Session 2 for this day
                                        const startTime2 = cls.class.startTime2 || "10:00:00";
                                        const endTime2 = cls.class.endTime2 || "11:00:00";
                                        const formattedStartTime2 = startTime2.substring(0, 5);
                                        const formattedEndTime2 = endTime2.substring(0, 5);

                                        processedClasses.push({
                                            ...cls.class,
                                            formattedStartTime: formattedStartTime2,
                                            formattedEndTime: formattedEndTime2,
                                            sessionNumber: 2,
                                            sessionId: `${cls.class.id}-session2`
                                        });
                                    }
                                });

                                // Process each class
                                processedClasses.forEach((cls) => {
                                    const { formattedStartTime, formattedEndTime } = cls;

                                    const [startHour, startMin] = formattedStartTime.split(':').map(Number);
                                    const [endHour, endMin] = formattedEndTime.split(':').map(Number);

                                    // Convert to minutes for easier comparison
                                    const startMinutes = startHour * 60 + startMin;
                                    const endMinutes = endHour * 60 + endMin;

                                    // Check if this class overlaps with any existing group
                                    let foundGroup = false;

                                    for (const group of overlappingGroups) {
                                        // Check if this class overlaps with any class in the group
                                        const overlapsWithGroup = group.some(existingClass => {
                                            const { formattedStartTime: eFormattedStartTime, formattedEndTime: eFormattedEndTime } = existingClass;

                                            const [eStartHour, eStartMin] = eFormattedStartTime.split(':').map(Number);
                                            const [eEndHour, eEndMin] = eFormattedEndTime.split(':').map(Number);

                                            const eStartMinutes = eStartHour * 60 + eStartMin;
                                            const eEndMinutes = eEndHour * 60 + eEndMin;

                                            // Check for overlap
                                            return (
                                                (startMinutes < eEndMinutes && endMinutes > eStartMinutes) ||
                                                (eStartMinutes < endMinutes && eEndMinutes > startMinutes)
                                            );
                                        });

                                        if (overlapsWithGroup) {
                                            group.push(cls);
                                            foundGroup = true;
                                            break;
                                        }
                                    }

                                    // If no overlapping group found, create a new one
                                    if (!foundGroup) {
                                        overlappingGroups.push([cls]);
                                    }
                                });

                                // Render all classes with appropriate positioning
                                return overlappingGroups.flatMap((group, groupIndex) => {
                                    return group.map((cls, classIndex) => {
                                        const { formattedStartTime } = cls;
                                        const [sh, sm] = formattedStartTime.split(':').map(Number);
                                        const baseHour = parseInt(timeSlots[0].split(':')[0]);
                                        const top = (sh - baseHour + sm / 60) * 3;

                                        // Calculate width and left position based on number of overlapping classes
                                        const width = group.length > 1 ? `calc((100% - 0.5rem) / ${group.length})` : 'calc(100% - 0.5rem)';
                                        const left = group.length > 1 ? `calc(${classIndex} * (100% / ${group.length}))` : '0';

                                        // Determine color based on class name
                                        const lowerName = cls?.name?.toLowerCase();

                                        // Xác định khối
                                        let grade = null;
                                        if (lowerName?.includes('10')) grade = '10';
                                        else if (lowerName?.includes('11')) grade = '11';
                                        else if (lowerName?.includes('12')) grade = '12';

                                        // Bảng màu theo từng khối và loại lớp
                                        const colorMap = {
                                            '10': {
                                                đề: 'bg-blue-100 border-blue-500 text-blue-800',
                                                đại: 'bg-indigo-100 border-indigo-500 text-indigo-800',
                                                hình: 'bg-emerald-100 border-emerald-500 text-emerald-800',
                                            },
                                            '11': {
                                                đề: 'bg-orange-100 border-orange-500 text-orange-800',
                                                đại: 'bg-cyan-100 border-cyan-500 text-cyan-800', // đổi từ pink sang cyan
                                                hình: 'bg-teal-100 border-teal-500 text-teal-800',
                                            },
                                            '12': {
                                                đề: 'bg-purple-100 border-purple-500 text-purple-800',
                                                đại: 'bg-lime-100 border-lime-500 text-lime-800', // đổi từ rose sang lime
                                                hình: 'bg-amber-100 border-amber-500 text-amber-800',
                                            }
                                        };


                                        let colorClass;

                                        if (grade) {
                                            if (lowerName.includes('đề')) {
                                                colorClass = colorMap[grade]['đề'];
                                            } else if (lowerName.includes('đại')) {
                                                colorClass = colorMap[grade]['đại'];
                                            } else if (lowerName.includes('hình')) {
                                                colorClass = colorMap[grade]['hình'];
                                            }
                                        }

                                        // Nếu không thuộc khối nào hoặc không có từ khóa phù hợp → dùng màu mặc định
                                        if (!colorClass) {
                                            const defaultColors = [
                                                'bg-cyan-100 border-cyan-500 text-cyan-800',
                                                'bg-lime-100 border-lime-500 text-lime-800',
                                                'bg-sky-100 border-sky-500 text-sky-800',
                                                'bg-fuchsia-100 border-fuchsia-500 text-fuchsia-800',
                                                'bg-yellow-100 border-yellow-500 text-yellow-800',
                                            ];
                                            const colorIndex = group.length > 1 ? classIndex % defaultColors.length : groupIndex % defaultColors.length;
                                            colorClass = defaultColors[colorIndex];
                                        }

                                        const [bgColor, borderColor, textColor] = colorClass.split(' ');

                                        return (
                                            <div
                                                key={cls.id || `${groupIndex}-${classIndex}`}
                                                className={`absolute mx-1 ${bgColor} border-l-4 ${borderColor} rounded-r-md p-1 sm:p-2 overflow-hidden hover:brightness-95 transition-colors duration-150 cursor-pointer`}
                                                style={{
                                                    top: `${top}rem`,
                                                    height: getClassHeight(cls.formattedStartTime, cls.formattedEndTime),
                                                    width: width,
                                                    left: left
                                                }}
                                                onClick={() => alert(`Lớp: ${cls.name}${cls.sessionNumber ? ` (Buổi ${cls.sessionNumber})` : ''}\nThời gian: ${cls.formattedStartTime} - ${cls.formattedEndTime}\nTrạng thái: ${cls.status}`)}
                                            >
                                                <div className={`font-semibold text-xs sm:text-sm ${textColor}`}>
                                                    {cls.name}{cls.sessionNumber ? ` (${cls.sessionNumber})` : ''}
                                                </div>
                                                {!isMobile && (
                                                    <>
                                                        <div className="text-xs text-gray-700">{cls.formattedStartTime} - {cls.formattedEndTime}</div>
                                                        <div className="text-xs text-gray-500 mt-1">{cls.academicYear}</div>
                                                    </>
                                                )}

                                            </div>
                                        );
                                    });
                                });
                            })()}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )

}

export default WeekView;