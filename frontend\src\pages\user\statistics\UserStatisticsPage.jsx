import UserLayout from "../../../layouts/UserLayout";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchAttemptsByUser, setUserAttemptsPage, resetUserAttemptsPagination } from "../../../features/attempt/attemptSlice";
import AttemptStatisticsChart from "../../../components/chart/AttemptStatisticsChart";
import LoadingData from "../../../components/loading/LoadingData";
import Pagination from "../../../components/Pagination";
import { BarChart3, Calendar, Clock, Trophy, TrendingUp, FileText } from "lucide-react";
import { formatTimeDate } from "../../../utils/formatters";
import { useNavigate } from "react-router-dom";
// Component để hiển thị thông tin user
const UserStatsHeader = () => {
    const { user } = useSelector(state => state.auth);
    
    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-full overflow-hidden border border-gray-300 flex items-center justify-center">
                    {user?.avatarUrl ? (
                        <img
                            src={user.avatarUrl}
                            alt="avatar"
                            className="w-full h-full object-cover"
                        />
                    ) : (
                        <svg className="w-full h-full text-gray-400" viewBox="0 0 40 40" fill="none">
                            <path
                                d="M20 2.5C10.335 2.5 2.5 10.335 2.5 20C2.5 29.665 10.335 37.5 20 37.5C29.665 37.5 37.5 29.665 37.5 20C37.5 10.335 29.665 2.5 20 2.5ZM20 22.5C16.6983 22.5 14.1667 19.88 14.1667 16.6667C14.1667 13.4533 16.6983 10.8333 20 10.8333C23.3017 10.8333 25.8333 13.4533 25.8333 16.6667C25.8333 19.88 23.3017 22.5 20 22.5ZM10 32.3C10.1 30.38 10.8 29.03 11.73 28.07C12.72 27.05 14 26.41 15.2 26.03C15.41 25.96 15.73 26.01 16.09 26.26C16.88 26.8 18.25 27.5 20 27.5C21.75 27.5 23.12 26.8 23.91 26.26C24.27 26.01 24.59 25.96 24.8 26.03C26 26.41 27.28 27.05 28.27 28.07C29.2 29.03 29.9 30.39 30 32.29C27.18 34.59 23.65 35.84 20 35.83C16.35 35.84 12.82 34.58 10 32.3Z"
                                fill="#94A3B8"
                            />
                        </svg>
                    )}
                </div>
                <div className="flex-1">
                    <h1 className="text-xl font-semibold text-gray-900">
                        Thống kê lượt làm bài
                    </h1>
                    <p className="text-gray-600 mt-1">
                        {user?.lastName} {user?.firstName} - @{user?.username}
                    </p>
                </div>
            </div>
        </div>
    );
};

// Component để hiển thị biểu đồ thống kê
const StatisticsChartSection = () => {
    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-6">
                <BarChart3 size={20} className="text-cyan-600" />
                <h2 className="text-lg font-semibold text-gray-900">Biểu đồ thống kê</h2>
            </div>
            <AttemptStatisticsChart />
        </div>
    );
};

// Component card hiển thị mỗi lượt làm bài
const AttemptCard = ({ attempt }) => {
    const navigate = useNavigate(); // Import useNavigate hook from react-router-dom
    
    const getExamTypeColor = (type) => {
        switch (type) {
            case 'KIEM_TRA':
                return 'bg-blue-100 text-blue-700 border-blue-200';
            case 'BAI_TAP':
                return 'bg-green-100 text-green-700 border-green-200';
            case 'THI':
                return 'bg-red-100 text-red-700 border-red-200';
            default:
                return 'bg-gray-100 text-gray-700 border-gray-200';
        }
    };

    const getScoreColor = (score) => {
        if (score >= 8) return 'text-green-600 font-semibold';
        if (score >= 6.5) return 'text-blue-600 font-semibold';
        if (score >= 5) return 'text-yellow-600 font-semibold';
        return 'text-red-600 font-semibold';
    };

    // Safely access exam data
    const examName = attempt.exam?.name || 'Đề thi không xác định';
    const examType = attempt.exam?.typeOfExam || 'Không xác định';
    const testDuration = attempt.exam?.testDuration;
    const startTime = attempt.startTime;
    const endTime = attempt.endTime;
    const score = attempt.score;
    const duration = attempt.duration;

    return (
        <div
        onClick={() => navigate(`/practice/exam/attempt/${attempt.id}/score`)}
        className="bg-white cursor-pointer rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md hover:border-cyan-300 transition-all duration-200">
            <div className="flex flex-col gap-3">
                {/* Header */}
                <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-900 text-sm mb-2 truncate" title={examName}>
                            {examName}
                        </h3>
                        <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getExamTypeColor(examType)}`}>
                                {examType}
                            </span>
                        </div>
                    </div>
                    <div className="text-right ml-2">
                        <div className={`text-lg font-bold ${getScoreColor(score)}`}>
                            {score !== null ? `${score}/10` : 'Chưa có điểm'}
                        </div>
                    </div>
                </div>

                {/* Details */}
                <div className="grid grid-cols-1 gap-2 text-xs text-gray-600">
                    <div className="flex items-center gap-2">
                        <Clock size={14} className="flex-shrink-0" />
                        <span>
                            Thời lượng quy định: {testDuration ? `${testDuration} phút` : 'Không giới hạn'}
                        </span>
                    </div>
                    <div className="flex items-center gap-2">
                        <Calendar size={14} className="flex-shrink-0" />
                        <span>
                            Thời gian làm: {duration || 'Chưa hoàn thành'}
                        </span>
                    </div>
                    <div className="flex items-center gap-2">
                        <TrendingUp size={14} className="flex-shrink-0" />
                        <span>
                            Bắt đầu: {startTime ? formatTimeDate(startTime) : 'Chưa xác định'}
                        </span>
                    </div>
                    <div className="flex items-center gap-2">
                        <Trophy size={14} className="flex-shrink-0" />
                        <span>
                            Kết thúc: {endTime ? formatTimeDate(endTime) : 'Chưa hoàn thành'}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );
};

// Component hiển thị danh sách lượt làm bài
const AttemptsListSection = () => {
    const dispatch = useDispatch();
    const { attempts, loadingAttempt, userAttemptsPagination } = useSelector(state => state.attempts);
    const { currentPage, totalItems, limit, totalPages } = userAttemptsPagination;

    useEffect(() => {
        dispatch(fetchAttemptsByUser({ currentPage }));
    }, [dispatch, currentPage]);

    const handlePageChange = (page) => {
        dispatch(setUserAttemptsPage(page));
    };

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                <div className="flex items-center gap-2">
                    <FileText size={20} className="text-green-600" />
                    <h2 className="text-lg font-semibold text-gray-900">Lịch sử làm bài</h2>
                    {totalItems > 0 && (
                        <span className="bg-gray-100 text-gray-700 text-sm px-2 py-1 rounded-full">
                            {totalItems} lượt
                        </span>
                    )}
                </div>
                
                {/* Hiển thị thông tin phân trang */}
                {totalItems > 0 && (
                    <div className="text-sm text-gray-600">
                        Hiển thị {Math.min((currentPage - 1) * limit + 1, totalItems)}-{Math.min(currentPage * limit, totalItems)} trong {totalItems} kết quả
                    </div>
                )}
            </div>

            <LoadingData
                loading={loadingAttempt}
                isNoData={attempts.length === 0}
                loadText="Đang tải danh sách lượt làm bài..."
                noDataText="Bạn chưa có lượt làm bài nào."
                IconNoData={FileText}
            >
                <div className="space-y-4">
                    {/* Grid layout for attempt cards - responsive */}
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-4">
                        {attempts.map((attempt, index) => (
                            <AttemptCard key={attempt.id || index} attempt={attempt} />
                        ))}
                    </div>
                    
                    {/* Pagination */}
                    {totalItems > limit && (
                        <div className="mt-6 flex justify-center">
                            <Pagination
                                totalItems={totalItems}
                                currentPage={currentPage}
                                limit={limit}
                                onPageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </LoadingData>
        </div>
    );
};

const UserStatisticsPage = () => {
    const dispatch = useDispatch();

    // Reset pagination khi component mount
    useEffect(() => {
        dispatch(resetUserAttemptsPagination());
    }, [dispatch]);

    return (
        <UserLayout>
            <div className="container mx-auto px-4 py-6">
                <div className="max-w-6xl mx-auto space-y-6">
                    {/* Header Section */}
                    <UserStatsHeader />
                    
                    {/* Statistics Chart Section */}
                    <StatisticsChartSection />
                    
                    {/* Attempts List Section */}
                    <AttemptsListSection />
                </div>
            </div>
        </UserLayout>
    );
};

export default UserStatisticsPage;
