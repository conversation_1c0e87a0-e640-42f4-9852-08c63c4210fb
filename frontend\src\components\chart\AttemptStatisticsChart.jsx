import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { fetchUserAttemptStatistics } from '../../features/attempt/attemptSlice';
import { BarChart3, TrendingUp, Calendar, ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';
import LoadingData from '../loading/LoadingData';

// Register Chart.js components
ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend
);

const AttemptStatisticsChart = () => {
    const dispatch = useDispatch();
    const { statistics } = useSelector(state => state.attempts);
    const { chartData, loadingChart } = statistics;

    const [period, setPeriod] = useState('week');
    const [type, setType] = useState('count');
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const dropdownRef = useRef(null);

    // Fetch data when component mounts or filters change
    useEffect(() => {
        const dateString = selectedDate.toISOString().split('T')[0]; // Format: YYYY-MM-DD
        dispatch(fetchUserAttemptStatistics({ period, type, date: dateString }));
    }, [dispatch, period, type, selectedDate]);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setDropdownOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Navigation functions
    const navigatePeriod = (direction) => {
        const newDate = new Date(selectedDate);

        if (period === 'week') {
            newDate.setDate(selectedDate.getDate() + (direction * 7));
        } else if (period === 'month') {
            newDate.setMonth(selectedDate.getMonth() + direction);
        } else if (period === 'year') {
            newDate.setFullYear(selectedDate.getFullYear() + direction);
        }

        setSelectedDate(newDate);
    };

    // Format display label for selected period
    const getDisplayLabel = () => {
        const vietnameseMonths = [
            'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
            'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
        ];

        if (period === 'week') {
            // Calculate week range
            const currentDay = selectedDate.getDay();
            const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1;
            const startOfWeek = new Date(selectedDate);
            startOfWeek.setDate(selectedDate.getDate() - daysFromMonday);
            const endOfWeek = new Date(startOfWeek);
            endOfWeek.setDate(startOfWeek.getDate() + 6);

            return `Tuần ${startOfWeek.getDate()}/${startOfWeek.getMonth() + 1} - ${endOfWeek.getDate()}/${endOfWeek.getMonth() + 1}/${endOfWeek.getFullYear()}`;
        } else if (period === 'month') {
            return `${vietnameseMonths[selectedDate.getMonth()]} ${selectedDate.getFullYear()}`;
        } else if (period === 'year') {
            return `Năm ${selectedDate.getFullYear()}`;
        }

        return '';
    };

    // Check if it's current period
    const isCurrentPeriod = () => {
        const today = new Date();

        if (period === 'week') {
            // Check if selected date is in current week
            const currentDay = today.getDay();
            const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1;
            const startOfCurrentWeek = new Date(today);
            startOfCurrentWeek.setDate(today.getDate() - daysFromMonday);
            const endOfCurrentWeek = new Date(startOfCurrentWeek);
            endOfCurrentWeek.setDate(startOfCurrentWeek.getDate() + 6);

            return selectedDate >= startOfCurrentWeek && selectedDate <= endOfCurrentWeek;
        } else if (period === 'month') {
            return selectedDate.getMonth() === today.getMonth() &&
                selectedDate.getFullYear() === today.getFullYear();
        } else if (period === 'year') {
            return selectedDate.getFullYear() === today.getFullYear();
        }

        return false;
    };

    // Utility function to format date
    const formatDate = (dateString) => {
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;

            const day = date.getDate();
            const month = date.getMonth() + 1;
            return `${day}/${month}`;
        } catch {
            return dateString;
        }
    };

    // Utility function to get day name
    const getDayName = (dateString) => {
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '';

            const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
            return dayNames[date.getDay()];
        } catch {
            return '';
        }
    };

    // Format labels based on period
    const formatLabels = (statistics) => {
        if (!statistics) return [];

        return statistics.map((item, index) => {
            const label = item.label;

            if (period === 'week') {
                // Backend trả về label là "Thứ 2", "Thứ 3"... và date là "2025-08-05"
                if (item.date) {
                    // Sử dụng date từ backend để format
                    const dateFormatted = formatDate(item.date);
                    return [label, dateFormatted]; // Return array for 2 lines
                } else if (label.toLowerCase().includes('thứ') || label.toLowerCase().includes('chủ nhật')) {
                    // Fallback: tính toán date nếu backend không trả về
                    const today = new Date();
                    const currentDay = today.getDay(); // 0 = Chủ nhật, 1 = Thứ 2, ..., 6 = Thứ 7
                    const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // Chủ nhật = 6 ngày từ thứ 2
                    const startOfWeek = new Date(today);
                    startOfWeek.setDate(today.getDate() - daysFromMonday); // Thứ 2 đầu tuần

                    // Map day names to indices (0 = Thứ 2, 6 = Chủ nhật)
                    const dayMapping = {
                        'thứ 2': 0, 'thứ hai': 0, 'monday': 0,
                        'thứ 3': 1, 'thứ ba': 1, 'tuesday': 1,
                        'thứ 4': 2, 'thứ tư': 2, 'wednesday': 2,
                        'thứ 5': 3, 'thứ năm': 3, 'thursday': 3,
                        'thứ 6': 4, 'thứ sáu': 4, 'friday': 4,
                        'thứ 7': 5, 'thứ bảy': 5, 'saturday': 5,
                        'chủ nhật': 6, 'sunday': 6
                    };

                    const dayKey = label.toLowerCase();
                    const dayIndex = dayMapping[dayKey];

                    if (dayIndex !== undefined) {
                        const targetDate = new Date(startOfWeek);
                        targetDate.setDate(startOfWeek.getDate() + dayIndex);
                        const day = targetDate.getDate();
                        const month = targetDate.getMonth() + 1;
                        return [label, `${day}/${month}`]; // Return array for 2 lines
                    }
                }

                return [label, ''];
            } else if (period === 'month') {
                // For month view, handle week labels with real date ranges
                if (item.weekStart && item.weekEnd) {
                    // Backend trả về weekStart và weekEnd
                    const today = new Date();
                    const currentMonth = today.getMonth() + 1;

                    return [label, `${item.weekStart}/${currentMonth} đến ${item.weekEnd}/${currentMonth}`]; // Return array for 2 lines
                } else if (item.week) {
                    // Fallback: Backend trả về week number, tính toán date range
                    const today = new Date();
                    const currentMonth = today.getMonth() + 1;
                    const currentYear = today.getFullYear();

                    // Tính ngày đầu và cuối tuần theo logic 7 ngày
                    const weekStart = (item.week - 1) * 7 + 1;
                    const lastDayOfMonth = new Date(currentYear, currentMonth, 0).getDate();
                    const weekEnd = Math.min(item.week * 7, lastDayOfMonth);

                    return [label, `${weekStart}/${currentMonth} đến ${weekEnd}/${currentMonth}`]; // Return array for 2 lines
                }
                return [label, ''];
            } else {
                // For year view, format months better
                if (item.month) {
                    const today = new Date();
                    return [`Tháng ${item.month}`, `${today.getFullYear()}`]; // Return array for 2 lines
                }
                return [label, ''];
            }
        });
    };

    // Prepare chart data
    const prepareChartData = () => {
        if (!chartData || !chartData.statistics) return null;

        const labels = formatLabels(chartData.statistics);
        const values = chartData.statistics.map(item => item.value);

        return {
            labels,
            datasets: [
                {
                    label: chartData.queryType,
                    data: values,
                    borderColor: type === 'count' ? '#06b6d4' : '#10b981',
                    backgroundColor: type === 'count' ? '#06b6d4' : '#10b981',
                    fill: false,
                    tension: 0.4,
                    pointBackgroundColor: '#ffffff',
                    pointBorderColor: type === 'count' ? '#06b6d4' : '#10b981',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    pointHoverBackgroundColor: type === 'count' ? '#06b6d4' : '#10b981',
                    pointHoverBorderColor: '#ffffff',
                    pointHoverBorderWidth: 2,
                }
            ]
        };
    };

    // Chart options
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: '#374151',
                titleColor: '#f9fafb',
                bodyColor: '#f9fafb',
                borderColor: '#6b7280',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: false,
                callbacks: {
                    title: (context) => {
                        const index = context[0].dataIndex;
                        const originalLabel = chartData?.statistics[index]?.label;
                        const formattedLabel = context[0].label;

                        // Add period info to title
                        const periodInfo = `${getDisplayLabel()}`;

                        // Handle array labels (2 lines)
                        let displayLabel = '';
                        if (Array.isArray(formattedLabel)) {
                            displayLabel = formattedLabel.join(' - ');
                        } else {
                            displayLabel = formattedLabel;
                        }

                        // Show both formatted and original if different
                        if (originalLabel && originalLabel !== displayLabel && !displayLabel.includes(originalLabel)) {
                            return `${displayLabel}\n(${originalLabel})\n📅 ${periodInfo}`;
                        }
                        return `${displayLabel}\n📅 ${periodInfo}`;
                    },
                    label: (context) => {
                        const value = context.parsed.y;
                        return `${chartData?.queryType}: ${value}${type === 'score' ? ' điểm' : ' lượt'}`;
                    }
                }
            }
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    color: '#6b7280',
                    font: {
                        size: period === 'week' ? 9 : period === 'month' ? 10 : 11
                    },
                    maxRotation: 0, // Keep labels horizontal for better 2-line display
                    minRotation: 0,
                    callback: function (value, index) {
                        const label = this.getLabelForValue(value);

                        // Handle array labels (2 lines)
                        if (Array.isArray(label)) {
                            // For mobile screens, show only first line
                            if (window.innerWidth < 640) {
                                return label[0]; // Just the first line (e.g., "Thứ 2" or "Tuần 1")
                            }
                            // For larger screens, Chart.js will automatically handle array as multi-line
                            return label;
                        }

                        // Handle string labels (fallback)
                        if (window.innerWidth < 640 && label.length > 12) {
                            if (period === 'week') {
                                const parts = label.split(' - ');
                                return parts[0];
                            } else if (period === 'month') {
                                return label.substring(0, 8) + '...';
                            }
                        }

                        return label;
                    }
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: '#f3f4f6',
                    borderDash: [2, 2]
                },
                ticks: {
                    color: '#6b7280',
                    font: {
                        size: 12
                    },
                    callback: function (value) {
                        return Number.isInteger(value) ? value : '';
                    }
                }
            }
        }
    };

    const periodOptions = [
        { value: 'week', label: 'Tuần', icon: Calendar },
        { value: 'month', label: 'Tháng', icon: Calendar },
        { value: 'year', label: 'Năm', icon: Calendar }
    ];

    const typeOptions = [
        { value: 'count', label: 'Số lượt làm bài', icon: BarChart3, color: 'text-cyan-600' },
        { value: 'score', label: 'Điểm trung bình', icon: TrendingUp, color: 'text-green-600' }
    ];

    const selectedPeriod = periodOptions.find(p => p.value === period);
    const selectedType = typeOptions.find(t => t.value === type);

    return (
        <div className="flex flex-col gap-3">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                <h3 className="text-gray-900 font-semibold flex items-center gap-2">
                    <BarChart3 size={18} />
                    Thống kê làm bài
                </h3>

                {/* Controls */}
                <div className="flex flex-col sm:flex-row gap-3">

                    <div className="flex gap-2">
                        {/* Type Toggle */}
                        <div className="flex bg-gray-100 rounded-md p-1">
                            {typeOptions.map((option) => (
                                <button
                                    key={option.value}
                                    onClick={() => setType(option.value)}
                                    className={`flex items-center gap-1 px-3 py-1 text-xs font-medium rounded transition-colors ${type === option.value
                                        ? 'bg-white text-gray-900 shadow-sm'
                                        : 'text-gray-600 hover:text-gray-900'
                                        }`}
                                >
                                    <option.icon size={14} />
                                    <span className="hidden sm:inline">{option.label}</span>
                                    <span className="sm:hidden">{option.value === 'count' ? 'Lượt' : 'Điểm'}</span>
                                </button>
                            ))}
                        </div>

                        {/* Period Dropdown */}
                        <div className="relative" ref={dropdownRef}>
                            <button
                                onClick={() => setDropdownOpen(!dropdownOpen)}
                                className="flex items-center gap-2 px-3 py-1.5 bg-white border border-gray-200 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                            >
                                <selectedPeriod.icon size={14} />
                                <span>{selectedPeriod.label}</span>
                                <ChevronDown size={14} className={`transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />
                            </button>

                            {dropdownOpen && (
                                <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[100px]">
                                    {periodOptions.map((option) => (
                                        <button
                                            key={option.value}
                                            onClick={() => {
                                                setPeriod(option.value);
                                                setDropdownOpen(false);
                                            }}
                                            className={`w-full flex items-center gap-2 px-3 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${period === option.value ? 'text-cyan-700 bg-cyan-50' : 'text-gray-700'
                                                }`}
                                        >
                                            <option.icon size={14} />
                                            {option.label}
                                        </button>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>


            </div>
            {/* Period Navigation */}
            <div className="flex md:flex-row flex-col items-center gap-2 bg-white border border-gray-200 rounded-md px-3 py-2">
                <div className="flex-1 w-full flex justify-between items-center flex-row">
                    <button
                        onClick={() => navigatePeriod(-1)}
                        className="p-1 hover:bg-gray-100 rounded transition-colors"
                        title={`${period === 'week' ? 'Tuần' : period === 'month' ? 'Tháng' : 'Năm'} trước`}
                    >
                        <ChevronLeft size={18} />
                    </button>
    
                    <div className="w-fit flex flex-col items-center min-w-[140px]">
                        <span className="text-sm font-medium text-gray-900">
                            {getDisplayLabel()}
                        </span>
                        {isCurrentPeriod() && (
                            <span className="text-xs text-cyan-600 font-medium">
                                (Hiện tại)
                            </span>
                        )}
                    </div>
    
                    <button
                        onClick={() => navigatePeriod(1)}
                        className="p-1 hover:bg-gray-100 rounded transition-colors"
                        title={`${period === 'week' ? 'Tuần' : period === 'month' ? 'Tháng' : 'Năm'} sau`}
                    >
                        <ChevronRight size={18} />
                    </button>
                </div>

                <div className="w-px h-6 bg-gray-200 mx-2 md:block hidden"></div>

                <div className='flex gap-2'>
                    <button
                        onClick={() => setSelectedDate(new Date())}
                        className="text-xs px-2 py-1 bg-cyan-50 text-cyan-700 rounded hover:bg-cyan-100 transition-colors"
                        title="Về hiện tại"
                    >
                        Hôm nay
                    </button>
    
                    <input
                        type="date"
                        value={selectedDate.toISOString().split('T')[0]}
                        onChange={(e) => setSelectedDate(new Date(e.target.value))}
                        className="text-xs px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500"
                        title="Chọn ngày cụ thể"
                    />
                </div>
            </div>

            {/* Chart Container */}
            <div className="bg-white border border-gray-200 rounded-md p-4">
                <LoadingData
                    loading={loadingChart}
                    isNoData={!chartData || !chartData.statistics || chartData.statistics.length === 0}
                    loadText="Đang tải dữ liệu thống kê"
                    noDataText="Chưa có dữ liệu thống kê"
                    IconNoData={BarChart3}
                >
                    <div className="h-64 sm:h-80">
                        {prepareChartData() && (
                            <Line data={prepareChartData()} options={chartOptions} />
                        )}
                    </div>

                    {/* Summary */}
                    {chartData && chartData.summary && (
                        <div className="mt-4 pt-4 border-t border-gray-200">
                            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 text-center">
                                <div className="flex flex-col gap-1">
                                    <p className="text-xs text-gray-500">Tổng cộng</p>
                                    <p className={`text-lg font-semibold ${selectedType.color}`}>
                                        {chartData.summary.total}{type === 'score' ? ' điểm' : ' lượt'}
                                    </p>
                                </div>
                                <div className="flex flex-col gap-1">
                                    <p className="text-xs text-gray-500">Trung bình</p>
                                    <p className="text-lg font-semibold text-gray-700">
                                        {chartData.summary.average}{type === 'score' ? ' điểm' : ' lượt'}
                                    </p>
                                </div>
                                <div className="flex flex-col gap-1 col-span-2 sm:col-span-1">
                                    <p className="text-xs text-gray-500">Ngày có dữ liệu</p>
                                    <p className="text-lg font-semibold text-gray-700">
                                        {chartData.summary.totalRecords}
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}
                </LoadingData>
            </div>
        </div>
    );
};

export default AttemptStatisticsChart;
