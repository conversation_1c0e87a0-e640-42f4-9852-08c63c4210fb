import db from "../models/index.js";
import { calculateScoreQuestion } from "./exam.service.js";
import { updateAverageScore } from "./user.service.js";

/**
 * Service để chấm lại bài thi cho một attemptId
 * @param {number} attemptId - ID của lượt làm bài
 * @param {object} transaction - Transaction (optional)
 * @returns {Promise<object>} - Kết quả chấm lại
 */
export const reExaminationService = async (attemptId, transaction = null) => {
    const shouldCommit = !transaction;
    const t = transaction || await db.sequelize.transaction();

    try {
        // Tìm attempt
        const attempt = await db.StudentExamAttempt.findByPk(attemptId, { transaction: t });
        if (!attempt) {
            throw new Error("Không tìm thấy lượt làm bài!");
        }

        // Lấy tất cả answers
        const answers = await db.Answer.findAll({
            where: { attemptId },
            include: [{ model: db.Question, attributes: ["typeOfQuestion"] }],
            transaction: t,
        });

        let countUpdated = 0;
        let newScore = 0;

        // Thu thập statementIds cho TN và DS
        const tnStatementIds = answers
            .filter(a => a.Question?.typeOfQuestion === "TN")
            .map(a => a.answerContent);

        const dsStatementIds = answers
            .filter(a => a.Question?.typeOfQuestion === "DS")
            .flatMap(a => {
                try {
                    const parsed = JSON.parse(a.answerContent);
                    return Array.isArray(parsed) ? parsed.map(ans => ans.statementId) : [];
                } catch (err) {
                    console.warn("❗ answerContent không hợp lệ JSON:", a.answerContent);
                    return [];
                }
            });

        const allStatementIds = [...new Set([...tnStatementIds, ...dsStatementIds])];
        const scoreQuestion = await calculateScoreQuestion(attempt.examId);
        const allStatements = await db.Statement.findAll({
            where: { id: allStatementIds },
            transaction: t,
        });

        const statementMap = Object.fromEntries(allStatements.map(s => [s.id.toString(), s.isCorrect]));

        // Chấm từng câu trả lời
        for (const answer of answers) {
            const question = answer.Question;

            if (question?.typeOfQuestion === "TN") {
                if (!answer.answerContent) continue;
                const isCorrect = statementMap[answer.answerContent] === true;

                if (answer.result !== isCorrect) {
                    await answer.update({ result: isCorrect }, { transaction: t });
                    countUpdated++;
                }
                if (isCorrect) {
                    newScore += scoreQuestion.TN;
                }

            } else if (question?.typeOfQuestion === "DS") {
                if (!answer.answerContent) continue;
                const ans = JSON.parse(answer.answerContent);
                const correctCount = ans.filter(a => statementMap[a.statementId] === a.answer).length;
                
                let shouldBeCorrect = false;
                if (correctCount === 1) {
                    newScore += 0.1;
                } else if (correctCount === 2) {
                    newScore += 0.25;
                } else if (correctCount === 3) {
                    newScore += 0.5;
                } else if (correctCount >= 4) {
                    newScore += 1.0;
                    shouldBeCorrect = true;
                }

                if (answer.result !== shouldBeCorrect) {
                    await answer.update({ result: shouldBeCorrect }, { transaction: t });
                    countUpdated++;
                }

            } else if (question?.typeOfQuestion === "TLN") {
                const fullQuestion = await db.Question.findByPk(answer.questionId, { transaction: t });
                if (!fullQuestion || !answer.answerContent) continue;

                const studentAns = answer.answerContent.trim().replace(',', '.');
                const correctAns = fullQuestion.correctAnswer?.trim().replace(',', '.');
                const check = studentAns === correctAns;

                if (answer.result !== check) {
                    await answer.update({ result: check }, { transaction: t });
                    countUpdated++;
                }

                if (check) {
                    newScore += scoreQuestion.TLN;
                }
            }
        }

        // Cập nhật điểm số
        await attempt.update({ score: newScore }, { transaction: t });
        
        // Commit transaction nếu được tạo trong service này
        if (shouldCommit) {
            await t.commit();
        }

        // Cập nhật điểm trung bình (ngoài transaction)
        await updateAverageScore(attempt.studentId);

        return {
            success: true,
            countUpdated,
            totalAnswers: answers.length,
            newScore,
            attemptId
        };

    } catch (error) {
        if (shouldCommit) {
            await t.rollback();
        }
        throw error;
    }
};