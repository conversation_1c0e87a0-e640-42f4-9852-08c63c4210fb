'use strict';

export default {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.createTable('AdminActivityLog', {
            id: {
                allowNull: false,
                autoIncrement: true,
                primaryKey: true,
                type: Sequelize.INTEGER,
            },
            adminId: {
                allowNull: false,
                type: Sequelize.INTEGER,
                references: {
                    model: 'user',
                    key: 'id',
                },
                onDelete: 'CASCADE',
            },
            action: {
                allowNull: false,
                type: Sequelize.STRING,
            },
            targetId: {
                allowNull: true,
                type: Sequelize.INTEGER,
            },
            description: {
                allowNull: true,
                type: Sequelize.TEXT,
            },
            createdAt: {
                allowNull: false,
                type: Sequelize.DATE,
            },
            updatedAt: {
                allowNull: false,
                type: Sequelize.DATE,
            }
        });
    },
    down: async (queryInterface) => {
        await queryInterface.dropTable('AdminActivityLog');
    },
};
