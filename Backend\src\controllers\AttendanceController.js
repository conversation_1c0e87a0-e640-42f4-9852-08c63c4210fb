import * as AttendanceService from '../services/attendance.service.js';
import { findBTVN } from '../services/lesson.service.js';
import * as logAdminActivity from '../services/admin.activity.log.service.js';
import ActionAdmin from '../constants/ActionAdmin.js';
import FEEDBACK from '../constants/feedbackConstants.js';

/**
 * [GET] /api/attendances
 * Query: ?search=&page=&limit=&sortOrder=
 */
export const getAllAttendances = async (req, res) => {
    const { lessonId, status, tuition } = req.query;
    const { search = '', page = 1, limit = 10, sortOrder = 'DESC' } = req.query;
    const { month } = req.query;
    const { btvnList } = req.query;
    // console.log(lessonId)

    const result = await AttendanceService.getAllAttendances(lessonId, {
        search,
        status,
        isPaid: tuition,
        page: parseInt(page),
        limit: parseInt(limit),
        sortOrder: sortOrder.toUpperCase()
    }, btvnList, month);

    return res.status(200).json(result);
};

/**
 * [POST] /api/attendances
 * Body: { userId, lessonId, status }
 */
export const createAttendance = async (req, res) => {
    const { userId, lessonId, classId, status, note } = req.body;
    const adminId = req.user.id;
    const attendance = await AttendanceService.createAttendance(userId, lessonId, classId, status, note);

    await logAdminActivity.logAdminActivity(
        adminId,
        ActionAdmin.CREATE_ATTENDANCE,
        attendance.id,
        `Tạo điểm danh cho học sinh ${attendance.userId} trong bài học ${attendance.lessonId}`
    );

    return res.status(201).json({
        message: 'Tạo điểm danh thành công',
        data: attendance
    });
};

/**
 * [POST] /api/attendances/bulk
 * Body: { classId, lessonId }
 */
export const createAllAttendanceInClass = async (req, res) => {

    const { classId, lessonId } = req.body;

    const result = await AttendanceService.createAllAttendanceInClass(classId, lessonId);
    const adminId = req.user.id;

    await logAdminActivity.logAdminActivity(
        adminId,
        ActionAdmin.CREATE_ATTENDANCE,
        lessonId,
        `Tạo điểm danh cho tất cả học sinh trong bài học ${lessonId}`
    );

    return res.status(201).json({
        message: `Tạo điểm danh cho ${result.length} học sinh thành công`,
        data: result,
        count: result.length
    });
};

/**
 * [PUT] /api/attendances/:id
 * Body: { status, note }
 */
export const updateAttendance = async (req, res) => {
    const { id } = req.params;
    const updateData = req.body || {};

    const updated = await AttendanceService.updateAttendance(id, updateData);
    const adminId = req.user.id;

    await logAdminActivity.logAdminActivity(
        adminId,
        ActionAdmin.UPDATE_ATTENDANCE,
        id,
        `Cập nhật điểm danh ${id} sang ${updateData?.status} thành công `
    );

    return res.status(200).json({
        message: 'Cập nhật điểm danh thành công',
        data: updated
    });
};

/**
 * [DELETE] /api/attendances/:id
 */
export const deleteAttendance = async (req, res) => {
    const { id } = req.params;

    const deletedId = await AttendanceService.deleteAttendance(id);
    const adminId = req.user.id;

    await logAdminActivity.logAdminActivity(
        adminId,
        ActionAdmin.DELETE_ATTENDANCE,
        id,
        `Xóa điểm danh ${id} thành công `
    );

    return res.status(200).json({
        message: 'Xóa điểm danh thành công',
        id: deletedId
    });
};

/**
 * [GET] /api/attendances/user/:userId
 * Get all attendances for a user grouped by month
 * Query: ?year=2024&month=1 (optional filters)
 */
export const getUserAttendances = async (req, res) => {
    const { id } = req.user;
    const { year, month } = req.query;

    const result = await AttendanceService.getUserAttendances(id, { year, month });

    return res.status(200).json({
        message: 'Lấy danh sách điểm danh thành công',
        data: result
    });
};

export const getUserAttendancesByAdmin = async (req, res) => {
    const { userId } = req.params;
    const { year, month } = req.query;

    const result = await AttendanceService.getUserAttendances(userId, { year, month });

    // console.log('result', result);

    return res.status(200).json({
        message: 'Lấy danh sách điểm danh thành công',
        data: result
    });
}

/**
 * [GET] /api/attendances/statistics/lesson/:lessonId
 * Get attendance statistics for a specific lesson
 */
export const getLessonAttendanceStatistics = async (req, res) => {
    const { lessonId } = req.params;

    const result = await AttendanceService.getLessonAttendanceStatistics(lessonId);

    return res.status(200).json({
        message: 'Lấy thống kê điểm danh buổi học thành công',
        data: result
    });
};

/**
 * [GET] /api/attendances/statistics/class/:classId
 * Get attendance statistics for a specific class
 * Query: ?startDate=2024-01-01&endDate=2024-12-31 (optional filters)
 */
export const getClassAttendanceStatistics = async (req, res) => {
    const { classId } = req.params;
    const { startDate, endDate } = req.query;

    const result = await AttendanceService.getClassAttendanceStatistics(classId, { startDate, endDate });

    return res.status(200).json({
        message: 'Lấy thống kê điểm danh lớp học thành công',
        data: result
    });
};

/**
 * [GET] /api/attendances/statistics/overall
 * Get overall attendance statistics
 * Query: ?startDate=2024-01-01&endDate=2024-12-31&classId=1 (optional filters)
 */
export const getOverallAttendanceStatistics = async (req, res) => {
    const { startDate, endDate, classId } = req.query;

    const result = await AttendanceService.getOverallAttendanceStatistics({ startDate, endDate, classId });

    return res.status(200).json({
        message: 'Lấy thống kê điểm danh tổng quan thành công',
        data: result
    });
};

/**
 * [PUT] /api/attendances/bulk-update/:lessonId
 * Bulk update attendance status for multiple students in a lesson
 * Body: { updates: [{ userId, status, note? }] }
 */
export const bulkUpdateAttendanceStatus = async (req, res) => {
    const { lessonId } = req.params;
    const { updates } = req.body;

    const result = await AttendanceService.bulkUpdateAttendanceStatus(lessonId, updates);

    return res.status(200).json({
        message: `Cập nhật hàng loạt thành công. ${result.successCount} bản ghi được xử lý${result.errorCount > 0 ? `, ${result.errorCount} lỗi xảy ra` : ''}`,
        data: result
    });
};

/**
 * [PUT] /api/attendances/mark-all/:lessonId
 * Mark all students in a lesson with the same status
 * Body: { status, note? }
 */
export const markAllAttendanceStatus = async (req, res) => {
    const { lessonId } = req.params;
    const { status, note } = req.body;

    const result = await AttendanceService.markAllAttendanceStatus(lessonId, status, note);
    const adminId = req.user.id;
    await logAdminActivity.logAdminActivity(
        adminId,
        ActionAdmin.MARK_ALL_ATTENDANCE,
        lessonId,
        `Đánh dấu tất cả học sinh trong bài học ${lessonId} là ${status} thành công `
    );
    return res.status(200).json({
        message: `Đánh dấu tất cả học sinh là ${status} thành công. ${result.successCount} bản ghi được xử lý${result.errorCount > 0 ? `, ${result.errorCount} lỗi xảy ra` : ''}`,
        data: result
    });

};


