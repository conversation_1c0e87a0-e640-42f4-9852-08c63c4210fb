import db from '../models/index.js';
const { Attendance, User, Lesson, Class, LearningItem, StudentStudyStatus, StudentExamAttempt } = db;
import { Op, where, literal } from 'sequelize';
import AttendanceStatus from '../constants/AttendanceStatus.js';
import ResponseDataPagination from '../dtos/responses/pagination/PaginationResponse.js';
import responseAttendance from '../dtos/responses/attendance/AttendanceResponse.js';
import { getAllStudentInClasses } from './class.service.js';
import UserType from '../constants/UserType.js';
import * as classService from './class.service.js';
import * as lessonService from './lesson.service.js';
import { getTuitionStatusesByUserIds } from './tuition.service.js';
import FEEDBACK from '../constants/feedBackAuto.js';

// Hàm tạo feedback dựa trên điểm số <PERSON>VN
const getBtvnFeedback = (score) => {
    if (score === null || score === undefined) {
        return 'Chưa hoàn thành bài tập về nhà';
    }
    
    if (score < 6) {
        return FEEDBACK.HOMEWORK.BELOW_6;
    } else if (score >= 6 && score < 8) {
        return FEEDBACK.HOMEWORK.FROM_6_TO_8;
    } else if (score >= 8 && score < 9) {
        return FEEDBACK.HOMEWORK.FROM_8_TO_9;
    } else {
        return FEEDBACK.HOMEWORK.ABOVE_9;
    }
};

export const getAllAttendances = async (
    lessonId,
    { search = '', status = '', page = 1, limit = 10, sortOrder = 'DESC', isPaid = '' },
    btvnList = [], // Thay đổi từ btvn thành btvnList - mảng các BTVN
    month = null
) => {
    const offset = (page - 1) * limit;

    // Lấy thông tin lesson hiện tại để có lesson.day và classId
    const currentLesson = await Lesson.findByPk(lessonId, {
        attributes: ['id', 'day', 'classId', 'name'],
    });
    // console.log(lessonId)
    // console.log(currentLesson)

    if (!currentLesson) {
        throw new Error('Lesson not found');
    }

    // Tính toán tuần từ lesson.day
    const lessonDate = new Date(currentLesson.day);
    const dayOfWeek = lessonDate.getDay(); // 0 = Sunday, 1 = Monday, ...
    const startOfWeek = new Date(lessonDate);
    const endOfWeek = new Date(lessonDate);

    // Tính ngày đầu tuần (Monday)
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    startOfWeek.setDate(lessonDate.getDate() - daysToMonday);
    startOfWeek.setHours(0, 0, 0, 0);

    // Tính ngày cuối tuần (Sunday)
    const daysToSunday = dayOfWeek === 0 ? 0 : 7 - dayOfWeek;
    endOfWeek.setDate(lessonDate.getDate() + daysToSunday);
    endOfWeek.setHours(23, 59, 59, 999);

    // Điều kiện tìm kiếm cho bảng User (MySQL dùng Op.like)
    const whereUser = {
        userType: UserType.STUDENT,
        ...(search && {
            [Op.or]: [
                // Nếu là số thì so sánh với id
                !isNaN(search) && { id: Number(search) },

                // So sánh full name: "firstName lastName" LIKE %search%
                literal(`CONCAT(lastName, ' ', firstName) LIKE '%${search}%'`)
            ].filter(Boolean)
        })
    };

    // Điều kiện lọc bảng Attendance
    const whereAttendance = {
        lessonId
    };

    if (status && status !== 'all') {
        whereAttendance.status = status;
    }

    const date = new Date(currentLesson.day);
    const formattedMonth = month ? month : `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    const whereTuition = {
        month: formattedMonth
    };

    if (isPaid && isPaid !== 'all') {
        whereTuition.isPaid = isPaid === 'true';
    }

    // console.log('whereAttendance', whereAttendance, 'whereUser', whereUser, 'whereTuition', whereTuition);

    const { count, rows } = await Attendance.findAndCountAll({
        where: whereAttendance,
        include: [
            {
                model: User,
                as: 'user',
                where: whereUser,
                attributes: ['id', 'firstName', 'lastName', 'highSchool', 'class', 'phone', 'username', 'password', 'averageScore'],
                required: !!search,
                include: [
                    {
                        model: db.TuitionPayment,
                        as: 'tuitionPayments',
                        where: whereTuition,
                        required: isPaid && isPaid !== 'all', // không bắt buộc phải có học phí
                        attributes: ['isPaid']
                    }
                ]
            }
        ],
        order: [['createdAt', sortOrder]],
        limit,
        offset
    });

    // console.log(rows[4]);
    const LearningItems = await LearningItem.findAll({
        where: {
            id: {
                [Op.in]: btvnList
            }
        },
        include: [
            {
                model: db.Lesson,
                as: 'lesson',
                attributes: ['id', 'name']
            }
        ],
        attributes: ['id', 'name', 'url', 'typeOfLearningItem', 'deadline']
    });
    // Kiểm tra trạng thái làm BTVN cho danh sách BTVN
    const userIds = rows.map(row => row.userId);
    let allBtvnStatuses = {};

    if (LearningItems && Array.isArray(LearningItems) && LearningItems.length > 0) {
        // Xử lý từng learningItem trong danh sách BTVN
        for (const learningItem of LearningItems) {
            let btvnStatuses = {};
            let statusType = 'learningItem';

            // Kiểm tra nếu learningItem có type là 'BTVN' và url chứa examId
            const isBTVNWithExam = learningItem.typeOfLearningItem === 'BTVN' &&
                learningItem.url;
            if (isBTVNWithExam) {
                // Trích xuất examId từ URL
                const examId = learningItem.url;
                if (examId) {
                    // Kiểm tra trạng thái làm bài thi
                    const examAttempts = await StudentExamAttempt.findAll({
                        where: {
                            examId: parseInt(examId),
                            studentId: { [Op.in]: userIds }
                        },
                        attributes: ['studentId', 'score', 'endTime']
                    });

                    btvnStatuses = examAttempts.reduce((acc, attempt) => {
                        acc[attempt.studentId] = {
                            isDone: !!attempt.endTime, // Đã làm xong nếu có endTime
                            score: attempt.score,
                            type: 'exam',
                            examId: parseInt(examId)
                        };
                        return acc;
                    }, {});

                    statusType = 'exam';
                }
            }

            // Nếu không phải BTVN với examId hoặc không tìm thấy examId, kiểm tra StudentStudyStatus
            if (Object.keys(btvnStatuses).length === 0) {
                const studyStatuses = await StudentStudyStatus.findAll({
                    where: {
                        learningItemId: learningItem.id,
                        studentId: { [Op.in]: userIds }
                    },
                    attributes: ['studentId', 'isDone', 'studyTime']
                });

                btvnStatuses = studyStatuses.reduce((acc, status) => {
                    acc[status.studentId] = {
                        isDone: status.isDone,
                        studyTime: status.studyTime,
                        type: 'learningItem'
                    };
                    return acc;
                }, {});

                statusType = 'learningItem';
            }

            // Tạo key cho learningItem
            const btvnKey = `learningItem_${learningItem.id}`;

            // Lưu trạng thái cho từng user với từng learningItem
            userIds.forEach(userId => {
                if (!allBtvnStatuses[userId]) {
                    allBtvnStatuses[userId] = {};
                }

                const defaultStatus = statusType === 'exam' ?
                    {
                        isDone: false,
                        score: null,
                        type: 'exam',
                        examId: null
                    } :
                    {
                        isDone: false,
                        studyTime: 0,
                        type: 'learningItem'
                    };

                // Luôn thêm btvnInfo cho tất cả user
                const btvnInfo = {
                    id: learningItem.id,
                    name: learningItem.name || 'Unknown',
                    lesson: learningItem.lesson,
                    type: statusType,
                    isExamBased: isBTVNWithExam
                };

                // Nếu user đã có dữ liệu BTVN, merge với btvnInfo
                if (btvnStatuses[userId]) {
                    const status = btvnStatuses[userId];
                    allBtvnStatuses[userId][btvnKey] = {
                        ...status,
                        btvnInfo: btvnInfo,
                        feedback: status.type === 'exam' ? getBtvnFeedback(status.score) : (status.isDone ? 'Đã hoàn thành bài tập' : 'Chưa hoàn thành bài tập')
                    };
                } else {
                    // Nếu user chưa có dữ liệu, sử dụng defaultStatus
                    allBtvnStatuses[userId][btvnKey] = {
                        ...defaultStatus,
                        btvnInfo: btvnInfo,
                        feedback: defaultStatus.type === 'exam' ? getBtvnFeedback(defaultStatus.score) : 'Chưa hoàn thành bài tập'
                    };
                }
            });
        }
    }    // Lấy tất cả lessons trong cùng tuần
    const weeklyLessons = await Lesson.findAll({
        where: {
            day: {
                [Op.between]: [startOfWeek, endOfWeek]
            },
            id: {
                [Op.ne]: currentLesson.id
            }
        },
        attributes: ['id', 'name', 'day', 'chapter'],
        order: [['day', 'ASC']]
    });

    // Lấy tất cả attendances của các lessons trong tuần
    const weeklyAttendances = await Attendance.findAll({
        where: {
            status: 'present',
            lessonId: {
                [Op.in]: weeklyLessons.map(lesson => lesson.id)
            },
            userId: {
                [Op.in]: userIds
            }
        },
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName']
            },
            {
                model: Lesson,
                as: 'lesson',
                attributes: ['id', 'name', 'day', 'chapter'],
                include: [
                    {
                        model: Class,
                        as: 'class',
                        attributes: ['name']
                    }
                ]
            }
        ],
        order: [['lesson', 'day', 'ASC'], ['createdAt', 'ASC']]
    });

    // Thêm thông tin BTVN vào dữ liệu điểm danh
    const data = rows.map(row => {
        const attendanceData = new responseAttendance(row);

        // Thêm thông tin trạng thái BTVN cho tất cả BTVN trong danh sách
        if (LearningItems && Array.isArray(LearningItems) && LearningItems.length > 0) {
            attendanceData.btvnStatuses = allBtvnStatuses[row.userId] || {};

            // Thêm tổng kết trạng thái BTVN
            const userBtvnStatuses = allBtvnStatuses[row.userId] || {};
            const btvnKeys = Object.keys(userBtvnStatuses);

            attendanceData.btvnSummary = {
                total: btvnKeys.length,
                completed: btvnKeys.filter(key => userBtvnStatuses[key]?.isDone).length,
                pending: btvnKeys.filter(key => !userBtvnStatuses[key]?.isDone).length,
                completionRate: btvnKeys.length > 0 ?
                    Math.round((btvnKeys.filter(key => userBtvnStatuses[key]?.isDone).length / btvnKeys.length) * 100) : 0
            };
        }

        // Group các attendance theo userId để truy nhanh hơn
        const attendanceMapByUser = new Map();

        weeklyAttendances.forEach(att => {
            if (!attendanceMapByUser.has(att.userId)) {
                attendanceMapByUser.set(att.userId, []);
            }
            attendanceMapByUser.get(att.userId).push(att);
        });


        // Thêm các điểm danh khác trong tuần cho user này
        const userWeeklyAttendances = attendanceMapByUser.get(row.userId) || [];

        attendanceData.weeklyAttendances = userWeeklyAttendances.map(att => ({
            id: att.id,
            status: att.status,
            note: att.note,
            attendanceTime: att.attendanceTime,
            lesson: {
                id: att.lesson.id,
                name: att.lesson.name,
                day: att.lesson.day,
                chapter: att.lesson.chapter,
                class: att.lesson.class
            }
        }));


        return attendanceData;
    });

    return {
        ...new ResponseDataPagination(data, {
            page,
            pageSize: limit,
            total: count,
            totalPages: Math.ceil(count / limit),
        }),
        weekInfo: {
            startOfWeek: startOfWeek,
            endOfWeek: endOfWeek,
            currentLesson: {
                id: currentLesson.id,
                name: currentLesson.name,
                day: currentLesson.day,
                class: currentLesson.class
            },
            weeklyLessons: weeklyLessons
        },
        btvnInfo: LearningItems && Array.isArray(LearningItems) ? {
            total: LearningItems.length,
            list: LearningItems.map(learningItem => ({
                id: learningItem.id,
                type: 'learningItem',
                title: learningItem.name || learningItem.title,
                description: learningItem.description
            }))
        } : null
    };
};

export const createAttendance = async (
    userId,
    lessonId,
    classId = null,
    status = AttendanceStatus.ABSENT,
    note = '',
    transaction = null
) => {
    // Kiểm tra đã tồn tại chưa
    const attendance = await Attendance.findOne({
        where: {
            userId,
            lessonId
        },
        transaction
    });

    if (attendance) {
        throw new Error('Bản ghi điểm danh đã tồn tại.');
    }

    // Kiểm tra hợp lệ status
    const validStatuses = ['present', 'absent', 'late'];
    if (!validStatuses.includes(status)) {
        throw new Error(`Status không hợp lệ. Chỉ chấp nhận: ${validStatuses.join(', ')}`);
    }

    let classStatus = null;

    if (classId) {
        classStatus = await classService.getStatusByUserIdAndClassId(userId, classId, transaction);
    }

    // Tạo bản ghi mới
    const at = await Attendance.create({
        userId,
        lessonId,
        status,
        note: note ?? null,
        attendanceTime: new Date(),
        assignmentFeedback: FEEDBACK.CLASSWORK.COMPLETE_DEFAULT
    }, { transaction });

    return {
        ...at.toJSON(),
        classStatus: classStatus || null
    };
};



export const createAllAttendanceInClass = async (classId, lessonId) => {
    const userIds = await getAllStudentInClasses(classId);
    if (!userIds.length) return [];

    // Kiểm tra những bản ghi đã tồn tại
    const existingAttendances = await Attendance.findAll({
        where: {
            lessonId,
            userId: {
                [Op.in]: userIds
            }
        },
        attributes: ['userId']
    });

    // Lấy danh sách userId đã có attendance
    const existingUserIds = existingAttendances.map(attendance => attendance.userId);

    // Lọc ra những userId chưa có attendance
    const newUserIds = userIds.filter(userId => !existingUserIds.includes(userId));

    // Nếu không có user nào cần tạo attendance mới
    if (!newUserIds.length) {
        return [];
    }

    // Tạo attendance records cho những user chưa có
    const attendanceRecords = newUserIds.map(userId => ({
        userId,
        lessonId,
        status: AttendanceStatus.ABSENT,
        attendanceTime: new Date(),
        assignmentFeedback: FEEDBACK.CLASSWORK.COMPLETE_DEFAULT,
    }));

    const created = await Attendance.bulkCreate(attendanceRecords);

    return created;
};

export const updateAttendance = async (id, updateData) => {
    const attendance = await Attendance.findByPk(id, {
        include: [{ model: User, as: 'user', attributes: ['id', 'firstName', 'lastName', 'highSchool', 'class'] }]
    });

    if (!attendance) {
        throw new Error(`Điểm danh với id ${id} không tồn tại.`);
    }

    if (
        updateData.status &&
        ['present', 'absent', 'late'].includes(updateData.status) &&
        updateData.status !== attendance.status
    ) {
        attendance.status = updateData.status;
        attendance.attendanceTime = new Date();
    }

    if (typeof updateData.assignmentFeedback === 'string') {
        attendance.assignmentFeedback = updateData.assignmentFeedback;
    }

    if (typeof updateData.note === 'string') {
        attendance.note = updateData.note;
    }

    if (typeof updateData.hasMessagedParent === 'boolean') {
        attendance.hasMessagedParent = updateData.hasMessagedParent;
    }

    await attendance.save();

    return new responseAttendance(attendance);
};

export const deleteAttendance = async (attendanceId) => {
    const attendance = await Attendance.findByPk(attendanceId);
    if (!attendance) {
        throw new Error('Không tồn tại bản ghi điểm danh.');
    }

    await Attendance.destroy({ where: { id: attendanceId } });

    return attendanceId;
};

export const getUserAttendances = async (userId, { year, month } = {}) => {
    // Build where conditions for filtering by date
    const whereConditions = {
        userId: userId
    };

    // If year and month are provided, filter by specific month
    if (year && month) {
        const startDate = new Date(year, month - 1, 1); // month is 0-indexed
        const endDate = new Date(year, month, 0, 23, 59, 59); // last day of month

        whereConditions.createdAt = {
            [Op.between]: [startDate, endDate]
        };
    }

    const attendances = await Attendance.findAll({
        where: whereConditions,
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName']
            },
            {
                model: Lesson,
                as: 'lesson',
                attributes: ['id', 'name', 'description', 'createdAt', 'day'],
                include: [
                    {
                        model: Class,
                        as: 'class',
                        attributes: ['id', 'name', 'grade', 'startTime1', 'endTime1', 'startTime2', 'endTime2', 'dayOfWeek1', 'dayOfWeek2']
                    }
                ]
            }
        ],
        order: [['createdAt', 'DESC']]
    });

    // Group attendances by month
    const groupedByMonth = {};

    attendances.forEach(attendance => {
        const date = new Date(attendance.createdAt);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

        if (!groupedByMonth[monthKey]) {
            groupedByMonth[monthKey] = {
                month: monthKey,
                year: date.getFullYear(),
                monthNumber: date.getMonth() + 1,
                attendances: [],
                summary: {
                    total: 0,
                    present: 0,
                    absent: 0,
                    late: 0
                }
            };
        }

        const attendanceData = {
            id: attendance.id,
            status: attendance.status,
            note: attendance.note,
            attendanceTime: attendance.attendanceTime,
            createdAt: attendance.createdAt,
            lesson: {
                id: attendance.lesson.id,
                name: attendance.lesson.name,
                description: attendance.lesson.description,
                createdAt: attendance.lesson.createdAt,
                day: attendance.lesson.day,
                class: {
                    id: attendance.lesson.class.id,
                    name: attendance.lesson.class.name,
                    grade: attendance.lesson.class.grade,
                    startTime1: attendance.lesson.class.startTime1,
                    endTime1: attendance.lesson.class.endTime1,
                    startTime2: attendance.lesson.class.startTime2,
                    endTime2: attendance.lesson.class.endTime2,
                    dayOfWeek1: attendance.lesson.class.dayOfWeek1,
                    dayOfWeek2: attendance.lesson.class.dayOfWeek2
                }
            }
        };

        groupedByMonth[monthKey].attendances.push(attendanceData);
        groupedByMonth[monthKey].summary.total++;
        groupedByMonth[monthKey].summary[attendance.status]++;
    });

    // Convert to array and sort by month (newest first)
    const result = Object.values(groupedByMonth).sort((a, b) => {
        return new Date(b.year, b.monthNumber - 1) - new Date(a.year, a.monthNumber - 1);
    });

    return {
        userId: userId,
        totalAttendances: attendances.length,
        monthlyData: result
    };
};

/**
 * Thống kê điểm danh cho một buổi học cụ thể
 * @param {number} lessonId - ID của buổi học
 * @returns {Object} Thống kê điểm danh
 */
export const getLessonAttendanceStatistics = async (lessonId) => {
    // Kiểm tra buổi học có tồn tại không
    const lesson = await Lesson.findByPk(lessonId, {
        include: [
            {
                model: Class,
                as: 'class',
                attributes: ['id', 'name', 'grade', 'academicYear']
            }
        ]
    });

    if (!lesson) {
        throw new Error('Buổi học không tồn tại');
    }

    // Lấy tất cả điểm danh của buổi học này
    const attendances = await Attendance.findAll({
        where: { lessonId },
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName', 'class', 'phone']
            }
        ],
        order: [['status', 'ASC'], ['user', 'lastName', 'ASC']]
    });

    // Thống kê theo trạng thái
    const statistics = {
        total: attendances.length,
        present: 0,
        absent: 0,
        late: 0,
        presentPercentage: 0,
        absentPercentage: 0,
        latePercentage: 0
    };

    const detailsByStatus = {
        present: [],
        absent: [],
        late: []
    };

    // Đếm và phân loại
    attendances.forEach(attendance => {
        statistics[attendance.status]++;

        const studentInfo = {
            id: attendance.user.id,
            fullName: `${attendance.user.lastName} ${attendance.user.firstName}`,
            class: attendance.user.class,
            phone: attendance.user.phone,
            attendanceTime: attendance.attendanceTime,
            note: attendance.note
        };

        detailsByStatus[attendance.status].push(studentInfo);
    });

    // Tính phần trăm
    if (statistics.total > 0) {
        statistics.presentPercentage = Math.round((statistics.present / statistics.total) * 100);
        statistics.absentPercentage = Math.round((statistics.absent / statistics.total) * 100);
        statistics.latePercentage = Math.round((statistics.late / statistics.total) * 100);
    }

    return {
        lesson: {
            id: lesson.id,
            name: lesson.name,
            description: lesson.description,
            day: lesson.day,
            chapter: lesson.chapter,
            class: lesson.class
        },
        statistics,
        details: detailsByStatus
    };
};

/**
 * Thống kê điểm danh cho một lớp học
 * @param {number} classId - ID của lớp học
 * @param {Object} options - Tùy chọn lọc (startDate, endDate)
 * @returns {Object} Thống kê điểm danh
 */
export const getClassAttendanceStatistics = async (classId, { startDate, endDate } = {}) => {
    // Kiểm tra lớp học có tồn tại không
    const classInfo = await Class.findByPk(classId);
    if (!classInfo) {
        throw new Error('Lớp học không tồn tại');
    }

    // Điều kiện lọc theo ngày
    const lessonWhere = { classId };
    if (startDate && endDate) {
        lessonWhere.day = {
            [Op.between]: [new Date(startDate), new Date(endDate)]
        };
    }

    // Lấy tất cả buổi học của lớp
    const lessons = await Lesson.findAll({
        where: lessonWhere,
        include: [
            {
                model: Attendance,
                as: 'attendances',
                include: [
                    {
                        model: User,
                        as: 'user',
                        attributes: ['id', 'firstName', 'lastName', 'class']
                    }
                ]
            }
        ],
        order: [['day', 'DESC']]
    });

    // Thống kê tổng quan
    const overallStats = {
        totalLessons: lessons.length,
        totalAttendances: 0,
        present: 0,
        absent: 0,
        late: 0,
        presentPercentage: 0,
        absentPercentage: 0,
        latePercentage: 0
    };

    // Thống kê theo từng buổi học
    const lessonStats = [];

    lessons.forEach(lesson => {
        const lessonStat = {
            lessonId: lesson.id,
            lessonName: lesson.name,
            lessonDay: lesson.day,
            chapter: lesson.chapter,
            total: lesson.attendances.length,
            present: 0,
            absent: 0,
            late: 0
        };

        lesson.attendances.forEach(attendance => {
            overallStats.totalAttendances++;
            overallStats[attendance.status]++;
            lessonStat[attendance.status]++;
        });

        // Tính phần trăm cho buổi học
        if (lessonStat.total > 0) {
            lessonStat.presentPercentage = Math.round((lessonStat.present / lessonStat.total) * 100);
            lessonStat.absentPercentage = Math.round((lessonStat.absent / lessonStat.total) * 100);
            lessonStat.latePercentage = Math.round((lessonStat.late / lessonStat.total) * 100);
        }

        lessonStats.push(lessonStat);
    });

    // Tính phần trăm tổng quan
    if (overallStats.totalAttendances > 0) {
        overallStats.presentPercentage = Math.round((overallStats.present / overallStats.totalAttendances) * 100);
        overallStats.absentPercentage = Math.round((overallStats.absent / overallStats.totalAttendances) * 100);
        overallStats.latePercentage = Math.round((overallStats.late / overallStats.totalAttendances) * 100);
    }

    return {
        class: {
            id: classInfo.id,
            name: classInfo.name,
            grade: classInfo.grade,
            academicYear: classInfo.academicYear
        },
        dateRange: {
            startDate: startDate || null,
            endDate: endDate || null
        },
        overallStatistics: overallStats,
        lessonStatistics: lessonStats
    };
};

/**
 * Thống kê điểm danh tổng quan
 * @param {Object} options - Tùy chọn lọc (startDate, endDate, classId)
 * @returns {Object} Thống kê điểm danh tổng quan
 */
export const getOverallAttendanceStatistics = async ({ startDate, endDate, classId } = {}) => {
    // Điều kiện lọc
    const whereConditions = {};

    // Lọc theo ngày nếu có
    if (startDate && endDate) {
        whereConditions.createdAt = {
            [Op.between]: [new Date(startDate), new Date(endDate)]
        };
    }

    // Điều kiện cho lesson nếu lọc theo lớp
    const lessonWhere = {};
    if (classId) {
        lessonWhere.classId = classId;
    }

    // Lấy tất cả điểm danh theo điều kiện
    const attendances = await Attendance.findAll({
        where: whereConditions,
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName', 'class']
            },
            {
                model: Lesson,
                as: 'lesson',
                where: lessonWhere,
                attributes: ['id', 'name', 'day', 'chapter'],
                include: [
                    {
                        model: Class,
                        as: 'class',
                        attributes: ['id', 'name', 'grade', 'academicYear']
                    }
                ]
            }
        ],
        order: [['createdAt', 'DESC']]
    });

    // Thống kê tổng quan
    const overallStats = {
        totalAttendances: attendances.length,
        present: 0,
        absent: 0,
        late: 0,
        presentPercentage: 0,
        absentPercentage: 0,
        latePercentage: 0
    };

    // Thống kê theo lớp học
    const classSummary = {};

    // Thống kê theo tháng
    const monthlySummary = {};

    // Thống kê theo trạng thái
    const statusDetails = {
        present: [],
        absent: [],
        late: []
    };

    attendances.forEach(attendance => {
        // Đếm tổng quan
        overallStats[attendance.status]++;

        // Thống kê theo lớp
        const classKey = `${attendance.lesson.class.id}-${attendance.lesson.class.name}`;
        if (!classSummary[classKey]) {
            classSummary[classKey] = {
                classId: attendance.lesson.class.id,
                className: attendance.lesson.class.name,
                grade: attendance.lesson.class.grade,
                academicYear: attendance.lesson.class.academicYear,
                total: 0,
                present: 0,
                absent: 0,
                late: 0
            };
        }
        classSummary[classKey].total++;
        classSummary[classKey][attendance.status]++;

        // Thống kê theo tháng
        const date = new Date(attendance.createdAt);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        if (!monthlySummary[monthKey]) {
            monthlySummary[monthKey] = {
                month: monthKey,
                year: date.getFullYear(),
                monthNumber: date.getMonth() + 1,
                total: 0,
                present: 0,
                absent: 0,
                late: 0
            };
        }
        monthlySummary[monthKey].total++;
        monthlySummary[monthKey][attendance.status]++;

        // Chi tiết theo trạng thái
        const studentInfo = {
            id: attendance.user.id,
            fullName: `${attendance.user.lastName} ${attendance.user.firstName}`,
            class: attendance.user.class,
            attendanceTime: attendance.attendanceTime,
            lesson: {
                id: attendance.lesson.id,
                name: attendance.lesson.name,
                day: attendance.lesson.day,
                chapter: attendance.lesson.chapter,
                className: attendance.lesson.class.name
            }
        };
        statusDetails[attendance.status].push(studentInfo);
    });

    // Tính phần trăm tổng quan
    if (overallStats.totalAttendances > 0) {
        overallStats.presentPercentage = Math.round((overallStats.present / overallStats.totalAttendances) * 100);
        overallStats.absentPercentage = Math.round((overallStats.absent / overallStats.totalAttendances) * 100);
        overallStats.latePercentage = Math.round((overallStats.late / overallStats.totalAttendances) * 100);
    }

    // Tính phần trăm cho từng lớp
    Object.values(classSummary).forEach(classData => {
        if (classData.total > 0) {
            classData.presentPercentage = Math.round((classData.present / classData.total) * 100);
            classData.absentPercentage = Math.round((classData.absent / classData.total) * 100);
            classData.latePercentage = Math.round((classData.late / classData.total) * 100);
        }
    });

    // Tính phần trăm cho từng tháng
    Object.values(monthlySummary).forEach(monthData => {
        if (monthData.total > 0) {
            monthData.presentPercentage = Math.round((monthData.present / monthData.total) * 100);
            monthData.absentPercentage = Math.round((monthData.absent / monthData.total) * 100);
            monthData.latePercentage = Math.round((monthData.late / monthData.total) * 100);
        }
    });

    // Sắp xếp dữ liệu
    const classStats = Object.values(classSummary).sort((a, b) => b.total - a.total);
    const monthlyStats = Object.values(monthlySummary).sort((a, b) => {
        return new Date(b.year, b.monthNumber - 1) - new Date(a.year, a.monthNumber - 1);
    });

    return {
        dateRange: {
            startDate: startDate || null,
            endDate: endDate || null,
            classId: classId || null
        },
        overallStatistics: overallStats,
        classSummary: classStats,
        monthlySummary: monthlyStats,
        statusDetails
    };
};

/**
 * Cập nhật hàng loạt trạng thái điểm danh cho nhiều học sinh trong một buổi học
 * @param {number} lessonId - ID của buổi học
 * @param {Array} updates - Mảng các object { userId, status, note? }
 * @returns {Object} Kết quả cập nhật
 */
export const bulkUpdateAttendanceStatus = async (lessonId, updates) => {
    // Validate input
    if (!lessonId) {
        throw new Error('Lesson ID is required');
    }

    if (!updates || !Array.isArray(updates) || updates.length === 0) {
        throw new Error('Updates array is required and must not be empty');
    }

    // Validate each update object
    const validStatuses = ['present', 'absent', 'late'];
    for (const update of updates) {
        if (!update.userId || !update.status) {
            throw new Error('Each update must have userId and status');
        }

        if (!validStatuses.includes(update.status)) {
            throw new Error(`Invalid status: ${update.status}. Must be one of: ${validStatuses.join(', ')}`);
        }
    }

    // Check if lesson exists
    const lesson = await Lesson.findByPk(lessonId);
    if (!lesson) {
        throw new Error('Lesson not found');
    }

    // Tìm tất cả attendance có lessonId
    const attendances = await Attendance.findAll({
        where: { lessonId },
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName']
            }
        ]
    });

    const results = [];
    const errors = [];

    // Update từng attendance theo updates
    for (const update of updates) {
        try {
            const { userId, status, note = '' } = update;

            // Tìm attendance của user này
            const attendance = attendances.find(att => att.userId === userId);

            if (attendance) {
                // Update attendance
                await attendance.update({
                    status,
                    note,
                    attendanceTime: new Date(),
                    updatedAt: new Date()
                });

                results.push({
                    userId,
                    userName: `${attendance.user.lastName} ${attendance.user.firstName}`,
                    action: 'updated',
                    status,
                    note,
                    attendanceId: attendance.id
                });
            } else {
                errors.push({
                    userId,
                    userName: 'Unknown',
                    error: 'Attendance record not found for this user'
                });
            }
        } catch (error) {
            errors.push({
                userId: update.userId,
                userName: 'Unknown',
                error: error.message
            });
        }
    }

    // Get updated attendances for response
    const updatedAttendances = await Attendance.findAll({
        where: { lessonId },
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName', 'class', 'phone']
            }
        ],
        order: [['status', 'ASC'], ['user', 'lastName', 'ASC']]
    });

    return {
        lessonId,
        results,
        errors,
        totalProcessed: updates.length,
        successCount: results.length,
        errorCount: errors.length,
        attendances: updatedAttendances.map(att => new responseAttendance(att))
    };
};

/**
 * Đánh dấu tất cả học sinh trong buổi học với cùng một trạng thái
 * @param {number} lessonId - ID của buổi học
 * @param {string} status - Trạng thái điểm danh (present, absent, late)
 * @param {string} note - Ghi chú (optional)
 * @returns {Object} Kết quả cập nhật
 */
export const markAllAttendanceStatus = async (lessonId, status, note = '') => {
    // Validate input
    if (!lessonId) {
        throw new Error('Lesson ID is required');
    }

    if (!status) {
        throw new Error('Status is required');
    }

    const validStatuses = ['present', 'absent', 'late'];
    if (!validStatuses.includes(status)) {
        throw new Error(`Invalid status: ${status}. Must be one of: ${validStatuses.join(', ')}`);
    }

    // Check if lesson exists
    const lesson = await Lesson.findByPk(lessonId);
    if (!lesson) {
        throw new Error('Lesson not found');
    }

    // Tìm tất cả attendance có lessonId và update hàng loạt
    const attendances = await Attendance.findAll({
        where: { lessonId },
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName']
            }
        ]
    });

    if (attendances.length === 0) {
        throw new Error('No attendance records found for this lesson');
    }

    const results = [];
    const errors = [];

    // Update tất cả attendance
    for (const attendance of attendances) {
        try {
            await attendance.update({
                status,
                note,
                attendanceTime: new Date(),
                updatedAt: new Date()
            });

            results.push({
                userId: attendance.userId,
                userName: `${attendance.user.lastName} ${attendance.user.firstName}`,
                action: 'updated',
                status,
                note,
                attendanceId: attendance.id
            });
        } catch (error) {
            errors.push({
                userId: attendance.userId,
                userName: `${attendance.user.lastName} ${attendance.user.firstName}`,
                error: error.message
            });
        }
    }

    // Get updated attendances for response
    const updatedAttendances = await Attendance.findAll({
        where: { lessonId },
        include: [
            {
                model: User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName', 'class', 'phone']
            }
        ],
        order: [['status', 'ASC'], ['user', 'lastName', 'ASC']]
    });

    return {
        lessonId,
        status,
        note,
        lesson: {
            id: lesson.id,
            name: lesson.name,
            day: lesson.day
        },
        results,
        errors,
        totalAttendances: attendances.length,
        successCount: results.length,
        errorCount: errors.length,
        attendances: updatedAttendances.map(att => new responseAttendance(att))
    };
};
